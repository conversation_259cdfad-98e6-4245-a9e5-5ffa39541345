"""
Data Provider Manager

Manages multiple external data providers for enhanced market data,
news, sentiment analysis, and additional token information.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTTPException
import httpx

from src.shared.types import TokenData, PriceData
from src.features.data_pipeline.cache_manager import CacheManager
from src.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class DataProvider(Enum):
    """Supported data providers"""
    COINGECKO = "coingecko"
    COINMARKETCAP = "coinmarketcap"
    MESSARI = "messari"
    DEFIPULSE = "defipulse"
    DEXSCREENER = "dexscreener"
    BIRDEYE = "birdeye"


@dataclass
class ProviderConfig:
    """Data provider configuration"""
    provider: DataProvider
    api_key: Optional[str] = None
    base_url: str = ""
    rate_limit: int = 10  # requests per second
    timeout: int = 30
    enabled: bool = True


@dataclass
class TokenMetadata:
    """Enhanced token metadata"""
    address: str
    symbol: str
    name: str
    description: Optional[str] = None
    website: Optional[str] = None
    twitter: Optional[str] = None
    telegram: Optional[str] = None
    discord: Optional[str] = None
    logo_url: Optional[str] = None
    market_cap: Optional[float] = None
    total_supply: Optional[float] = None
    circulating_supply: Optional[float] = None
    volume_24h: Optional[float] = None
    price_change_24h: Optional[float] = None
    all_time_high: Optional[float] = None
    all_time_low: Optional[float] = None
    categories: List[str] = None
    tags: List[str] = None


@dataclass
class MarketSentiment:
    """Market sentiment data"""
    token_address: str
    sentiment_score: float  # -1 to 1
    confidence: float  # 0 to 1
    sources: List[str]
    positive_mentions: int
    negative_mentions: int
    neutral_mentions: int
    trending_score: float
    social_volume: int
    timestamp: datetime


@dataclass
class NewsArticle:
    """News article data"""
    title: str
    content: str
    url: str
    source: str
    author: Optional[str] = None
    published_at: datetime
    sentiment: Optional[float] = None  # -1 to 1
    relevance_score: Optional[float] = None  # 0 to 1
    tokens_mentioned: List[str] = None
    categories: List[str] = None


class DataProviderManager:
    """
    Manages multiple external data providers for enhanced
    market data, news, and sentiment analysis.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        provider_configs: Dict[DataProvider, ProviderConfig]
    ):
        self.cache_manager = cache_manager
        self.provider_configs = provider_configs
        self.logger = logger.bind(service="data_provider_manager")
        self.http_clients = {}
        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize HTTP clients for each provider"""
        for provider in self.provider_configs:
            if self.provider_configs[provider].enabled:
                self.http_clients[provider] = httpx.AsyncClient(
                    timeout=self.provider_configs[provider].timeout,
                    limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
                )
    
    async def get_enhanced_token_data(
        self,
        token_address: str,
        providers: Optional[List[DataProvider]] = None
    ) -> TokenMetadata:
        """
        Get enhanced token metadata from multiple providers.
        
        Args:
            token_address: Token contract address
            providers: List of providers to query (if None, query all enabled)
            
        Returns:
            Enhanced token metadata
        """
        try:
            if providers is None:
                providers = [p for p in self.provider_configs if self.provider_configs[p].enabled]
            
            # Check cache first
            cache_key = f"token_metadata:{token_address}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Token metadata served from cache", token_address=token_address)
                return TokenMetadata(**cached_data)
            
            # Query providers concurrently
            tasks = []
            for provider in providers:
                if provider in self.http_clients:
                    task = self._get_token_data_from_provider(token_address, provider)
                    tasks.append((provider, task))
            
            # Collect results
            provider_data = {}
            for provider, task in tasks:
                try:
                    data = await task
                    if data:
                        provider_data[provider] = data
                except Exception as e:
                    self.logger.error(
                        "Failed to get data from provider",
                        provider=provider.value,
                        token_address=token_address,
                        error=str(e)
                    )
            
            # Merge data from all providers
            merged_metadata = self._merge_token_metadata(token_address, provider_data)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                merged_metadata.__dict__,
                ttl=3600  # Cache for 1 hour
            )
            
            self.logger.info(
                "Enhanced token data retrieved",
                token_address=token_address,
                providers=len(provider_data)
            )
            
            return merged_metadata
            
        except Exception as e:
            self.logger.error("Failed to get enhanced token data", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get enhanced token data")
    
    async def get_market_sentiment(
        self,
        token_address: str,
        timeframe: str = "24h"
    ) -> MarketSentiment:
        """
        Get market sentiment for a token.
        
        Args:
            token_address: Token contract address
            timeframe: Sentiment timeframe (1h, 24h, 7d)
            
        Returns:
            Market sentiment data
        """
        try:
            cache_key = f"market_sentiment:{token_address}:{timeframe}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return MarketSentiment(**cached_data)
            
            # Aggregate sentiment from multiple sources
            sentiment_data = await self._aggregate_sentiment_data(token_address, timeframe)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                sentiment_data.__dict__,
                ttl=1800  # Cache for 30 minutes
            )
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error("Failed to get market sentiment", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get market sentiment")
    
    async def get_related_news(
        self,
        token_address: str,
        limit: int = 20,
        hours_back: int = 24
    ) -> List[NewsArticle]:
        """
        Get news articles related to a token.
        
        Args:
            token_address: Token contract address
            limit: Maximum number of articles
            hours_back: How many hours back to search
            
        Returns:
            List of related news articles
        """
        try:
            cache_key = f"token_news:{token_address}:{limit}:{hours_back}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return [NewsArticle(**article) for article in cached_data]
            
            # Get news from multiple sources
            news_articles = await self._aggregate_news_data(token_address, limit, hours_back)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                [article.__dict__ for article in news_articles],
                ttl=1800  # Cache for 30 minutes
            )
            
            self.logger.info(
                "Related news retrieved",
                token_address=token_address,
                articles=len(news_articles)
            )
            
            return news_articles
            
        except Exception as e:
            self.logger.error("Failed to get related news", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get related news")
    
    async def get_trending_tokens(
        self,
        limit: int = 50,
        timeframe: str = "24h"
    ) -> List[Dict[str, Any]]:
        """
        Get trending tokens across platforms.
        
        Args:
            limit: Maximum number of tokens
            timeframe: Trending timeframe
            
        Returns:
            List of trending token data
        """
        try:
            cache_key = f"trending_tokens:{limit}:{timeframe}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return cached_data
            
            # Get trending data from multiple providers
            trending_data = await self._aggregate_trending_data(limit, timeframe)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                trending_data,
                ttl=900  # Cache for 15 minutes
            )
            
            self.logger.info("Trending tokens retrieved", count=len(trending_data))
            return trending_data
            
        except Exception as e:
            self.logger.error("Failed to get trending tokens", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get trending tokens")
    
    async def _get_token_data_from_provider(
        self,
        token_address: str,
        provider: DataProvider
    ) -> Optional[Dict[str, Any]]:
        """Get token data from a specific provider"""
        try:
            if provider == DataProvider.COINGECKO:
                return await self._get_coingecko_token_data(token_address)
            elif provider == DataProvider.COINMARKETCAP:
                return await self._get_coinmarketcap_token_data(token_address)
            elif provider == DataProvider.DEXSCREENER:
                return await self._get_dexscreener_token_data(token_address)
            else:
                # Mock implementation for other providers
                return await self._get_mock_token_data(token_address, provider)
                
        except Exception as e:
            self.logger.error(f"Failed to get data from {provider.value}", error=str(e))
            return None
    
    async def _get_coingecko_token_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Get token data from CoinGecko"""
        try:
            client = self.http_clients[DataProvider.COINGECKO]
            
            # First, try to find the token by contract address
            url = f"https://api.coingecko.com/api/v3/coins/ethereum/contract/{token_address}"
            response = await client.get(url)
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "symbol": data.get("symbol", "").upper(),
                    "name": data.get("name", ""),
                    "description": data.get("description", {}).get("en", ""),
                    "website": data.get("links", {}).get("homepage", [None])[0],
                    "twitter": data.get("links", {}).get("twitter_screen_name"),
                    "logo_url": data.get("image", {}).get("large"),
                    "market_cap": data.get("market_data", {}).get("market_cap", {}).get("usd"),
                    "total_supply": data.get("market_data", {}).get("total_supply"),
                    "circulating_supply": data.get("market_data", {}).get("circulating_supply"),
                    "volume_24h": data.get("market_data", {}).get("total_volume", {}).get("usd"),
                    "price_change_24h": data.get("market_data", {}).get("price_change_percentage_24h"),
                    "all_time_high": data.get("market_data", {}).get("ath", {}).get("usd"),
                    "all_time_low": data.get("market_data", {}).get("atl", {}).get("usd"),
                    "categories": data.get("categories", [])
                }
            else:
                return None
                
        except Exception as e:
            self.logger.error("Failed to get CoinGecko data", error=str(e))
            return None
    
    async def _get_coinmarketcap_token_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Get token data from CoinMarketCap"""
        try:
            # Mock implementation - would require actual CMC API integration
            return {
                "symbol": "TOKEN",
                "name": "Example Token",
                "market_cap": 1000000.0,
                "volume_24h": 50000.0,
                "price_change_24h": 5.2
            }
            
        except Exception as e:
            self.logger.error("Failed to get CoinMarketCap data", error=str(e))
            return None
    
    async def _get_dexscreener_token_data(self, token_address: str) -> Optional[Dict[str, Any]]:
        """Get token data from DexScreener"""
        try:
            client = self.http_clients[DataProvider.DEXSCREENER]
            
            url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
            response = await client.get(url)
            
            if response.status_code == 200:
                data = response.json()
                pairs = data.get("pairs", [])
                
                if pairs:
                    pair = pairs[0]  # Use first pair
                    return {
                        "symbol": pair.get("baseToken", {}).get("symbol", ""),
                        "name": pair.get("baseToken", {}).get("name", ""),
                        "volume_24h": float(pair.get("volume", {}).get("h24", 0)),
                        "price_change_24h": float(pair.get("priceChange", {}).get("h24", 0))
                    }
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get DexScreener data", error=str(e))
            return None
    
    async def _get_mock_token_data(
        self,
        token_address: str,
        provider: DataProvider
    ) -> Dict[str, Any]:
        """Get mock token data for demonstration"""
        return {
            "symbol": "MOCK",
            "name": f"Mock Token ({provider.value})",
            "description": f"Mock token data from {provider.value}",
            "market_cap": 500000.0,
            "volume_24h": 25000.0,
            "price_change_24h": 2.5
        }
    
    def _merge_token_metadata(
        self,
        token_address: str,
        provider_data: Dict[DataProvider, Dict[str, Any]]
    ) -> TokenMetadata:
        """Merge token metadata from multiple providers"""
        merged = TokenMetadata(address=token_address, symbol="", name="")
        
        # Priority order for data sources
        priority_order = [
            DataProvider.COINGECKO,
            DataProvider.COINMARKETCAP,
            DataProvider.DEXSCREENER,
            DataProvider.MESSARI
        ]
        
        # Merge data with priority
        for provider in priority_order:
            if provider in provider_data:
                data = provider_data[provider]
                
                # Use first non-empty value for each field
                if not merged.symbol and data.get("symbol"):
                    merged.symbol = data["symbol"]
                if not merged.name and data.get("name"):
                    merged.name = data["name"]
                if not merged.description and data.get("description"):
                    merged.description = data["description"]
                if not merged.website and data.get("website"):
                    merged.website = data["website"]
                if not merged.twitter and data.get("twitter"):
                    merged.twitter = data["twitter"]
                if not merged.logo_url and data.get("logo_url"):
                    merged.logo_url = data["logo_url"]
                
                # Use highest values for numerical fields
                if data.get("market_cap") and (not merged.market_cap or data["market_cap"] > merged.market_cap):
                    merged.market_cap = data["market_cap"]
                if data.get("volume_24h") and (not merged.volume_24h or data["volume_24h"] > merged.volume_24h):
                    merged.volume_24h = data["volume_24h"]
                
                # Merge categories and tags
                if data.get("categories"):
                    if not merged.categories:
                        merged.categories = []
                    merged.categories.extend(data["categories"])
        
        # Remove duplicates from lists
        if merged.categories:
            merged.categories = list(set(merged.categories))
        
        return merged
    
    async def _aggregate_sentiment_data(
        self,
        token_address: str,
        timeframe: str
    ) -> MarketSentiment:
        """Aggregate sentiment data from multiple sources"""
        # Mock implementation - would integrate with sentiment analysis APIs
        return MarketSentiment(
            token_address=token_address,
            sentiment_score=0.3,  # Slightly positive
            confidence=0.7,
            sources=["twitter", "reddit", "telegram"],
            positive_mentions=150,
            negative_mentions=80,
            neutral_mentions=200,
            trending_score=0.6,
            social_volume=430,
            timestamp=datetime.utcnow()
        )
    
    async def _aggregate_news_data(
        self,
        token_address: str,
        limit: int,
        hours_back: int
    ) -> List[NewsArticle]:
        """Aggregate news data from multiple sources"""
        # Mock implementation - would integrate with news APIs
        articles = []
        
        for i in range(min(limit, 5)):  # Mock 5 articles
            article = NewsArticle(
                title=f"Breaking: Token Analysis Update #{i+1}",
                content=f"Latest analysis and market updates for token {token_address}...",
                url=f"https://example.com/news/{i+1}",
                source="CryptoNews",
                published_at=datetime.utcnow() - timedelta(hours=i*2),
                sentiment=0.2 + (i * 0.1),  # Varying sentiment
                relevance_score=0.8 - (i * 0.1),
                tokens_mentioned=[token_address],
                categories=["market-analysis", "defi"]
            )
            articles.append(article)
        
        return articles
    
    async def _aggregate_trending_data(
        self,
        limit: int,
        timeframe: str
    ) -> List[Dict[str, Any]]:
        """Aggregate trending data from multiple providers"""
        # Mock implementation - would integrate with trending APIs
        trending = []
        
        for i in range(min(limit, 10)):  # Mock 10 trending tokens
            trending.append({
                "rank": i + 1,
                "token_address": f"0x{'a' * 40}",  # Mock address
                "symbol": f"TREND{i+1}",
                "name": f"Trending Token {i+1}",
                "price_change_24h": 10.0 + (i * 2),
                "volume_24h": 1000000 - (i * 50000),
                "trending_score": 1.0 - (i * 0.1),
                "mentions": 1000 - (i * 50)
            })
        
        return trending
    
    async def cleanup(self) -> None:
        """Cleanup HTTP clients"""
        try:
            for client in self.http_clients.values():
                await client.aclose()
            self.logger.info("Data provider manager cleanup completed")
        except Exception as e:
            self.logger.error("Failed to cleanup data provider manager", error=str(e))
