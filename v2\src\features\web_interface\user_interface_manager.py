"""
User Interface Manager

Manages user profiles, subscription management, notification preferences,
and API access management for the web interface.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import structlog
from fastapi import HTTPException

from src.shared.types import (
    UserProfile, SubscriptionData, NotificationPreferences,
    APIAccessData, UserInterfaceData
)
from src.features.security.user_manager import UserManager
from src.features.notifications.subscription_manager import SubscriptionManager
from src.features.security.auth_manager import AuthManager
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


@dataclass
class UIConfig:
    """User interface configuration settings"""
    session_timeout: int = 3600  # seconds
    max_api_keys: int = 5
    cache_ttl: int = 300  # seconds
    profile_update_cooldown: int = 60  # seconds


class UserInterfaceManager:
    """
    Manages user interface components including profiles,
    subscriptions, preferences, and API access.
    """
    
    def __init__(
        self,
        user_manager: User<PERSON>anager,
        subscription_manager: SubscriptionManager,
        auth_manager: AuthManager,
        cache_manager: CacheManager,
        config: Optional[UIConfig] = None
    ):
        self.user_manager = user_manager
        self.subscription_manager = subscription_manager
        self.auth_manager = auth_manager
        self.cache_manager = cache_manager
        self.config = config or UIConfig()
        self.logger = logger.bind(service="user_interface")
    
    async def get_user_interface_data(self, user_id: str) -> UserInterfaceData:
        """
        Get comprehensive user interface data.
        
        Args:
            user_id: User identifier
            
        Returns:
            Complete user interface data
        """
        try:
            cache_key = f"ui_data:{user_id}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("UI data served from cache", user_id=user_id)
                return UserInterfaceData(**cached_data)
            
            # Get user profile
            profile = await self._get_user_profile(user_id)
            
            # Get subscription data
            subscription = await self._get_subscription_data(user_id)
            
            # Get notification preferences
            preferences = await self._get_notification_preferences(user_id)
            
            # Get API access data
            api_access = await self._get_api_access_data(user_id)
            
            ui_data = UserInterfaceData(
                profile=profile,
                subscription=subscription,
                preferences=preferences,
                api_access=api_access,
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                ui_data.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("UI data generated", user_id=user_id)
            return ui_data
            
        except Exception as e:
            self.logger.error("Failed to get UI data", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to load user interface data")
    
    async def _get_user_profile(self, user_id: str) -> UserProfile:
        """Get user profile data"""
        try:
            user = await self.user_manager.get_user_by_id(user_id)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
            
            return UserProfile(
                user_id=user.id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                avatar_url=user.avatar_url,
                bio=user.bio,
                timezone=user.timezone,
                language=user.language,
                created_at=user.created_at,
                last_login=user.last_login,
                is_verified=user.is_verified,
                subscription_tier=user.subscription_tier
            )
            
        except Exception as e:
            self.logger.error("Failed to get user profile", user_id=user_id, error=str(e))
            raise
    
    async def _get_subscription_data(self, user_id: str) -> SubscriptionData:
        """Get subscription data"""
        try:
            subscription = await self.subscription_manager.get_user_subscription(user_id)
            
            if not subscription:
                return SubscriptionData(
                    tier="free",
                    status="active",
                    features=[],
                    limits={},
                    expires_at=None
                )
            
            return SubscriptionData(
                tier=subscription.tier,
                status=subscription.status,
                features=subscription.features,
                limits=subscription.limits,
                expires_at=subscription.expires_at,
                auto_renew=subscription.auto_renew,
                payment_method=subscription.payment_method
            )
            
        except Exception as e:
            self.logger.error("Failed to get subscription data", user_id=user_id, error=str(e))
            raise
    
    async def _get_notification_preferences(self, user_id: str) -> NotificationPreferences:
        """Get notification preferences"""
        try:
            preferences = await self.subscription_manager.get_user_preferences(user_id)
            
            if not preferences:
                # Return default preferences
                return NotificationPreferences(
                    email_enabled=True,
                    telegram_enabled=False,
                    push_enabled=True,
                    signal_alerts=True,
                    portfolio_updates=True,
                    trade_confirmations=True,
                    risk_warnings=True,
                    daily_reports=False,
                    quiet_hours_enabled=False,
                    quiet_hours_start="22:00",
                    quiet_hours_end="08:00"
                )
            
            return preferences
            
        except Exception as e:
            self.logger.error("Failed to get notification preferences", user_id=user_id, error=str(e))
            raise
    
    async def _get_api_access_data(self, user_id: str) -> APIAccessData:
        """Get API access data"""
        try:
            api_keys = await self.auth_manager.get_user_api_keys(user_id)
            
            return APIAccessData(
                api_keys=[
                    {
                        "id": key.id,
                        "name": key.name,
                        "key_preview": f"{key.key[:8]}...{key.key[-4:]}",
                        "permissions": key.permissions,
                        "created_at": key.created_at,
                        "last_used": key.last_used,
                        "expires_at": key.expires_at,
                        "is_active": key.is_active
                    }
                    for key in api_keys
                ],
                rate_limits={
                    "requests_per_minute": 100,
                    "requests_per_hour": 1000,
                    "requests_per_day": 10000
                },
                usage_stats=await self._get_api_usage_stats(user_id)
            )
            
        except Exception as e:
            self.logger.error("Failed to get API access data", user_id=user_id, error=str(e))
            raise
    
    async def _get_api_usage_stats(self, user_id: str) -> Dict[str, Any]:
        """Get API usage statistics"""
        try:
            # Get usage stats for the last 30 days
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            stats = await self.auth_manager.get_api_usage_stats(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            return {
                "total_requests": stats.get("total_requests", 0),
                "successful_requests": stats.get("successful_requests", 0),
                "failed_requests": stats.get("failed_requests", 0),
                "average_response_time": stats.get("average_response_time", 0),
                "most_used_endpoints": stats.get("most_used_endpoints", []),
                "daily_usage": stats.get("daily_usage", [])
            }
            
        except Exception as e:
            self.logger.error("Failed to get API usage stats", user_id=user_id, error=str(e))
            return {}
    
    async def update_user_profile(
        self, 
        user_id: str, 
        profile_data: Dict[str, Any]
    ) -> UserProfile:
        """
        Update user profile.
        
        Args:
            user_id: User identifier
            profile_data: Profile data to update
            
        Returns:
            Updated user profile
        """
        try:
            # Check update cooldown
            last_update_key = f"profile_update:{user_id}"
            last_update = await self.cache_manager.get(last_update_key)
            
            if last_update:
                raise HTTPException(
                    status_code=429, 
                    detail="Profile update too frequent. Please wait before updating again."
                )
            
            # Update profile
            updated_user = await self.user_manager.update_user_profile(user_id, profile_data)
            
            # Set cooldown
            await self.cache_manager.set(
                last_update_key,
                datetime.utcnow().isoformat(),
                ttl=self.config.profile_update_cooldown
            )
            
            # Invalidate cache
            await self._invalidate_user_cache(user_id)
            
            self.logger.info("User profile updated", user_id=user_id)
            return await self._get_user_profile(user_id)
            
        except Exception as e:
            self.logger.error("Failed to update user profile", user_id=user_id, error=str(e))
            raise
    
    async def update_notification_preferences(
        self,
        user_id: str,
        preferences: NotificationPreferences
    ) -> NotificationPreferences:
        """
        Update notification preferences.
        
        Args:
            user_id: User identifier
            preferences: New notification preferences
            
        Returns:
            Updated preferences
        """
        try:
            updated_preferences = await self.subscription_manager.update_user_preferences(
                user_id, preferences
            )
            
            # Invalidate cache
            await self._invalidate_user_cache(user_id)
            
            self.logger.info("Notification preferences updated", user_id=user_id)
            return updated_preferences
            
        except Exception as e:
            self.logger.error("Failed to update preferences", user_id=user_id, error=str(e))
            raise
    
    async def create_api_key(
        self,
        user_id: str,
        key_name: str,
        permissions: List[str],
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create new API key.
        
        Args:
            user_id: User identifier
            key_name: Name for the API key
            permissions: List of permissions
            expires_in_days: Expiration in days (optional)
            
        Returns:
            Created API key data
        """
        try:
            # Check API key limit
            existing_keys = await self.auth_manager.get_user_api_keys(user_id)
            active_keys = [key for key in existing_keys if key.is_active]
            
            if len(active_keys) >= self.config.max_api_keys:
                raise HTTPException(
                    status_code=400,
                    detail=f"Maximum number of API keys ({self.config.max_api_keys}) reached"
                )
            
            # Create API key
            api_key = await self.auth_manager.create_api_key(
                user_id=user_id,
                name=key_name,
                permissions=permissions,
                expires_in_days=expires_in_days
            )
            
            # Invalidate cache
            await self._invalidate_user_cache(user_id)
            
            self.logger.info("API key created", user_id=user_id, key_name=key_name)
            return {
                "id": api_key.id,
                "name": api_key.name,
                "key": api_key.key,  # Only returned once
                "permissions": api_key.permissions,
                "expires_at": api_key.expires_at
            }
            
        except Exception as e:
            self.logger.error("Failed to create API key", user_id=user_id, error=str(e))
            raise
    
    async def revoke_api_key(self, user_id: str, key_id: str) -> bool:
        """
        Revoke an API key.
        
        Args:
            user_id: User identifier
            key_id: API key identifier
            
        Returns:
            True if key was revoked successfully
        """
        try:
            success = await self.auth_manager.revoke_api_key(user_id, key_id)
            
            if success:
                # Invalidate cache
                await self._invalidate_user_cache(user_id)
                self.logger.info("API key revoked", user_id=user_id, key_id=key_id)
            
            return success
            
        except Exception as e:
            self.logger.error("Failed to revoke API key", user_id=user_id, key_id=key_id, error=str(e))
            raise
    
    async def _invalidate_user_cache(self, user_id: str) -> None:
        """Invalidate all user-related cache entries"""
        try:
            cache_keys = [
                f"ui_data:{user_id}",
                f"user_profile:{user_id}",
                f"user_preferences:{user_id}",
                f"api_keys:{user_id}"
            ]
            
            for key in cache_keys:
                await self.cache_manager.delete(key)
                
        except Exception as e:
            self.logger.error("Failed to invalidate user cache", user_id=user_id, error=str(e))
