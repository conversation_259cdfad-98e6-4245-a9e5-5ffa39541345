# TokenTracker V2 API Documentation

This document provides comprehensive API documentation for TokenTracker V2. The API is built with FastAPI and provides RESTful endpoints for all system functionality.

## 📋 Table of Contents

- [Authentication](#authentication)
- [Base URL](#base-url)
- [Response Format](#response-format)
- [Error Handling](#error-handling)
- [Rate Limiting](#rate-limiting)
- [API Endpoints](#api-endpoints)

## 🔐 Authentication

TokenTracker V2 uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

Response:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## 🌐 Base URL

- **Production**: `https://api.tokentracker.com`
- **Staging**: `https://staging-api.tokentracker.com`
- **Development**: `http://localhost:8000`

## 📊 Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "issue": "Invalid email format"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## ⚠️ Error Handling

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 422 | Validation Error |
| 429 | Rate Limited |
| 500 | Internal Server Error |

### Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Input validation failed |
| `AUTHENTICATION_ERROR` | Invalid credentials |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `RESOURCE_NOT_FOUND` | Requested resource not found |
| `INTERNAL_ERROR` | Server error |

## 🚦 Rate Limiting

API endpoints are rate limited to ensure fair usage:

- **Default**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **Trading**: 50 requests per minute

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

## 🔗 API Endpoints

### Authentication & Users

#### Login
```http
POST /api/v1/auth/login
```

#### Register
```http
POST /api/v1/auth/register
```

#### Get User Profile
```http
GET /api/v1/auth/profile
```

### Signal Processing

#### Get Signals
```http
GET /api/v1/signals?limit=20&strength=STRONG
```

#### Create Signal
```http
POST /api/v1/signals
```

#### Get Signal Details
```http
GET /api/v1/signals/{signal_id}
```

### Portfolio Management

#### Get Portfolio
```http
GET /api/v1/portfolio
```

#### Get Positions
```http
GET /api/v1/portfolio/positions
```

#### Get Performance
```http
GET /api/v1/portfolio/performance?period=30d
```

### Paper Trading

#### Execute Trade
```http
POST /api/v1/trading/execute
```

#### Get Trade History
```http
GET /api/v1/trading/history?limit=50&page=1
```

#### Get Open Orders
```http
GET /api/v1/trading/orders
```

### Risk Management

#### Get Risk Metrics
```http
GET /api/v1/risk/metrics
```

#### Update Risk Settings
```http
PUT /api/v1/risk/settings
```

### Data Pipeline

#### Get Token Data
```http
GET /api/v1/data/tokens/{token_address}
```

#### Get Market Data
```http
GET /api/v1/data/market?symbols=BTC,ETH,SOL
```

### Notifications

#### Get Notifications
```http
GET /api/v1/notifications?unread=true
```

#### Mark as Read
```http
PUT /api/v1/notifications/{notification_id}/read
```

#### Update Preferences
```http
PUT /api/v1/notifications/preferences
```

### Web Interface

#### Get Dashboard Data
```http
GET /api/v1/web/dashboard
```

#### Get Real-time Updates
```http
GET /api/v1/web/dashboard/realtime
```

#### Get Charts
```http
GET /api/v1/web/charts/portfolio?timeframe=1d&type=line
```

### Mobile Integration

#### Register Device
```http
POST /api/v1/mobile/push/register
```

#### Get Mobile Dashboard
```http
GET /api/v1/mobile/dashboard?format=compact
```

#### Sync Data
```http
POST /api/v1/mobile/sync
```

### Third-Party Integrations

#### Get Exchange Balances
```http
GET /api/v1/integrations/exchanges/balances
```

#### Scan Arbitrage
```http
GET /api/v1/integrations/arbitrage/opportunities
```

#### Get Sentiment
```http
GET /api/v1/integrations/sentiment/tokens/{token_address}
```

### Monitoring

#### Health Check
```http
GET /health
```

#### System Metrics
```http
GET /api/v1/monitoring/metrics
```

#### Performance Stats
```http
GET /api/v1/monitoring/performance
```

## 📝 Request Examples

### Get Portfolio with Positions

```bash
curl -X GET "https://api.tokentracker.com/api/v1/portfolio" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json"
```

Response:
```json
{
  "success": true,
  "data": {
    "total_value": 10000.50,
    "total_pnl": 500.25,
    "total_pnl_percentage": 5.25,
    "positions": [
      {
        "token_address": "So11111111111111111111111111111111111111112",
        "token_symbol": "SOL",
        "quantity": 100.0,
        "average_price": 95.50,
        "current_price": 100.25,
        "unrealized_pnl": 475.0,
        "unrealized_pnl_percentage": 4.97
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Execute Paper Trade

```bash
curl -X POST "https://api.tokentracker.com/api/v1/trading/execute" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "signal_id": "signal-123",
    "token_address": "So11111111111111111111111111111111111111112",
    "trade_type": "BUY",
    "quantity": 10.0,
    "order_type": "MARKET"
  }'
```

Response:
```json
{
  "success": true,
  "data": {
    "trade_id": "trade-456",
    "status": "EXECUTED",
    "execution_price": 100.25,
    "quantity": 10.0,
    "total_value": 1002.50,
    "fees": 2.50,
    "executed_at": "2024-01-15T10:30:00Z"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔄 WebSocket API

For real-time data, TokenTracker V2 provides WebSocket endpoints:

### Connection
```javascript
const ws = new WebSocket('wss://api.tokentracker.com/ws');
```

### Subscribe to Signals
```json
{
  "action": "subscribe",
  "channel": "signals",
  "filters": {
    "strength": ["STRONG", "VERY_STRONG"]
  }
}
```

### Subscribe to Portfolio Updates
```json
{
  "action": "subscribe",
  "channel": "portfolio",
  "user_id": "user-123"
}
```

## 📚 SDK Libraries

Official SDKs are available for popular languages:

- **Python**: `pip install tokentracker-sdk`
- **JavaScript**: `npm install @tokentracker/sdk`
- **Go**: `go get github.com/tokentracker/go-sdk`

### Python SDK Example

```python
from tokentracker import TokenTrackerClient

client = TokenTrackerClient(api_key="your-api-key")

# Get portfolio
portfolio = client.portfolio.get()

# Execute trade
trade = client.trading.execute(
    signal_id="signal-123",
    token_address="So11111111111111111111111111111111111111112",
    trade_type="BUY",
    quantity=10.0
)
```

## 🧪 Testing

Use the interactive API documentation at `/docs` to test endpoints directly in your browser.

For automated testing, use the OpenAPI specification available at `/openapi.json`.

## 📞 Support

- **API Issues**: [GitHub Issues](https://github.com/your-org/tokentracker-v2/issues)
- **Documentation**: [API Docs](./README.md)
- **Email**: <EMAIL>

---

For detailed endpoint documentation, see the individual API guides:
- [Authentication API](./authentication.md)
- [Signal Processing API](./signal-processing.md)
- [Portfolio API](./portfolio.md)
- [Trading API](./trading.md)
