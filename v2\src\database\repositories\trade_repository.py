"""
💹 Trade Repository

Repository for trade data access operations.
"""

from typing import List, Optional, Dict, Any
from ..models.trade import Trade
from .base_repository import BaseRepository


class TradeRepository(BaseRepository):
    """Repository for trade operations"""
    
    def __init__(self):
        super().__init__(Trade)
    
    async def get_by_portfolio_id(self, portfolio_id: str) -> List[Trade]:
        """Get trades by portfolio ID"""
        return await self.find_many({"portfolio_id": portfolio_id})
    
    async def get_by_user_id(self, user_id: str) -> List[Trade]:
        """Get trades by user ID"""
        return await self.find_many({"user_id": user_id})
