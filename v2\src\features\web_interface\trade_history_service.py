"""
Trade History Service

Provides comprehensive trade history management, filtering, and analytics
for the web interface. Supports advanced search and export functionality.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTTPException

from src.shared.types import (
    TradeData, TradeFilter, TradeAnalytics, 
    TradeExportData, PaginatedTradeHistory
)
from src.features.paper_trading.portfolio_manager import PortfolioManager
from src.features.paper_trading.trade_executor import TradeExecutor
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


class TradeStatus(Enum):
    """Trade status enumeration"""
    PENDING = "pending"
    EXECUTED = "executed"
    CANCELLED = "cancelled"
    FAILED = "failed"


class TradeType(Enum):
    """Trade type enumeration"""
    BUY = "buy"
    SELL = "sell"


class SortOrder(Enum):
    """Sort order enumeration"""
    ASC = "asc"
    DESC = "desc"


@dataclass
class TradeHistoryConfig:
    """Trade history service configuration"""
    max_export_records: int = 10000
    cache_ttl: int = 300  # seconds
    default_page_size: int = 50
    max_page_size: int = 200


class TradeHistoryService:
    """
    Provides comprehensive trade history management and analytics.
    Supports filtering, sorting, pagination, and export functionality.
    """
    
    def __init__(
        self,
        portfolio_manager: PortfolioManager,
        trade_executor: TradeExecutor,
        cache_manager: CacheManager,
        config: Optional[TradeHistoryConfig] = None
    ):
        self.portfolio_manager = portfolio_manager
        self.trade_executor = trade_executor
        self.cache_manager = cache_manager
        self.config = config or TradeHistoryConfig()
        self.logger = logger.bind(service="trade_history")
    
    async def get_trade_history(
        self,
        user_id: str,
        filters: Optional[TradeFilter] = None,
        page: int = 1,
        page_size: int = 50,
        sort_by: str = "created_at",
        sort_order: SortOrder = SortOrder.DESC
    ) -> PaginatedTradeHistory:
        """
        Get paginated trade history with filtering and sorting.
        
        Args:
            user_id: User identifier
            filters: Trade filters
            page: Page number (1-based)
            page_size: Number of records per page
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)
            
        Returns:
            Paginated trade history
        """
        try:
            # Validate page size
            page_size = min(page_size, self.config.max_page_size)
            
            # Create cache key
            cache_key = self._create_cache_key(
                user_id, filters, page, page_size, sort_by, sort_order
            )
            
            cached_data = await self.cache_manager.get(cache_key)
            if cached_data:
                self.logger.info("Trade history served from cache", user_id=user_id)
                return PaginatedTradeHistory(**cached_data)
            
            # Get trades with filters
            trades, total_count = await self._get_filtered_trades(
                user_id=user_id,
                filters=filters,
                page=page,
                page_size=page_size,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            # Calculate pagination info
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            
            result = PaginatedTradeHistory(
                trades=trades,
                pagination={
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev
                },
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order.value
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                result.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info(
                "Trade history retrieved",
                user_id=user_id,
                count=len(trades),
                total=total_count
            )
            return result
            
        except Exception as e:
            self.logger.error("Failed to get trade history", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to retrieve trade history")
    
    async def get_trade_details(self, user_id: str, trade_id: str) -> TradeData:
        """
        Get detailed information for a specific trade.
        
        Args:
            user_id: User identifier
            trade_id: Trade identifier
            
        Returns:
            Detailed trade information
        """
        try:
            cache_key = f"trade_details:{user_id}:{trade_id}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Trade details served from cache", trade_id=trade_id)
                return TradeData(**cached_data)
            
            # Get trade from database
            trade = await self.portfolio_manager.get_trade_by_id(user_id, trade_id)
            
            if not trade:
                raise HTTPException(status_code=404, detail="Trade not found")
            
            # Convert to TradeData
            trade_data = TradeData(
                id=trade.id,
                user_id=trade.user_id,
                token_address=trade.token_address,
                token_symbol=trade.token_symbol,
                trade_type=trade.trade_type,
                quantity=trade.quantity,
                price=trade.price,
                total_value=trade.total_value,
                fees=trade.fees,
                slippage=trade.slippage,
                pnl=trade.pnl,
                status=trade.status,
                signal_id=trade.signal_id,
                execution_time=trade.execution_time,
                created_at=trade.created_at,
                updated_at=trade.updated_at,
                metadata=trade.metadata or {}
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                trade_data.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Trade details retrieved", trade_id=trade_id)
            return trade_data
            
        except Exception as e:
            self.logger.error("Failed to get trade details", trade_id=trade_id, error=str(e))
            raise
    
    async def get_trade_analytics(
        self,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> TradeAnalytics:
        """
        Get comprehensive trade analytics.
        
        Args:
            user_id: User identifier
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Trade analytics data
        """
        try:
            # Default to last 30 days if no dates provided
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)
            
            cache_key = f"trade_analytics:{user_id}:{start_date.date()}:{end_date.date()}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Trade analytics served from cache", user_id=user_id)
                return TradeAnalytics(**cached_data)
            
            # Get trades for the period
            trades = await self.portfolio_manager.get_trades_by_date_range(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Calculate analytics
            analytics = self._calculate_trade_analytics(trades, start_date, end_date)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                analytics.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Trade analytics calculated", user_id=user_id)
            return analytics
            
        except Exception as e:
            self.logger.error("Failed to get trade analytics", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to calculate trade analytics")
    
    async def export_trade_history(
        self,
        user_id: str,
        filters: Optional[TradeFilter] = None,
        format: str = "csv"
    ) -> TradeExportData:
        """
        Export trade history in specified format.
        
        Args:
            user_id: User identifier
            filters: Trade filters
            format: Export format (csv, json, xlsx)
            
        Returns:
            Export data with download URL
        """
        try:
            # Get all trades matching filters (up to limit)
            trades, total_count = await self._get_filtered_trades(
                user_id=user_id,
                filters=filters,
                page=1,
                page_size=self.config.max_export_records,
                sort_by="created_at",
                sort_order=SortOrder.DESC
            )
            
            if total_count > self.config.max_export_records:
                self.logger.warning(
                    "Export truncated due to size limit",
                    user_id=user_id,
                    total=total_count,
                    limit=self.config.max_export_records
                )
            
            # Generate export data
            export_data = await self._generate_export_data(trades, format)
            
            self.logger.info(
                "Trade history exported",
                user_id=user_id,
                format=format,
                count=len(trades)
            )
            
            return export_data
            
        except Exception as e:
            self.logger.error("Failed to export trade history", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to export trade history")
    
    async def _get_filtered_trades(
        self,
        user_id: str,
        filters: Optional[TradeFilter],
        page: int,
        page_size: int,
        sort_by: str,
        sort_order: SortOrder
    ) -> Tuple[List[TradeData], int]:
        """Get filtered and sorted trades"""
        try:
            # Build query filters
            query_filters = {"user_id": user_id}
            
            if filters:
                if filters.start_date:
                    query_filters["created_at__gte"] = filters.start_date
                if filters.end_date:
                    query_filters["created_at__lte"] = filters.end_date
                if filters.token_address:
                    query_filters["token_address"] = filters.token_address
                if filters.trade_type:
                    query_filters["trade_type"] = filters.trade_type
                if filters.status:
                    query_filters["status"] = filters.status
                if filters.min_value:
                    query_filters["total_value__gte"] = filters.min_value
                if filters.max_value:
                    query_filters["total_value__lte"] = filters.max_value
            
            # Get trades from database
            trades = await self.portfolio_manager.get_trades_with_filters(
                filters=query_filters,
                page=page,
                page_size=page_size,
                sort_by=sort_by,
                sort_order=sort_order.value
            )
            
            # Get total count
            total_count = await self.portfolio_manager.count_trades_with_filters(query_filters)
            
            # Convert to TradeData objects
            trade_data_list = [
                TradeData(
                    id=trade.id,
                    user_id=trade.user_id,
                    token_address=trade.token_address,
                    token_symbol=trade.token_symbol,
                    trade_type=trade.trade_type,
                    quantity=trade.quantity,
                    price=trade.price,
                    total_value=trade.total_value,
                    fees=trade.fees,
                    slippage=trade.slippage,
                    pnl=trade.pnl,
                    status=trade.status,
                    signal_id=trade.signal_id,
                    execution_time=trade.execution_time,
                    created_at=trade.created_at,
                    updated_at=trade.updated_at,
                    metadata=trade.metadata or {}
                )
                for trade in trades
            ]
            
            return trade_data_list, total_count
            
        except Exception as e:
            self.logger.error("Failed to get filtered trades", user_id=user_id, error=str(e))
            raise
    
    def _calculate_trade_analytics(
        self,
        trades: List[Any],
        start_date: datetime,
        end_date: datetime
    ) -> TradeAnalytics:
        """Calculate comprehensive trade analytics"""
        if not trades:
            return TradeAnalytics(
                period_start=start_date,
                period_end=end_date,
                total_trades=0,
                profitable_trades=0,
                losing_trades=0,
                win_rate=0.0,
                total_pnl=0.0,
                total_fees=0.0,
                avg_trade_size=0.0,
                avg_profit=0.0,
                avg_loss=0.0,
                largest_profit=0.0,
                largest_loss=0.0,
                profit_factor=0.0,
                trade_frequency=0.0,
                token_distribution={},
                daily_stats=[]
            )
        
        total_trades = len(trades)
        profitable_trades = [t for t in trades if t.pnl > 0]
        losing_trades = [t for t in trades if t.pnl < 0]
        
        total_pnl = sum(trade.pnl for trade in trades)
        total_fees = sum(trade.fees for trade in trades)
        total_profit = sum(trade.pnl for trade in profitable_trades)
        total_loss = abs(sum(trade.pnl for trade in losing_trades))
        
        # Calculate metrics
        win_rate = len(profitable_trades) / total_trades * 100 if total_trades > 0 else 0
        avg_trade_size = sum(trade.total_value for trade in trades) / total_trades if total_trades > 0 else 0
        avg_profit = total_profit / len(profitable_trades) if profitable_trades else 0
        avg_loss = total_loss / len(losing_trades) if losing_trades else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf') if total_profit > 0 else 0
        
        # Calculate trade frequency (trades per day)
        period_days = (end_date - start_date).days + 1
        trade_frequency = total_trades / period_days if period_days > 0 else 0
        
        # Token distribution
        token_distribution = {}
        for trade in trades:
            token = trade.token_symbol or trade.token_address
            if token not in token_distribution:
                token_distribution[token] = {"count": 0, "volume": 0, "pnl": 0}
            token_distribution[token]["count"] += 1
            token_distribution[token]["volume"] += trade.total_value
            token_distribution[token]["pnl"] += trade.pnl
        
        # Daily stats
        daily_stats = self._calculate_daily_stats(trades, start_date, end_date)
        
        return TradeAnalytics(
            period_start=start_date,
            period_end=end_date,
            total_trades=total_trades,
            profitable_trades=len(profitable_trades),
            losing_trades=len(losing_trades),
            win_rate=win_rate,
            total_pnl=total_pnl,
            total_fees=total_fees,
            avg_trade_size=avg_trade_size,
            avg_profit=avg_profit,
            avg_loss=avg_loss,
            largest_profit=max(trade.pnl for trade in trades) if trades else 0,
            largest_loss=min(trade.pnl for trade in trades) if trades else 0,
            profit_factor=profit_factor,
            trade_frequency=trade_frequency,
            token_distribution=token_distribution,
            daily_stats=daily_stats
        )
    
    def _calculate_daily_stats(
        self,
        trades: List[Any],
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """Calculate daily trading statistics"""
        daily_stats = {}
        
        # Initialize all days in the period
        current_date = start_date.date()
        while current_date <= end_date.date():
            daily_stats[current_date] = {
                "date": current_date,
                "trades": 0,
                "volume": 0,
                "pnl": 0,
                "fees": 0
            }
            current_date += timedelta(days=1)
        
        # Aggregate trades by day
        for trade in trades:
            trade_date = trade.created_at.date()
            if trade_date in daily_stats:
                daily_stats[trade_date]["trades"] += 1
                daily_stats[trade_date]["volume"] += trade.total_value
                daily_stats[trade_date]["pnl"] += trade.pnl
                daily_stats[trade_date]["fees"] += trade.fees
        
        return list(daily_stats.values())
    
    async def _generate_export_data(
        self,
        trades: List[TradeData],
        format: str
    ) -> TradeExportData:
        """Generate export data in specified format"""
        try:
            if format.lower() == "csv":
                content = self._generate_csv_export(trades)
                content_type = "text/csv"
                filename = f"trade_history_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.csv"
            elif format.lower() == "json":
                content = self._generate_json_export(trades)
                content_type = "application/json"
                filename = f"trade_history_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
            else:
                raise HTTPException(status_code=400, detail="Unsupported export format")
            
            return TradeExportData(
                content=content,
                content_type=content_type,
                filename=filename,
                size=len(content.encode('utf-8')),
                record_count=len(trades)
            )
            
        except Exception as e:
            self.logger.error("Failed to generate export data", format=format, error=str(e))
            raise
    
    def _generate_csv_export(self, trades: List[TradeData]) -> str:
        """Generate CSV export content"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "ID", "Date", "Token", "Type", "Quantity", "Price", 
            "Total Value", "Fees", "Slippage", "PnL", "Status"
        ])
        
        # Write data
        for trade in trades:
            writer.writerow([
                trade.id,
                trade.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                trade.token_symbol or trade.token_address,
                trade.trade_type,
                trade.quantity,
                trade.price,
                trade.total_value,
                trade.fees,
                trade.slippage,
                trade.pnl,
                trade.status
            ])
        
        return output.getvalue()
    
    def _generate_json_export(self, trades: List[TradeData]) -> str:
        """Generate JSON export content"""
        import json
        
        export_data = {
            "export_date": datetime.utcnow().isoformat(),
            "record_count": len(trades),
            "trades": [trade.dict() for trade in trades]
        }
        
        return json.dumps(export_data, indent=2, default=str)
    
    def _create_cache_key(
        self,
        user_id: str,
        filters: Optional[TradeFilter],
        page: int,
        page_size: int,
        sort_by: str,
        sort_order: SortOrder
    ) -> str:
        """Create cache key for trade history query"""
        filter_str = ""
        if filters:
            filter_parts = []
            if filters.start_date:
                filter_parts.append(f"start:{filters.start_date.date()}")
            if filters.end_date:
                filter_parts.append(f"end:{filters.end_date.date()}")
            if filters.token_address:
                filter_parts.append(f"token:{filters.token_address}")
            if filters.trade_type:
                filter_parts.append(f"type:{filters.trade_type}")
            if filters.status:
                filter_parts.append(f"status:{filters.status}")
            filter_str = "|".join(filter_parts)
        
        return f"trade_history:{user_id}:{filter_str}:{page}:{page_size}:{sort_by}:{sort_order.value}"
