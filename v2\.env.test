# Test Environment Configuration
ENVIRONMENT=test
LOG_LEVEL=INFO

# Database
MONGODB_URI=mongodb://localhost:27017/tokentracker_test

# API Keys (test values)
DUNE_API_KEY=test_dune_key
DUNE_QUERY_ID=123456
TELEGRAM_BOT_TOKEN=test_bot_token
TELEGRAM_CHANNEL_ID=test_channel_id

# Security
JWT_SECRET=test_jwt_secret_key_for_testing_purposes_only
ENCRYPTION_KEY=test_encryption_key_for_testing_purposes_only

# Solana
SOLANA_RPC_URL=https://api.devnet.solana.com
SOLANA_PRIVATE_KEY=test_private_key

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=100

# Features
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=false
ENABLE_NOTIFICATIONS=false
