"""
🧪 Data Pipeline Tests

Test suite for the data pipeline implementation including Jupiter, Raydium,
Solana clients, data aggregator, validator, and cache manager.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
from datetime import datetime

# Import the modules we want to test
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.features.data_pipeline import (
    JupiterClient,
    RaydiumClient,
    SolanaClient,
    DataAggregator,
    DataValidator,
    CacheManager,
    AggregatedTokenData,
    ValidationResult
)
from src.shared.types import TokenData, TokenMetrics


class TestJupiterClient:
    """Test Jupiter API client"""
    
    @pytest.fixture
    def jupiter_client(self):
        """Create Jupiter client for testing"""
        return JupiterClient("test")
    
    @pytest.mark.asyncio
    async def test_jupiter_client_initialization(self, jupiter_client):
        """Test Jupiter client initialization"""
        assert jupiter_client.project_id == "test"
        assert jupiter_client.base_url is not None
        assert jupiter_client.client is not None
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, jupiter_client):
        """Test rate limiting functionality"""
        # Should be within limits initially
        assert jupiter_client._check_rate_limit() == True
        
        # Fill up the rate limit
        for _ in range(jupiter_client.rate_limit_requests):
            jupiter_client._check_rate_limit()
        
        # Should now be rate limited
        assert jupiter_client._check_rate_limit() == False
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.request')
    async def test_get_token_price_success(self, mock_request, jupiter_client):
        """Test successful token price fetching"""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": {
                "So11111111111111111111111111111111111111112": {
                    "address": "So11111111111111111111111111111111111111112",
                    "symbol": "TEST",
                    "name": "Test Token",
                    "decimals": 9,
                    "price": "1.23",
                    "marketCap": "1000000",
                    "volume24h": "50000"
                }
            }
        }
        mock_response.raise_for_status.return_value = None
        mock_request.return_value = mock_response
        
        result = await jupiter_client.get_token_price("So11111111111111111111111111111111111111112")
        
        assert result is not None
        assert result.symbol == "TEST"
        assert result.name == "Test Token"
        assert result.price == Decimal("1.23")
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.request')
    async def test_get_token_price_not_found(self, mock_request, jupiter_client):
        """Test token price fetching when token not found"""
        # Mock response with no data
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"data": {}}
        mock_response.raise_for_status.return_value = None
        mock_request.return_value = mock_response
        
        result = await jupiter_client.get_token_price("nonexistent_token")
        
        assert result is None


class TestRaydiumClient:
    """Test Raydium API client"""
    
    @pytest.fixture
    async def raydium_client(self):
        """Create Raydium client for testing"""
        client = RaydiumClient("test")
        yield client
        await client.close()
    
    @pytest.mark.asyncio
    async def test_raydium_client_initialization(self, raydium_client):
        """Test Raydium client initialization"""
        assert raydium_client.project_id == "test"
        assert raydium_client.base_url is not None
        assert raydium_client.client is not None
    
    @pytest.mark.asyncio
    @patch('httpx.AsyncClient.request')
    async def test_get_token_pools_success(self, mock_request, raydium_client):
        """Test successful token pools fetching"""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": "pool1",
                "baseMint": "test_token",
                "quoteMint": "SOL",
                "liquidity": "100000"
            }
        ]
        mock_response.raise_for_status.return_value = None
        mock_request.return_value = mock_response
        
        result = await raydium_client.get_token_pools("test_token")
        
        assert result is not None
        assert len(result) == 1
        assert result[0]["id"] == "pool1"
    
    @pytest.mark.asyncio
    async def test_validate_trading_pair(self, raydium_client):
        """Test trading pair validation"""
        # Mock the get_token_pools method
        raydium_client.get_token_pools = AsyncMock(return_value=[
            {
                "id": "pool1",
                "baseMint": "token1",
                "quoteMint": "token2",
                "liquidity": "100000"
            }
        ])
        
        # Should find the pair
        result = await raydium_client.validate_trading_pair("token1", "token2")
        assert result == True
        
        # Should not find non-existent pair
        result = await raydium_client.validate_trading_pair("token1", "token3")
        assert result == False


class TestDataValidator:
    """Test data validator"""
    
    @pytest.fixture
    def validator(self):
        """Create data validator for testing"""
        return DataValidator("test")
    
    def test_validate_token_address_valid(self, validator):
        """Test valid token address validation"""
        valid_address = "So11111111111111111111111111111111111111112"
        result = validator.validate_token_address(valid_address)
        
        assert result.is_valid == True
        assert len(result.errors) == 0
    
    def test_validate_token_address_invalid(self, validator):
        """Test invalid token address validation"""
        invalid_address = "invalid_address"
        result = validator.validate_token_address(invalid_address)
        
        assert result.is_valid == False
        assert len(result.errors) > 0
    
    def test_validate_price_data_consistent(self, validator):
        """Test price data validation with consistent prices"""
        price_data = [
            TokenData(
                address="test",
                symbol="TEST",
                name="Test",
                price=Decimal("1.00"),
                timestamp=datetime.utcnow()
            ),
            TokenData(
                address="test",
                symbol="TEST",
                name="Test",
                price=Decimal("1.05"),
                timestamp=datetime.utcnow()
            )
        ]
        
        result = validator.validate_price_data(price_data)
        
        assert result.is_valid == True
        assert result.confidence_score > 0.8
    
    def test_validate_price_data_inconsistent(self, validator):
        """Test price data validation with inconsistent prices"""
        price_data = [
            TokenData(
                address="test",
                symbol="TEST",
                name="Test",
                price=Decimal("1.00"),
                timestamp=datetime.utcnow()
            ),
            TokenData(
                address="test",
                symbol="TEST",
                name="Test",
                price=Decimal("2.50"),  # 150% difference
                timestamp=datetime.utcnow()
            )
        ]
        
        result = validator.validate_price_data(price_data)
        
        # Should have warnings about high deviation
        assert len(result.warnings) > 0
        assert result.confidence_score < 1.0
    
    def test_validate_liquidity_threshold(self, validator):
        """Test liquidity threshold validation"""
        # Above threshold
        result = validator.validate_liquidity_threshold(Decimal("100000"))
        assert result.is_valid == True
        
        # Below threshold
        result = validator.validate_liquidity_threshold(Decimal("1000"))
        assert result.is_valid == False
        assert len(result.errors) > 0


class TestDataAggregator:
    """Test data aggregator"""
    
    @pytest.fixture
    async def aggregator(self):
        """Create data aggregator for testing"""
        aggregator = DataAggregator("test")
        yield aggregator
        await aggregator.close()
    
    @pytest.mark.asyncio
    async def test_aggregator_initialization(self, aggregator):
        """Test data aggregator initialization"""
        assert aggregator.project_id == "test"
        assert aggregator.dune_client is not None
        assert aggregator.jupiter_client is not None
        assert aggregator.raydium_client is not None
        assert aggregator.solana_client is not None
    
    def test_calculate_confidence_score(self, aggregator):
        """Test confidence score calculation"""
        # Create test data with all sources
        data = AggregatedTokenData(
            token_address="test",
            symbol="TEST",
            name="Test Token",
            price=Decimal("1.00"),
            pools=[{"id": "pool1"}],
            total_liquidity=Decimal("100000"),
            token_supply=Decimal("1000000"),
            trading_activity={"volume": "50000"},
            data_sources=["Jupiter", "Raydium", "Solana", "Dune"]
        )
        
        score = aggregator._calculate_confidence_score(data)
        
        # Should have high confidence with all data sources
        assert score > 0.9
    
    def test_cache_validity(self, aggregator):
        """Test cache validity checking"""
        # No cache entry
        assert aggregator._is_cache_valid("test_token") == False
        
        # Add cache entry
        aggregator.cache["test_token"] = AggregatedTokenData(
            token_address="test_token",
            symbol="TEST",
            name="Test",
            last_updated=datetime.utcnow()
        )
        
        # Should be valid
        assert aggregator._is_cache_valid("test_token") == True


class TestCacheManager:
    """Test cache manager"""
    
    @pytest.fixture
    async def cache_manager(self):
        """Create cache manager for testing"""
        # Use a mock Redis client for testing
        cache = CacheManager("test")
        cache.redis_client = AsyncMock()
        yield cache
        await cache.close()
    
    @pytest.mark.asyncio
    async def test_cache_manager_initialization(self, cache_manager):
        """Test cache manager initialization"""
        assert cache_manager.project_id == "test"
        assert cache_manager.key_prefix == "tokentracker_v2_test"
        assert cache_manager.default_ttl == 300
    
    def test_make_key(self, cache_manager):
        """Test cache key generation"""
        key = cache_manager._make_key("test_key")
        assert key == "tokentracker_v2_test:test_key"
    
    @pytest.mark.asyncio
    async def test_set_and_get(self, cache_manager):
        """Test cache set and get operations"""
        # Mock Redis operations
        cache_manager.redis_client.setex = AsyncMock(return_value=True)
        cache_manager.redis_client.get = AsyncMock(return_value=b'{"test": "data"}')
        
        # Test set
        result = await cache_manager.set("test_key", {"test": "data"})
        assert result == True
        
        # Test get
        result = await cache_manager.get("test_key", "dict")
        assert result == {"test": "data"}


if __name__ == "__main__":
    # Run basic tests
    print("🧪 Running basic data pipeline tests...")
    
    # Test validator
    validator = DataValidator("test")
    
    # Test valid address
    result = validator.validate_token_address("So11111111111111111111111111111111111111112")
    print(f"✅ Valid address test: {result.is_valid}")
    
    # Test invalid address
    result = validator.validate_token_address("invalid")
    print(f"❌ Invalid address test: {not result.is_valid}")
    
    print("🎉 Basic tests completed!")
