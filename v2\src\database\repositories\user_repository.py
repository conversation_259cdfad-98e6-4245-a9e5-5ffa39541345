"""
👤 User Repository

Repository for user data access operations.
"""

from typing import List, Optional, Dict, Any
from ..models.user import User
from .base_repository import BaseRepository


class UserRepository(BaseRepository):
    """Repository for user operations"""
    
    def __init__(self):
        super().__init__(User)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        return await self.find_one({"email": email})
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        return await self.find_one({"username": username})
    
    async def get_active_users(self) -> List[User]:
        """Get active users"""
        return await self.find_many({"is_active": True})
