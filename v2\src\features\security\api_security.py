"""
🛡️ API Security Manager

Comprehensive API security with rate limiting, request validation,
CORS protection, and security headers management.
"""

import asyncio
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from collections import defaultdict, deque
import ipaddress

from ...config.logging_config import get_logger
from ...shared.types import UserRole

logger = get_logger(__name__)


class RateLimitType(str, Enum):
    """Rate limit types"""
    PER_IP = "per_ip"
    PER_USER = "per_user"
    PER_ENDPOINT = "per_endpoint"
    GLOBAL = "global"


class SecurityThreatLevel(str, Enum):
    """Security threat levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RateLimiter:
    """
    ⏱️ Rate Limiter
    
    Advanced rate limiting with multiple strategies and threat detection
    """
    
    def __init__(self):
        self.logger = logger
        
        # Rate limit configurations
        self.rate_limits = {
            # Per IP limits
            "ip_per_minute": 60,
            "ip_per_hour": 1000,
            "ip_per_day": 10000,
            
            # Per user limits (authenticated)
            "user_per_minute": 120,
            "user_per_hour": 2000,
            "user_per_day": 20000,
            
            # Per endpoint limits
            "auth_per_minute": 5,
            "trade_per_minute": 30,
            "analytics_per_minute": 100,
            
            # Global limits
            "global_per_second": 1000
        }
        
        # Rate limit storage (in production, use Redis)
        self.ip_requests: Dict[str, deque] = defaultdict(deque)
        self.user_requests: Dict[str, deque] = defaultdict(deque)
        self.endpoint_requests: Dict[str, deque] = defaultdict(deque)
        self.global_requests: deque = deque()
        
        # Blocked IPs and users
        self.blocked_ips: Dict[str, datetime] = {}
        self.blocked_users: Dict[str, datetime] = {}
        
        # Suspicious activity tracking
        self.suspicious_activity: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    
    async def check_rate_limit(
        self,
        ip_address: str,
        user_id: str = None,
        endpoint: str = None,
        user_role: UserRole = None
    ) -> Dict[str, Any]:
        """
        Check if request is within rate limits
        
        Args:
            ip_address: Client IP address
            user_id: User ID (if authenticated)
            endpoint: API endpoint
            user_role: User role for role-based limits
            
        Returns:
            Rate limit check result
        """
        try:
            current_time = time.time()
            
            # Check if IP is blocked
            if ip_address in self.blocked_ips:
                if datetime.utcnow() < self.blocked_ips[ip_address]:
                    return {
                        "allowed": False,
                        "reason": "IP address is temporarily blocked",
                        "retry_after": int((self.blocked_ips[ip_address] - datetime.utcnow()).total_seconds())
                    }
                else:
                    # Unblock expired blocks
                    del self.blocked_ips[ip_address]
            
            # Check if user is blocked
            if user_id and user_id in self.blocked_users:
                if datetime.utcnow() < self.blocked_users[user_id]:
                    return {
                        "allowed": False,
                        "reason": "User is temporarily blocked",
                        "retry_after": int((self.blocked_users[user_id] - datetime.utcnow()).total_seconds())
                    }
                else:
                    del self.blocked_users[user_id]
            
            # Check global rate limit
            global_check = self._check_global_limit(current_time)
            if not global_check["allowed"]:
                return global_check
            
            # Check IP rate limits
            ip_check = self._check_ip_limits(ip_address, current_time)
            if not ip_check["allowed"]:
                return ip_check
            
            # Check user rate limits (if authenticated)
            if user_id:
                user_check = self._check_user_limits(user_id, current_time, user_role)
                if not user_check["allowed"]:
                    return user_check
            
            # Check endpoint-specific limits
            if endpoint:
                endpoint_check = self._check_endpoint_limits(endpoint, current_time)
                if not endpoint_check["allowed"]:
                    return endpoint_check
            
            # Record the request
            self._record_request(ip_address, user_id, endpoint, current_time)
            
            return {"allowed": True}
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {str(e)}")
            return {"allowed": True}  # Fail open for availability
    
    def _check_global_limit(self, current_time: float) -> Dict[str, Any]:
        """Check global rate limit"""
        try:
            # Clean old requests (older than 1 second)
            while self.global_requests and current_time - self.global_requests[0] > 1:
                self.global_requests.popleft()
            
            if len(self.global_requests) >= self.rate_limits["global_per_second"]:
                return {
                    "allowed": False,
                    "reason": "Global rate limit exceeded",
                    "retry_after": 1
                }
            
            return {"allowed": True}
            
        except Exception as e:
            self.logger.error(f"Error checking global limit: {str(e)}")
            return {"allowed": True}
    
    def _check_ip_limits(self, ip_address: str, current_time: float) -> Dict[str, Any]:
        """Check IP-based rate limits"""
        try:
            ip_requests = self.ip_requests[ip_address]
            
            # Clean old requests
            while ip_requests and current_time - ip_requests[0] > 86400:  # 24 hours
                ip_requests.popleft()
            
            # Count requests in different time windows
            minute_requests = sum(1 for req_time in ip_requests if current_time - req_time <= 60)
            hour_requests = sum(1 for req_time in ip_requests if current_time - req_time <= 3600)
            day_requests = len(ip_requests)
            
            # Check limits
            if minute_requests >= self.rate_limits["ip_per_minute"]:
                self._record_suspicious_activity(ip_address, "rate_limit_exceeded", "minute")
                return {
                    "allowed": False,
                    "reason": "IP rate limit exceeded (per minute)",
                    "retry_after": 60
                }
            
            if hour_requests >= self.rate_limits["ip_per_hour"]:
                self._record_suspicious_activity(ip_address, "rate_limit_exceeded", "hour")
                return {
                    "allowed": False,
                    "reason": "IP rate limit exceeded (per hour)",
                    "retry_after": 3600
                }
            
            if day_requests >= self.rate_limits["ip_per_day"]:
                # Block IP for 24 hours
                self.blocked_ips[ip_address] = datetime.utcnow() + timedelta(hours=24)
                self._record_suspicious_activity(ip_address, "ip_blocked", "day_limit_exceeded")
                return {
                    "allowed": False,
                    "reason": "IP rate limit exceeded (per day) - IP blocked",
                    "retry_after": 86400
                }
            
            return {"allowed": True}
            
        except Exception as e:
            self.logger.error(f"Error checking IP limits: {str(e)}")
            return {"allowed": True}
    
    def _check_user_limits(self, user_id: str, current_time: float, user_role: UserRole) -> Dict[str, Any]:
        """Check user-based rate limits"""
        try:
            user_requests = self.user_requests[user_id]
            
            # Clean old requests
            while user_requests and current_time - user_requests[0] > 86400:
                user_requests.popleft()
            
            # Adjust limits based on user role
            role_multiplier = {
                UserRole.ADMIN: 5.0,
                UserRole.PREMIUM: 2.0,
                UserRole.USER: 1.0,
                UserRole.VIEWER: 0.5
            }.get(user_role, 1.0)
            
            minute_limit = int(self.rate_limits["user_per_minute"] * role_multiplier)
            hour_limit = int(self.rate_limits["user_per_hour"] * role_multiplier)
            day_limit = int(self.rate_limits["user_per_day"] * role_multiplier)
            
            # Count requests
            minute_requests = sum(1 for req_time in user_requests if current_time - req_time <= 60)
            hour_requests = sum(1 for req_time in user_requests if current_time - req_time <= 3600)
            day_requests = len(user_requests)
            
            # Check limits
            if minute_requests >= minute_limit:
                return {
                    "allowed": False,
                    "reason": "User rate limit exceeded (per minute)",
                    "retry_after": 60
                }
            
            if hour_requests >= hour_limit:
                return {
                    "allowed": False,
                    "reason": "User rate limit exceeded (per hour)",
                    "retry_after": 3600
                }
            
            if day_requests >= day_limit:
                # Block user for 24 hours
                self.blocked_users[user_id] = datetime.utcnow() + timedelta(hours=24)
                return {
                    "allowed": False,
                    "reason": "User rate limit exceeded (per day) - User blocked",
                    "retry_after": 86400
                }
            
            return {"allowed": True}
            
        except Exception as e:
            self.logger.error(f"Error checking user limits: {str(e)}")
            return {"allowed": True}
    
    def _check_endpoint_limits(self, endpoint: str, current_time: float) -> Dict[str, Any]:
        """Check endpoint-specific rate limits"""
        try:
            endpoint_requests = self.endpoint_requests[endpoint]
            
            # Clean old requests
            while endpoint_requests and current_time - endpoint_requests[0] > 60:
                endpoint_requests.popleft()
            
            # Determine endpoint limit
            if "auth" in endpoint.lower():
                limit = self.rate_limits["auth_per_minute"]
            elif "trade" in endpoint.lower():
                limit = self.rate_limits["trade_per_minute"]
            elif "analytics" in endpoint.lower():
                limit = self.rate_limits["analytics_per_minute"]
            else:
                limit = 60  # Default limit
            
            if len(endpoint_requests) >= limit:
                return {
                    "allowed": False,
                    "reason": f"Endpoint rate limit exceeded: {endpoint}",
                    "retry_after": 60
                }
            
            return {"allowed": True}
            
        except Exception as e:
            self.logger.error(f"Error checking endpoint limits: {str(e)}")
            return {"allowed": True}
    
    def _record_request(self, ip_address: str, user_id: str, endpoint: str, current_time: float):
        """Record request for rate limiting"""
        try:
            # Record global request
            self.global_requests.append(current_time)
            
            # Record IP request
            self.ip_requests[ip_address].append(current_time)
            
            # Record user request
            if user_id:
                self.user_requests[user_id].append(current_time)
            
            # Record endpoint request
            if endpoint:
                self.endpoint_requests[endpoint].append(current_time)
            
        except Exception as e:
            self.logger.error(f"Error recording request: {str(e)}")
    
    def _record_suspicious_activity(self, identifier: str, activity_type: str, details: str):
        """Record suspicious activity"""
        try:
            activity = {
                "timestamp": datetime.utcnow(),
                "type": activity_type,
                "details": details
            }
            
            self.suspicious_activity[identifier].append(activity)
            
            # Keep only recent activities (last 24 hours)
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            self.suspicious_activity[identifier] = [
                act for act in self.suspicious_activity[identifier]
                if act["timestamp"] > cutoff_time
            ]
            
            self.logger.warning(f"Suspicious activity recorded: {identifier} - {activity_type}: {details}")
            
        except Exception as e:
            self.logger.error(f"Error recording suspicious activity: {str(e)}")


class APISecurityManager:
    """
    🛡️ API Security Manager
    
    Comprehensive API security management including:
    - Request validation and sanitization
    - CORS protection
    - Security headers
    - Input validation
    - SQL injection prevention
    - XSS protection
    """
    
    def __init__(self):
        self.logger = logger
        self.rate_limiter = RateLimiter()
        
        # Security configurations
        self.allowed_origins = [
            "http://localhost:3000",
            "http://localhost:8000",
            "https://tokentracker.app"
        ]
        
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        # Blocked patterns for input validation
        self.blocked_patterns = [
            r"<script[^>]*>.*?</script>",  # XSS
            r"javascript:",  # XSS
            r"on\w+\s*=",  # Event handlers
            r"union\s+select",  # SQL injection
            r"drop\s+table",  # SQL injection
            r"delete\s+from",  # SQL injection
            r"insert\s+into",  # SQL injection
            r"update\s+.*\s+set",  # SQL injection
        ]
    
    async def validate_request(
        self,
        request_data: Dict[str, Any],
        ip_address: str,
        user_agent: str = None
    ) -> Dict[str, Any]:
        """
        Validate incoming request for security threats
        
        Args:
            request_data: Request data to validate
            ip_address: Client IP address
            user_agent: User agent string
            
        Returns:
            Validation result
        """
        try:
            # Check for malicious IP addresses
            ip_check = self._check_ip_reputation(ip_address)
            if not ip_check["safe"]:
                return {
                    "valid": False,
                    "threat_level": SecurityThreatLevel.HIGH,
                    "reason": ip_check["reason"]
                }
            
            # Validate input data
            input_validation = self._validate_input_data(request_data)
            if not input_validation["valid"]:
                return {
                    "valid": False,
                    "threat_level": SecurityThreatLevel.MEDIUM,
                    "reason": input_validation["reason"]
                }
            
            # Check user agent
            if user_agent:
                ua_check = self._validate_user_agent(user_agent)
                if not ua_check["valid"]:
                    return {
                        "valid": False,
                        "threat_level": SecurityThreatLevel.LOW,
                        "reason": ua_check["reason"]
                    }
            
            return {
                "valid": True,
                "threat_level": SecurityThreatLevel.LOW
            }
            
        except Exception as e:
            self.logger.error(f"Error validating request: {str(e)}")
            return {
                "valid": False,
                "threat_level": SecurityThreatLevel.MEDIUM,
                "reason": "Request validation failed"
            }

    def _check_ip_reputation(self, ip_address: str) -> Dict[str, Any]:
        """Check IP address reputation"""
        try:
            # Parse IP address
            try:
                ip = ipaddress.ip_address(ip_address)
            except ValueError:
                return {"safe": False, "reason": "Invalid IP address format"}

            # Check for private/local IPs (allow in development)
            if ip.is_private or ip.is_loopback:
                return {"safe": True}

            # Check against known malicious IP ranges (simplified)
            # In production, integrate with threat intelligence feeds
            malicious_ranges = [
                "*********/24",  # Example range
                "************/24"  # Example range
            ]

            for range_str in malicious_ranges:
                if ip in ipaddress.ip_network(range_str):
                    return {"safe": False, "reason": "IP address in malicious range"}

            return {"safe": True}

        except Exception as e:
            self.logger.error(f"Error checking IP reputation: {str(e)}")
            return {"safe": True}  # Fail open

    def _validate_input_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate input data for malicious content"""
        try:
            import re

            def check_value(value):
                if isinstance(value, str):
                    # Check for malicious patterns
                    for pattern in self.blocked_patterns:
                        if re.search(pattern, value, re.IGNORECASE):
                            return False, f"Blocked pattern detected: {pattern}"

                    # Check for excessive length
                    if len(value) > 10000:
                        return False, "Input too long"

                elif isinstance(value, dict):
                    for k, v in value.items():
                        valid, reason = check_value(k)
                        if not valid:
                            return False, reason
                        valid, reason = check_value(v)
                        if not valid:
                            return False, reason

                elif isinstance(value, list):
                    for item in value:
                        valid, reason = check_value(item)
                        if not valid:
                            return False, reason

                return True, None

            valid, reason = check_value(data)

            return {
                "valid": valid,
                "reason": reason or "Input validation passed"
            }

        except Exception as e:
            self.logger.error(f"Error validating input data: {str(e)}")
            return {"valid": True}  # Fail open

    def _validate_user_agent(self, user_agent: str) -> Dict[str, Any]:
        """Validate user agent string"""
        try:
            # Check for suspicious user agents
            suspicious_patterns = [
                r"bot",
                r"crawler",
                r"spider",
                r"scraper",
                r"curl",
                r"wget",
                r"python-requests"
            ]

            import re
            for pattern in suspicious_patterns:
                if re.search(pattern, user_agent, re.IGNORECASE):
                    return {
                        "valid": False,
                        "reason": f"Suspicious user agent: {pattern}"
                    }

            # Check for empty or very short user agents
            if not user_agent or len(user_agent) < 10:
                return {
                    "valid": False,
                    "reason": "Invalid user agent"
                }

            return {"valid": True}

        except Exception as e:
            self.logger.error(f"Error validating user agent: {str(e)}")
            return {"valid": True}

    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers for responses"""
        return self.security_headers.copy()

    def is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed for CORS"""
        try:
            return origin in self.allowed_origins
        except Exception as e:
            self.logger.error(f"Error checking origin: {str(e)}")
            return False

    def sanitize_input(self, data: Any) -> Any:
        """Sanitize input data"""
        try:
            if isinstance(data, str):
                # Remove potentially dangerous characters
                import html
                sanitized = html.escape(data)

                # Remove null bytes
                sanitized = sanitized.replace('\x00', '')

                # Limit length
                if len(sanitized) > 10000:
                    sanitized = sanitized[:10000]

                return sanitized

            elif isinstance(data, dict):
                return {k: self.sanitize_input(v) for k, v in data.items()}

            elif isinstance(data, list):
                return [self.sanitize_input(item) for item in data]

            else:
                return data

        except Exception as e:
            self.logger.error(f"Error sanitizing input: {str(e)}")
            return data

    async def log_security_event(
        self,
        event_type: str,
        ip_address: str,
        user_id: str = None,
        details: Dict[str, Any] = None
    ):
        """Log security events for monitoring"""
        try:
            security_event = {
                "timestamp": datetime.utcnow(),
                "event_type": event_type,
                "ip_address": ip_address,
                "user_id": user_id,
                "details": details or {}
            }

            # In production, send to security monitoring system
            self.logger.warning(f"Security event: {event_type} from {ip_address}")

        except Exception as e:
            self.logger.error(f"Error logging security event: {str(e)}")
