"""
🔐 Security API Routes

FastAPI routes for security features including authentication, user management,
data protection, and compliance.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Request, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...shared.types import UserRole
from .auth_manager import Auth<PERSON>anager, JWTManager
from .user_manager import UserManager, PermissionType, ResourceType
from .api_security import APISecurityManager
from .data_protection import DataProtectionManager, DataClassification
from .compliance import ComplianceManager, GDPRManager, ComplianceRegulation

logger = get_logger(__name__)
router = APIRouter()
security = HTTPBearer()

# Initialize security components
auth_manager = AuthManager()
jwt_manager = JWTManager()
user_manager = UserManager()
api_security = APISecurityManager()
data_protection = DataProtectionManager()
compliance_manager = ComplianceManager()
gdpr_manager = GDPRManager()


# Request/Response Models
class RegisterRequest(BaseModel):
    email: str = Field(..., description="User email")
    password: str = Field(..., description="User password")
    username: str = Field(..., description="Username")
    role: UserRole = Field(UserRole.USER, description="User role")


class LoginRequest(BaseModel):
    email: str = Field(..., description="User email")
    password: str = Field(..., description="User password")


class PasswordResetRequest(BaseModel):
    email: str = Field(..., description="User email")


class PasswordChangeRequest(BaseModel):
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., description="New password")


class UpdateProfileRequest(BaseModel):
    username: Optional[str] = Field(None, description="New username")
    email: Optional[str] = Field(None, description="New email")


class GDPRRequest(BaseModel):
    request_type: str = Field(..., description="GDPR request type")
    details: Optional[Dict[str, Any]] = Field(None, description="Request details")


# Dependency functions
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current authenticated user"""
    try:
        token_data = jwt_manager.verify_token(credentials.credentials)
        
        if not token_data["valid"]:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        return {
            "user_id": token_data["user_id"],
            "user_role": UserRole(token_data["user_role"]),
            "token": credentials.credentials
        }
        
    except Exception as e:
        logger.error(f"Error getting current user: {str(e)}")
        raise HTTPException(status_code=401, detail="Authentication failed")


async def check_rate_limit(request: Request):
    """Check rate limits for requests"""
    try:
        client_ip = request.client.host
        user_agent = request.headers.get("user-agent")
        
        # Get user info if authenticated
        user_id = None
        user_role = None
        auth_header = request.headers.get("authorization")
        
        if auth_header and auth_header.startswith("Bearer "):
            token = auth_header.split(" ")[1]
            token_data = jwt_manager.verify_token(token)
            if token_data["valid"]:
                user_id = token_data["user_id"]
                user_role = UserRole(token_data["user_role"])
        
        # Check rate limit
        rate_limit_result = await api_security.rate_limiter.check_rate_limit(
            ip_address=client_ip,
            user_id=user_id,
            endpoint=str(request.url.path),
            user_role=user_role
        )
        
        if not rate_limit_result["allowed"]:
            raise HTTPException(
                status_code=429,
                detail=rate_limit_result["reason"],
                headers={"Retry-After": str(rate_limit_result.get("retry_after", 60))}
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking rate limit: {str(e)}")
        # Fail open for availability


# Authentication Routes
@router.post("/auth/register", response_model=Dict[str, Any], dependencies=[Depends(check_rate_limit)])
async def register_user(request: RegisterRequest, req: Request):
    """Register a new user"""
    try:
        # Validate request
        validation_result = await api_security.validate_request(
            request_data=request.dict(),
            ip_address=req.client.host,
            user_agent=req.headers.get("user-agent")
        )
        
        if not validation_result["valid"]:
            raise HTTPException(status_code=400, detail=validation_result["reason"])
        
        # Register user
        result = await auth_manager.register_user(
            email=request.email,
            password=request.password,
            username=request.username,
            role=request.role
        )
        
        if result["success"]:
            # Log audit event
            await compliance_manager.log_audit_event(
                event_type="user_registration",
                user_id=result["user_id"],
                details={"ip_address": req.client.host}
            )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error registering user: {str(e)}")
        raise HTTPException(status_code=500, detail="Registration failed")


@router.post("/auth/login", response_model=Dict[str, Any], dependencies=[Depends(check_rate_limit)])
async def login_user(request: LoginRequest, req: Request):
    """Login user"""
    try:
        result = await auth_manager.login_user(
            email=request.email,
            password=request.password,
            ip_address=req.client.host
        )
        
        if result["success"]:
            # Log audit event
            await compliance_manager.log_audit_event(
                event_type="user_login",
                user_id=result["user"]["id"],
                details={"ip_address": req.client.host}
            )
        
        return result
        
    except Exception as e:
        logger.error(f"Error logging in user: {str(e)}")
        raise HTTPException(status_code=500, detail="Login failed")


@router.post("/auth/logout", response_model=Dict[str, Any])
async def logout_user(current_user: Dict = Depends(get_current_user)):
    """Logout user"""
    try:
        result = await auth_manager.logout_user(
            access_token=current_user["token"]
        )
        
        # Log audit event
        await compliance_manager.log_audit_event(
            event_type="user_logout",
            user_id=current_user["user_id"]
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error logging out user: {str(e)}")
        raise HTTPException(status_code=500, detail="Logout failed")


@router.post("/auth/password-reset-request", response_model=Dict[str, Any])
async def request_password_reset(request: PasswordResetRequest):
    """Request password reset"""
    try:
        result = await auth_manager.reset_password_request(request.email)
        return result
        
    except Exception as e:
        logger.error(f"Error requesting password reset: {str(e)}")
        raise HTTPException(status_code=500, detail="Password reset request failed")


@router.post("/auth/verify-email", response_model=Dict[str, Any])
async def verify_email(token: str):
    """Verify user email"""
    try:
        result = await auth_manager.verify_email(token)
        return result
        
    except Exception as e:
        logger.error(f"Error verifying email: {str(e)}")
        raise HTTPException(status_code=500, detail="Email verification failed")


# User Management Routes
@router.get("/users/profile", response_model=Dict[str, Any])
async def get_user_profile(current_user: Dict = Depends(get_current_user)):
    """Get user profile"""
    try:
        profile = await user_manager.get_user_profile(current_user["user_id"])
        
        if not profile:
            raise HTTPException(status_code=404, detail="User profile not found")
        
        # Log audit event
        await compliance_manager.log_audit_event(
            event_type="data_access",
            user_id=current_user["user_id"],
            resource_id="user_profile"
        )
        
        return {"success": True, "profile": profile}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get user profile")


@router.put("/users/profile", response_model=Dict[str, Any])
async def update_user_profile(
    request: UpdateProfileRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Update user profile"""
    try:
        result = await user_manager.update_user_profile(
            user_id=current_user["user_id"],
            updates=request.dict(exclude_unset=True)
        )
        
        if result["success"]:
            # Log audit event
            await compliance_manager.log_audit_event(
                event_type="data_modification",
                user_id=current_user["user_id"],
                resource_id="user_profile",
                details={"updated_fields": result.get("updated_fields", [])}
            )
        
        return result
        
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        raise HTTPException(status_code=500, detail="Profile update failed")


@router.post("/users/change-password", response_model=Dict[str, Any])
async def change_password(
    request: PasswordChangeRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Change user password"""
    try:
        result = await auth_manager.change_password(
            user_id=current_user["user_id"],
            current_password=request.current_password,
            new_password=request.new_password
        )
        
        if result["success"]:
            # Log audit event
            await compliance_manager.log_audit_event(
                event_type="security_event",
                user_id=current_user["user_id"],
                details={"event": "password_changed"}
            )
        
        return result
        
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        raise HTTPException(status_code=500, detail="Password change failed")


# Admin Routes
@router.get("/admin/users", response_model=Dict[str, Any])
async def list_users(
    current_user: Dict = Depends(get_current_user),
    limit: int = 100,
    offset: int = 0
):
    """List users (admin only)"""
    try:
        # Check admin permissions
        if current_user["user_role"] != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Admin access required")
        
        result = await user_manager.list_users(
            admin_user_id=current_user["user_id"],
            limit=limit,
            offset=offset
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing users: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to list users")


# GDPR Compliance Routes
@router.post("/gdpr/request", response_model=Dict[str, Any])
async def submit_gdpr_request(
    request: GDPRRequest,
    current_user: Dict = Depends(get_current_user)
):
    """Submit GDPR data subject request"""
    try:
        result = await gdpr_manager.handle_data_subject_request(
            user_id=current_user["user_id"],
            request_type=request.request_type,
            details=request.details
        )
        
        # Log audit event
        await compliance_manager.log_audit_event(
            event_type="privacy_request",
            user_id=current_user["user_id"],
            details={
                "request_type": request.request_type,
                "gdpr_request": True
            }
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Error processing GDPR request: {str(e)}")
        raise HTTPException(status_code=500, detail="GDPR request processing failed")


@router.get("/compliance/report/{regulation}", response_model=Dict[str, Any])
async def generate_compliance_report(
    regulation: ComplianceRegulation,
    start_date: datetime,
    end_date: datetime,
    current_user: Dict = Depends(get_current_user)
):
    """Generate compliance report (admin only)"""
    try:
        # Check admin permissions
        if current_user["user_role"] != UserRole.ADMIN:
            raise HTTPException(status_code=403, detail="Admin access required")
        
        report = await compliance_manager.generate_compliance_report(
            regulation=regulation,
            start_date=start_date,
            end_date=end_date
        )
        
        return {"success": True, "report": report}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating compliance report: {str(e)}")
        raise HTTPException(status_code=500, detail="Report generation failed")


# Data Protection Routes
@router.post("/data/protect", response_model=Dict[str, Any])
async def protect_data(
    data: Dict[str, Any],
    classification: DataClassification,
    current_user: Dict = Depends(get_current_user)
):
    """Protect sensitive data"""
    try:
        protected_data = await data_protection.protect_sensitive_data(
            data=data,
            classification=classification
        )
        
        return {"success": True, "protected_data": protected_data}
        
    except Exception as e:
        logger.error(f"Error protecting data: {str(e)}")
        raise HTTPException(status_code=500, detail="Data protection failed")


# Security Status Route
@router.get("/security/status", response_model=Dict[str, Any])
async def get_security_status(current_user: Dict = Depends(get_current_user)):
    """Get security status and metrics"""
    try:
        # Check admin permissions for detailed status
        if current_user["user_role"] != UserRole.ADMIN:
            return {
                "success": True,
                "status": "operational",
                "user_permissions": user_manager.get_user_permissions(current_user["user_role"])
            }
        
        # Detailed status for admins
        status = {
            "authentication_system": "operational",
            "rate_limiting": "active",
            "data_protection": "enabled",
            "compliance_monitoring": "active",
            "audit_logging": "enabled",
            "security_headers": "configured",
            "encryption": "aes_256_enabled"
        }
        
        return {"success": True, "security_status": status}
        
    except Exception as e:
        logger.error(f"Error getting security status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get security status")
