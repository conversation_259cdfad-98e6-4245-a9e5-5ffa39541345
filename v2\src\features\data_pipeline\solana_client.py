"""
⚡ Solana RPC Client

Solana blockchain client for token account information, transaction history,
real-time updates via WebSocket, and block monitoring following V2 architecture patterns.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Callable
from decimal import Decimal
import httpx
import websockets
import json
from solana.rpc.async_api import AsyncClient
from solana.rpc.websocket_api import connect
from solders.pubkey import Pubkey as PublicKey
from solana.rpc.types import TokenAccountOpts
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import APIResponse, TokenData, TokenMetrics

logger = get_logger(__name__)


class SolanaClient:
    """
    ⚡ Solana RPC Client for blockchain data
    
    Provides comprehensive Solana blockchain integration with:
    - Token account information
    - Transaction history
    - Real-time updates via WebSocket
    - Block and slot monitoring
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        self.rpc_url = self.settings.solana_rpc_url
        self.ws_url = self.settings.solana_ws_url
        
        # Rate limiting
        self.rate_limit_requests = 100  # requests per minute
        self.rate_limit_window = 60  # seconds
        self.request_timestamps = []
        
        # Solana RPC client with error handling
        try:
            self.client = AsyncClient(self.rpc_url)
        except Exception as e:
            logger.warning(f"Failed to initialize Solana client: {str(e)}")
            self.client = None
        
        # WebSocket connections
        self.ws_connections = {}
        self.ws_subscriptions = {}
        
        logger.info(
            "Solana client initialized",
            project_id=self.project_id,
            rpc_url=self.rpc_url,
            ws_url=self.ws_url
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close RPC client and WebSocket connections"""
        try:
            # Close WebSocket connections
            for ws_type, ws in self.ws_connections.items():
                if ws:
                    await ws.close()
            
            self.ws_connections.clear()
            self.ws_subscriptions.clear()
            
            # Close RPC client
            await self.client.close()
            
            logger.info("Solana client closed successfully")
        except Exception as e:
            logger.error(
                "Error closing Solana client",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        now = time.time()
        
        # Remove old timestamps outside the window
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < self.rate_limit_window
        ]
        
        # Check if we're within limits
        if len(self.request_timestamps) >= self.rate_limit_requests:
            logger.warning(
                "Rate limit reached",
                current_requests=len(self.request_timestamps),
                limit=self.rate_limit_requests,
                window_seconds=self.rate_limit_window
            )
            return False
        
        # Add current timestamp
        self.request_timestamps.append(now)
        return True
    
    async def _wait_for_rate_limit(self):
        """Wait if rate limit is exceeded"""
        if not self._check_rate_limit():
            # Calculate wait time
            oldest_timestamp = min(self.request_timestamps)
            wait_time = self.rate_limit_window - (time.time() - oldest_timestamp)
            
            if wait_time > 0:
                logger.info(
                    "Waiting for rate limit reset",
                    wait_seconds=wait_time
                )
                await asyncio.sleep(wait_time)
    
    async def get_token_account_info(self, token_address: str, owner_address: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        🪙 Get token account information

        Args:
            token_address: Token mint address
            owner_address: Optional owner address filter

        Returns:
            Token account information
        """
        try:
            if not self.client:
                logger.warning("Solana client not initialized")
                return None

            await self._wait_for_rate_limit()
            
            logger.info(
                "Fetching token account info",
                token_address=token_address,
                owner_address=owner_address
            )
            
            token_pubkey = PublicKey(token_address)
            
            if owner_address:
                # Get token accounts by owner
                owner_pubkey = PublicKey(owner_address)
                response = await self.client.get_token_accounts_by_owner(
                    owner_pubkey,
                    TokenAccountOpts(mint=token_pubkey)
                )
            else:
                # Get token supply and info
                supply_response = await self.client.get_token_supply(token_pubkey)
                account_response = await self.client.get_account_info(token_pubkey)
                
                response = {
                    "supply": supply_response,
                    "account": account_response
                }
            
            if response:
                logger.info(
                    "Token account info fetched successfully",
                    token_address=token_address
                )
                return response.value if hasattr(response, 'value') else response
            else:
                logger.warning(
                    "No token account info found",
                    token_address=token_address
                )
                return None
            
        except Exception as e:
            logger.error(
                "Error fetching token account info",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_transaction_history(self, address: str, limit: int = 100, before: Optional[str] = None) -> Optional[List[Dict[str, Any]]]:
        """
        📜 Get transaction history for an address
        
        Args:
            address: Account address
            limit: Number of transactions to fetch
            before: Fetch transactions before this signature
            
        Returns:
            List of transaction information
        """
        try:
            await self._wait_for_rate_limit()
            
            logger.info(
                "Fetching transaction history",
                address=address,
                limit=limit
            )
            
            pubkey = PublicKey(address)
            
            response = await self.client.get_signatures_for_address(
                pubkey,
                limit=limit,
                before=before
            )
            
            if response and response.value:
                transactions = []
                
                # Get detailed transaction info for each signature
                for sig_info in response.value[:10]:  # Limit detailed fetches
                    try:
                        tx_response = await self.client.get_transaction(
                            sig_info.signature,
                            encoding="json"
                        )
                        
                        if tx_response and tx_response.value:
                            transactions.append({
                                "signature": sig_info.signature,
                                "slot": sig_info.slot,
                                "block_time": sig_info.block_time,
                                "transaction": tx_response.value
                            })
                    except Exception as tx_error:
                        logger.warning(
                            "Error fetching transaction details",
                            signature=sig_info.signature,
                            error=str(tx_error)
                        )
                        continue
                
                logger.info(
                    "Transaction history fetched successfully",
                    address=address,
                    transaction_count=len(transactions)
                )
                
                return transactions
            else:
                logger.warning(
                    "No transaction history found",
                    address=address
                )
                return None
            
        except Exception as e:
            logger.error(
                "Error fetching transaction history",
                address=address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_current_slot(self) -> Optional[int]:
        """
        🎰 Get current slot number
        
        Returns:
            Current slot number
        """
        try:
            await self._wait_for_rate_limit()
            
            response = await self.client.get_slot()
            
            if response:
                slot = response.value if hasattr(response, 'value') else response
                logger.debug("Current slot fetched", slot=slot)
                return slot
            else:
                logger.warning("Failed to fetch current slot")
                return None
            
        except Exception as e:
            logger.error(
                "Error fetching current slot",
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_block_info(self, slot: int) -> Optional[Dict[str, Any]]:
        """
        🧱 Get block information
        
        Args:
            slot: Slot number
            
        Returns:
            Block information
        """
        try:
            await self._wait_for_rate_limit()
            
            logger.info("Fetching block info", slot=slot)
            
            response = await self.client.get_block(slot)
            
            if response and response.value:
                logger.info(
                    "Block info fetched successfully",
                    slot=slot,
                    transaction_count=len(response.value.transactions)
                )
                return response.value
            else:
                logger.warning("No block info found", slot=slot)
                return None
            
        except Exception as e:
            logger.error(
                "Error fetching block info",
                slot=slot,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def start_account_monitoring(self, account_address: str, callback: Callable = None) -> bool:
        """
        👁️ Start monitoring account changes via WebSocket

        Args:
            account_address: Account address to monitor
            callback: Callback function for account updates

        Returns:
            True if monitoring started successfully
        """
        try:
            logger.info(
                "Starting account monitoring",
                account_address=account_address
            )

            # Connect to WebSocket
            ws = await connect(self.ws_url)
            self.ws_connections[f"account_{account_address}"] = ws

            # Subscribe to account changes
            pubkey = PublicKey(account_address)
            subscription_id = await ws.account_subscribe(pubkey)
            self.ws_subscriptions[f"account_{account_address}"] = subscription_id

            # Start listening task
            asyncio.create_task(
                self._listen_account_updates(ws, account_address, callback)
            )

            logger.info(
                "Account monitoring started successfully",
                account_address=account_address,
                subscription_id=subscription_id
            )

            return True

        except Exception as e:
            logger.error(
                "Error starting account monitoring",
                account_address=account_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return False

    async def _listen_account_updates(self, ws, account_address: str, callback: Callable = None):
        """
        👂 Listen for account updates

        Args:
            ws: WebSocket connection
            account_address: Account address being monitored
            callback: Callback function for updates
        """
        logger.info(
            "Starting account update listener",
            account_address=account_address
        )

        try:
            async for message in ws:
                try:
                    if callback:
                        await callback(account_address, message)

                    logger.debug(
                        "Account update received",
                        account_address=account_address
                    )

                except Exception as callback_error:
                    logger.error(
                        "Error in account update callback",
                        account_address=account_address,
                        error=str(callback_error)
                    )

        except asyncio.CancelledError:
            logger.info(
                "Account monitoring cancelled",
                account_address=account_address
            )
        except Exception as e:
            logger.error(
                "Error in account update listener",
                account_address=account_address,
                error=str(e),
                error_type=type(e).__name__
            )

    async def start_slot_monitoring(self, callback: Callable = None) -> bool:
        """
        🎰 Start monitoring slot updates via WebSocket

        Args:
            callback: Callback function for slot updates

        Returns:
            True if monitoring started successfully
        """
        try:
            logger.info("Starting slot monitoring")

            # Connect to WebSocket
            ws = await connect(self.ws_url)
            self.ws_connections["slot"] = ws

            # Subscribe to slot updates
            subscription_id = await ws.slot_subscribe()
            self.ws_subscriptions["slot"] = subscription_id

            # Start listening task
            asyncio.create_task(
                self._listen_slot_updates(ws, callback)
            )

            logger.info(
                "Slot monitoring started successfully",
                subscription_id=subscription_id
            )

            return True

        except Exception as e:
            logger.error(
                "Error starting slot monitoring",
                error=str(e),
                error_type=type(e).__name__
            )
            return False

    async def _listen_slot_updates(self, ws, callback: Callable = None):
        """
        👂 Listen for slot updates

        Args:
            ws: WebSocket connection
            callback: Callback function for updates
        """
        logger.info("Starting slot update listener")

        try:
            async for message in ws:
                try:
                    if callback:
                        await callback(message)

                    logger.debug("Slot update received")

                except Exception as callback_error:
                    logger.error(
                        "Error in slot update callback",
                        error=str(callback_error)
                    )

        except asyncio.CancelledError:
            logger.info("Slot monitoring cancelled")
        except Exception as e:
            logger.error(
                "Error in slot update listener",
                error=str(e),
                error_type=type(e).__name__
            )

    async def stop_monitoring(self, monitor_type: str = None, address: str = None):
        """
        ⏹️ Stop WebSocket monitoring

        Args:
            monitor_type: Type of monitoring to stop (account, slot, all)
            address: Specific address for account monitoring
        """
        try:
            if monitor_type == "all" or monitor_type is None:
                # Stop all monitoring
                for key, ws in self.ws_connections.items():
                    if ws:
                        await ws.close()

                self.ws_connections.clear()
                self.ws_subscriptions.clear()

                logger.info("All monitoring stopped")

            elif monitor_type == "account" and address:
                # Stop specific account monitoring
                key = f"account_{address}"
                ws = self.ws_connections.get(key)

                if ws:
                    await ws.close()
                    del self.ws_connections[key]

                    if key in self.ws_subscriptions:
                        del self.ws_subscriptions[key]

                    logger.info(
                        "Account monitoring stopped",
                        address=address
                    )

            elif monitor_type == "slot":
                # Stop slot monitoring
                ws = self.ws_connections.get("slot")

                if ws:
                    await ws.close()
                    del self.ws_connections["slot"]

                    if "slot" in self.ws_subscriptions:
                        del self.ws_subscriptions["slot"]

                    logger.info("Slot monitoring stopped")

        except Exception as e:
            logger.error(
                "Error stopping monitoring",
                monitor_type=monitor_type,
                address=address,
                error=str(e),
                error_type=type(e).__name__
            )

    async def health_check(self) -> bool:
        """
        🏥 Check Solana RPC health

        Returns:
            True if RPC is healthy
        """
        try:
            # Try to get current slot
            slot = await self.get_current_slot()

            if slot is not None:
                logger.info("Solana RPC health check passed", slot=slot)
                return True
            else:
                logger.warning("Solana RPC health check failed - no slot")
                return False

        except Exception as e:
            logger.error(
                "Solana RPC health check failed",
                error=str(e),
                error_type=type(e).__name__
            )
            return False
