"""
🚨 Alert Manager

Comprehensive alerting system with threshold-based alerts, anomaly detection,
alert escalation, and fatigue prevention for TokenTracker V2.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from enum import Enum
from dataclasses import dataclass, field

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import MONITORING_CONSTANTS

logger = get_logger(__name__)
settings = get_settings()


class AlertSeverity(str, Enum):
    """Alert severity levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


class AlertStatus(str, Enum):
    """Alert status"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ACKNOWLEDGED = "acknowledged"


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    description: str
    condition: str
    threshold: float
    severity: AlertSeverity
    enabled: bool = True
    cooldown_minutes: int = 15
    escalation_minutes: int = 60
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class Alert:
    """Alert instance"""
    id: str
    rule_name: str
    severity: AlertSeverity
    title: str
    description: str
    value: float
    threshold: float
    status: AlertStatus = AlertStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AlertManager:
    """
    🚨 Comprehensive alert management system
    """
    
    def __init__(self):
        self.settings = settings
        self.is_running = False
        
        # Alert storage
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # Alert suppression and rate limiting
        self.suppressed_alerts: Dict[str, datetime] = {}
        self.alert_counts: Dict[str, List[datetime]] = {}
        
        # Initialize default alert rules
        self._init_default_rules()
        
        logger.info("AlertManager initialized")
    
    def _init_default_rules(self):
        """Initialize default alert rules"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU usage exceeds threshold",
                condition="cpu_usage_percent > threshold",
                threshold=MONITORING_CONSTANTS.HIGH_CPU_PERCENT,
                severity=AlertSeverity.HIGH,
                cooldown_minutes=10
            ),
            AlertRule(
                name="high_memory_usage",
                description="Memory usage exceeds threshold",
                condition="memory_usage_percent > threshold",
                threshold=MONITORING_CONSTANTS.HIGH_MEMORY_PERCENT,
                severity=AlertSeverity.HIGH,
                cooldown_minutes=10
            ),
            AlertRule(
                name="high_disk_usage",
                description="Disk usage exceeds threshold",
                condition="disk_usage_percent > threshold",
                threshold=MONITORING_CONSTANTS.HIGH_DISK_PERCENT,
                severity=AlertSeverity.CRITICAL,
                cooldown_minutes=5
            ),
            AlertRule(
                name="high_error_rate",
                description="Error rate exceeds threshold",
                condition="error_rate_percent > threshold",
                threshold=MONITORING_CONSTANTS.MAX_ERROR_RATE_PERCENT,
                severity=AlertSeverity.HIGH,
                cooldown_minutes=15
            ),
            AlertRule(
                name="slow_response_time",
                description="API response time exceeds threshold",
                condition="avg_response_time_ms > threshold",
                threshold=MONITORING_CONSTANTS.MAX_RESPONSE_TIME_MS,
                severity=AlertSeverity.MEDIUM,
                cooldown_minutes=20
            ),
            AlertRule(
                name="database_connection_failed",
                description="Database connection failure",
                condition="database_healthy == false",
                threshold=0,
                severity=AlertSeverity.CRITICAL,
                cooldown_minutes=5
            ),
            AlertRule(
                name="external_api_failure",
                description="External API failure",
                condition="api_healthy == false",
                threshold=0,
                severity=AlertSeverity.HIGH,
                cooldown_minutes=10
            ),
            AlertRule(
                name="low_signal_accuracy",
                description="Signal accuracy below threshold",
                condition="signal_accuracy < threshold",
                threshold=0.7,
                severity=AlertSeverity.MEDIUM,
                cooldown_minutes=30
            ),
            AlertRule(
                name="low_trade_success_rate",
                description="Trade success rate below threshold",
                condition="trade_success_rate < threshold",
                threshold=0.6,
                severity=AlertSeverity.MEDIUM,
                cooldown_minutes=30
            )
        ]
        
        for rule in default_rules:
            self.alert_rules[rule.name] = rule
        
        logger.info(f"Initialized {len(default_rules)} default alert rules")
    
    async def start(self):
        """Start alert monitoring"""
        if self.is_running:
            logger.warning("AlertManager already running")
            return
        
        self.is_running = True
        logger.info("Starting AlertManager")
        
        # Start background alert processing
        asyncio.create_task(self._alert_processing_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """Stop alert monitoring"""
        self.is_running = False
        logger.info("AlertManager stopped")
    
    async def _alert_processing_loop(self):
        """Main alert processing loop"""
        while self.is_running:
            try:
                await self._process_alert_escalations()
                await self._cleanup_suppressed_alerts()
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(
                    "Error in alert processing loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self):
        """Cleanup old alerts and history"""
        while self.is_running:
            try:
                await self._cleanup_alert_history()
                await asyncio.sleep(3600)  # Cleanup every hour
                
            except Exception as e:
                logger.error(
                    "Error in cleanup loop",
                    error=str(e)
                )
                await asyncio.sleep(3600)
    
    async def check_metrics(self, metrics: Dict[str, Any]):
        """Check metrics against alert rules"""
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            try:
                should_alert = await self._evaluate_rule(rule, metrics)
                if should_alert:
                    await self._create_alert(rule, metrics)
                else:
                    await self._resolve_alert(rule_name)
                    
            except Exception as e:
                logger.error(
                    "Error checking alert rule",
                    rule_name=rule_name,
                    error=str(e)
                )
    
    async def _evaluate_rule(self, rule: AlertRule, metrics: Dict[str, Any]) -> bool:
        """Evaluate if alert rule should trigger"""
        try:
            # Simple condition evaluation
            if rule.condition == "cpu_usage_percent > threshold":
                return metrics.get('cpu_usage_percent', 0) > rule.threshold
            elif rule.condition == "memory_usage_percent > threshold":
                return metrics.get('memory_usage_percent', 0) > rule.threshold
            elif rule.condition == "disk_usage_percent > threshold":
                return metrics.get('disk_usage_percent', 0) > rule.threshold
            elif rule.condition == "error_rate_percent > threshold":
                return metrics.get('error_rate_percent', 0) > rule.threshold
            elif rule.condition == "avg_response_time_ms > threshold":
                return metrics.get('avg_response_time_ms', 0) > rule.threshold
            elif rule.condition == "database_healthy == false":
                return not metrics.get('database_healthy', True)
            elif rule.condition == "api_healthy == false":
                return not metrics.get('api_healthy', True)
            elif rule.condition == "signal_accuracy < threshold":
                return metrics.get('signal_accuracy', 1.0) < rule.threshold
            elif rule.condition == "trade_success_rate < threshold":
                return metrics.get('trade_success_rate', 1.0) < rule.threshold
            
            return False
            
        except Exception as e:
            logger.error(
                "Error evaluating alert rule",
                rule_name=rule.name,
                error=str(e)
            )
            return False
    
    async def _create_alert(self, rule: AlertRule, metrics: Dict[str, Any]):
        """Create new alert"""
        alert_id = f"{rule.name}_{int(time.time())}"
        
        # Check if alert is suppressed
        if self._is_alert_suppressed(rule.name):
            return
        
        # Check rate limiting
        if not self._check_rate_limit(rule.name):
            return
        
        # Get metric value
        metric_value = self._extract_metric_value(rule, metrics)
        
        alert = Alert(
            id=alert_id,
            rule_name=rule.name,
            severity=rule.severity,
            title=f"{rule.name.replace('_', ' ').title()}",
            description=f"{rule.description}. Current value: {metric_value}, Threshold: {rule.threshold}",
            value=metric_value,
            threshold=rule.threshold,
            tags=rule.tags.copy(),
            metadata={'metrics': metrics}
        )
        
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        # Send alert notification
        await self._send_alert_notification(alert)
        
        logger.warning(
            "Alert created",
            alert_id=alert_id,
            rule_name=rule.name,
            severity=rule.severity.value,
            value=metric_value,
            threshold=rule.threshold
        )
    
    async def _resolve_alert(self, rule_name: str):
        """Resolve active alerts for rule"""
        resolved_alerts = []
        
        for alert_id, alert in list(self.active_alerts.items()):
            if alert.rule_name == rule_name and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = datetime.utcnow()
                alert.updated_at = datetime.utcnow()
                
                resolved_alerts.append(alert)
                del self.active_alerts[alert_id]
        
        for alert in resolved_alerts:
            await self._send_resolution_notification(alert)
            logger.info(
                "Alert resolved",
                alert_id=alert.id,
                rule_name=rule_name
            )
    
    def _extract_metric_value(self, rule: AlertRule, metrics: Dict[str, Any]) -> float:
        """Extract metric value for alert"""
        if "cpu_usage_percent" in rule.condition:
            return metrics.get('cpu_usage_percent', 0)
        elif "memory_usage_percent" in rule.condition:
            return metrics.get('memory_usage_percent', 0)
        elif "disk_usage_percent" in rule.condition:
            return metrics.get('disk_usage_percent', 0)
        elif "error_rate_percent" in rule.condition:
            return metrics.get('error_rate_percent', 0)
        elif "avg_response_time_ms" in rule.condition:
            return metrics.get('avg_response_time_ms', 0)
        elif "signal_accuracy" in rule.condition:
            return metrics.get('signal_accuracy', 1.0)
        elif "trade_success_rate" in rule.condition:
            return metrics.get('trade_success_rate', 1.0)
        else:
            return 0.0
    
    def _is_alert_suppressed(self, rule_name: str) -> bool:
        """Check if alert is currently suppressed"""
        if rule_name in self.suppressed_alerts:
            suppressed_until = self.suppressed_alerts[rule_name]
            if datetime.utcnow() < suppressed_until:
                return True
            else:
                del self.suppressed_alerts[rule_name]
        return False
    
    def _check_rate_limit(self, rule_name: str) -> bool:
        """Check alert rate limiting"""
        now = datetime.utcnow()
        
        if rule_name not in self.alert_counts:
            self.alert_counts[rule_name] = []
        
        # Clean old timestamps
        cutoff = now - timedelta(hours=1)
        self.alert_counts[rule_name] = [
            ts for ts in self.alert_counts[rule_name] if ts > cutoff
        ]
        
        # Check if we're within limits (max 5 alerts per hour per rule)
        if len(self.alert_counts[rule_name]) >= 5:
            return False
        
        self.alert_counts[rule_name].append(now)
        return True
    
    async def _send_alert_notification(self, alert: Alert):
        """Send alert notification"""
        try:
            # This would integrate with notification system
            logger.info(
                "Alert notification sent",
                alert_id=alert.id,
                severity=alert.severity.value,
                title=alert.title
            )
        except Exception as e:
            logger.error(
                "Failed to send alert notification",
                alert_id=alert.id,
                error=str(e)
            )
    
    async def _send_resolution_notification(self, alert: Alert):
        """Send alert resolution notification"""
        try:
            # This would integrate with notification system
            logger.info(
                "Alert resolution notification sent",
                alert_id=alert.id,
                title=alert.title
            )
        except Exception as e:
            logger.error(
                "Failed to send resolution notification",
                alert_id=alert.id,
                error=str(e)
            )

    async def _process_alert_escalations(self):
        """Process alert escalations"""
        now = datetime.utcnow()

        for alert in self.active_alerts.values():
            if alert.status != AlertStatus.ACTIVE:
                continue

            rule = self.alert_rules.get(alert.rule_name)
            if not rule:
                continue

            # Check if alert should be escalated
            time_since_created = (now - alert.created_at).total_seconds() / 60
            if time_since_created >= rule.escalation_minutes:
                await self._escalate_alert(alert)

    async def _escalate_alert(self, alert: Alert):
        """Escalate alert to higher severity"""
        if alert.severity == AlertSeverity.LOW:
            alert.severity = AlertSeverity.MEDIUM
        elif alert.severity == AlertSeverity.MEDIUM:
            alert.severity = AlertSeverity.HIGH
        elif alert.severity == AlertSeverity.HIGH:
            alert.severity = AlertSeverity.CRITICAL

        alert.updated_at = datetime.utcnow()

        logger.warning(
            "Alert escalated",
            alert_id=alert.id,
            new_severity=alert.severity.value
        )

        await self._send_alert_notification(alert)

    async def _cleanup_suppressed_alerts(self):
        """Clean up expired suppressed alerts"""
        now = datetime.utcnow()
        expired = [
            rule_name for rule_name, until in self.suppressed_alerts.items()
            if now >= until
        ]

        for rule_name in expired:
            del self.suppressed_alerts[rule_name]

    async def _cleanup_alert_history(self):
        """Clean up old alert history"""
        cutoff = datetime.utcnow() - timedelta(days=30)
        self.alert_history = [
            alert for alert in self.alert_history
            if alert.created_at > cutoff
        ]

    # Public API methods
    async def get_active_alerts(self) -> List[Alert]:
        """Get all active alerts"""
        return list(self.active_alerts.values())

    async def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history"""
        return sorted(
            self.alert_history[-limit:],
            key=lambda x: x.created_at,
            reverse=True
        )

    async def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.updated_at = datetime.utcnow()

            logger.info(
                "Alert acknowledged",
                alert_id=alert_id
            )
            return True
        return False

    async def suppress_alert_rule(self, rule_name: str, duration_minutes: int = 60):
        """Suppress alerts for a specific rule"""
        until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.suppressed_alerts[rule_name] = until

        logger.info(
            "Alert rule suppressed",
            rule_name=rule_name,
            duration_minutes=duration_minutes
        )

    async def add_alert_rule(self, rule: AlertRule):
        """Add new alert rule"""
        self.alert_rules[rule.name] = rule
        logger.info(
            "Alert rule added",
            rule_name=rule.name
        )

    async def remove_alert_rule(self, rule_name: str):
        """Remove alert rule"""
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            logger.info(
                "Alert rule removed",
                rule_name=rule_name
            )

    async def get_alert_rules(self) -> Dict[str, AlertRule]:
        """Get all alert rules"""
        return self.alert_rules.copy()

    async def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        now = datetime.utcnow()
        last_24h = now - timedelta(hours=24)
        last_7d = now - timedelta(days=7)

        alerts_24h = [a for a in self.alert_history if a.created_at > last_24h]
        alerts_7d = [a for a in self.alert_history if a.created_at > last_7d]

        return {
            'active_alerts': len(self.active_alerts),
            'total_alerts_24h': len(alerts_24h),
            'total_alerts_7d': len(alerts_7d),
            'alerts_by_severity_24h': {
                severity.value: len([a for a in alerts_24h if a.severity == severity])
                for severity in AlertSeverity
            },
            'most_frequent_alerts': self._get_most_frequent_alerts(alerts_7d),
            'suppressed_rules': len(self.suppressed_alerts),
            'total_rules': len(self.alert_rules),
            'enabled_rules': len([r for r in self.alert_rules.values() if r.enabled])
        }

    def _get_most_frequent_alerts(self, alerts: List[Alert]) -> Dict[str, int]:
        """Get most frequent alert rules"""
        rule_counts = {}
        for alert in alerts:
            rule_counts[alert.rule_name] = rule_counts.get(alert.rule_name, 0) + 1

        return dict(sorted(rule_counts.items(), key=lambda x: x[1], reverse=True)[:5])
