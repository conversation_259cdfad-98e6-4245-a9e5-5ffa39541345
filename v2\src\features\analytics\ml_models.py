"""
🧠 Machine Learning Models

Advanced ML models for price prediction, pattern recognition,
and market analysis with training, validation, and inference capabilities.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import pickle
import json
from abc import ABC, abstractmethod

from ...config.logging_config import get_logger
from ...shared.types import SignalType, MarketData
from ...database.models import Token, Signal, Trade
from ...features.data_pipeline import DataAggregator

logger = get_logger(__name__)


class ModelType(str, Enum):
    """ML model types"""
    PRICE_PREDICTION = "price_prediction"
    PATTERN_RECOGNITION = "pattern_recognition"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    MARKET_REGIME = "market_regime"
    VOLATILITY_PREDICTION = "volatility_prediction"


class ModelStatus(str, Enum):
    """Model training/deployment status"""
    UNTRAINED = "untrained"
    TRAINING = "training"
    TRAINED = "trained"
    DEPLOYED = "deployed"
    ERROR = "error"


class BaseMLModel(ABC):
    """Base class for ML models"""
    
    def __init__(self, model_id: str, model_type: ModelType):
        self.model_id = model_id
        self.model_type = model_type
        self.status = ModelStatus.UNTRAINED
        self.model = None
        self.scaler = None
        self.feature_columns = []
        self.target_column = None
        self.training_history = []
        self.performance_metrics = {}
        self.last_trained = None
        self.logger = logger
    
    @abstractmethod
    async def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for training/inference"""
        pass
    
    @abstractmethod
    async def train(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train the model"""
        pass
    
    @abstractmethod
    async def predict(self, input_data: pd.DataFrame) -> np.ndarray:
        """Make predictions"""
        pass
    
    @abstractmethod
    async def evaluate(self, test_data: pd.DataFrame) -> Dict[str, float]:
        """Evaluate model performance"""
        pass
    
    async def save_model(self, filepath: str):
        """Save model to file"""
        try:
            model_data = {
                "model": self.model,
                "scaler": self.scaler,
                "feature_columns": self.feature_columns,
                "target_column": self.target_column,
                "performance_metrics": self.performance_metrics,
                "last_trained": self.last_trained,
                "model_type": self.model_type,
                "status": self.status
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"Model {self.model_id} saved to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving model: {str(e)}")
            raise
    
    async def load_model(self, filepath: str):
        """Load model from file"""
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data["model"]
            self.scaler = model_data["scaler"]
            self.feature_columns = model_data["feature_columns"]
            self.target_column = model_data["target_column"]
            self.performance_metrics = model_data["performance_metrics"]
            self.last_trained = model_data["last_trained"]
            self.status = ModelStatus.DEPLOYED
            
            self.logger.info(f"Model {self.model_id} loaded from {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error loading model: {str(e)}")
            raise


class PricePredictionModel(BaseMLModel):
    """
    📈 Price Prediction Model
    
    Predicts future token prices using technical indicators,
    market data, and historical patterns.
    """
    
    def __init__(self, model_id: str = "price_predictor"):
        super().__init__(model_id, ModelType.PRICE_PREDICTION)
        self.prediction_horizon = 24  # hours
        self.lookback_window = 168   # hours (1 week)
    
    async def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for price prediction"""
        try:
            # Ensure data is sorted by timestamp
            data = data.sort_values('timestamp')
            
            # Technical indicators
            data['sma_12'] = data['price'].rolling(window=12).mean()
            data['sma_24'] = data['price'].rolling(window=24).mean()
            data['ema_12'] = data['price'].ewm(span=12).mean()
            data['ema_24'] = data['price'].ewm(span=24).mean()
            
            # Price changes
            data['price_change_1h'] = data['price'].pct_change(1)
            data['price_change_4h'] = data['price'].pct_change(4)
            data['price_change_24h'] = data['price'].pct_change(24)
            
            # Volatility
            data['volatility_12h'] = data['price'].rolling(window=12).std()
            data['volatility_24h'] = data['price'].rolling(window=24).std()
            
            # Volume indicators
            data['volume_sma_12'] = data['volume'].rolling(window=12).mean()
            data['volume_ratio'] = data['volume'] / data['volume_sma_12']
            
            # RSI
            delta = data['price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # MACD
            ema_12 = data['price'].ewm(span=12).mean()
            ema_26 = data['price'].ewm(span=26).mean()
            data['macd'] = ema_12 - ema_26
            data['macd_signal'] = data['macd'].ewm(span=9).mean()
            data['macd_histogram'] = data['macd'] - data['macd_signal']
            
            # Bollinger Bands
            bb_period = 20
            bb_std = 2
            data['bb_middle'] = data['price'].rolling(window=bb_period).mean()
            bb_std_dev = data['price'].rolling(window=bb_period).std()
            data['bb_upper'] = data['bb_middle'] + (bb_std_dev * bb_std)
            data['bb_lower'] = data['bb_middle'] - (bb_std_dev * bb_std)
            data['bb_position'] = (data['price'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
            
            # Time-based features
            data['hour'] = pd.to_datetime(data['timestamp']).dt.hour
            data['day_of_week'] = pd.to_datetime(data['timestamp']).dt.dayofweek
            
            # Target variable (future price)
            data['target_price'] = data['price'].shift(-self.prediction_horizon)
            data['target_return'] = (data['target_price'] / data['price']) - 1
            
            # Feature columns
            self.feature_columns = [
                'sma_12', 'sma_24', 'ema_12', 'ema_24',
                'price_change_1h', 'price_change_4h', 'price_change_24h',
                'volatility_12h', 'volatility_24h',
                'volume_ratio', 'rsi', 'macd', 'macd_signal', 'macd_histogram',
                'bb_position', 'hour', 'day_of_week'
            ]
            
            self.target_column = 'target_return'
            
            # Drop rows with NaN values
            data = data.dropna()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error preparing features: {str(e)}")
            raise
    
    async def train(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train the price prediction model"""
        try:
            self.logger.info("Training price prediction model")
            self.status = ModelStatus.TRAINING
            
            # Prepare features
            data = await self.prepare_features(training_data)
            
            if len(data) < 100:
                raise ValueError("Insufficient training data")
            
            # Split features and target
            X = data[self.feature_columns]
            y = data[self.target_column]
            
            # Split train/validation
            split_idx = int(len(data) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train, y_val = y[:split_idx], y[split_idx:]
            
            # Scale features
            from sklearn.preprocessing import StandardScaler
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_val_scaled = self.scaler.transform(X_val)
            
            # Train model (using Random Forest for simplicity)
            from sklearn.ensemble import RandomForestRegressor
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            self.model.fit(X_train_scaled, y_train)
            
            # Evaluate on validation set
            val_predictions = self.model.predict(X_val_scaled)
            
            # Calculate metrics
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            mse = mean_squared_error(y_val, val_predictions)
            mae = mean_absolute_error(y_val, val_predictions)
            r2 = r2_score(y_val, val_predictions)
            
            self.performance_metrics = {
                "mse": float(mse),
                "mae": float(mae),
                "r2": float(r2),
                "rmse": float(np.sqrt(mse))
            }
            
            # Feature importance
            feature_importance = dict(zip(
                self.feature_columns,
                self.model.feature_importances_
            ))
            
            self.status = ModelStatus.TRAINED
            self.last_trained = datetime.utcnow()
            
            training_result = {
                "success": True,
                "model_id": self.model_id,
                "training_samples": len(X_train),
                "validation_samples": len(X_val),
                "performance_metrics": self.performance_metrics,
                "feature_importance": feature_importance,
                "training_time": datetime.utcnow()
            }
            
            self.training_history.append(training_result)
            
            self.logger.info(f"Model training completed. R2 score: {r2:.4f}")
            return training_result
            
        except Exception as e:
            self.status = ModelStatus.ERROR
            self.logger.error(f"Error training model: {str(e)}")
            raise
    
    async def predict(self, input_data: pd.DataFrame) -> np.ndarray:
        """Make price predictions"""
        try:
            if self.status not in [ModelStatus.TRAINED, ModelStatus.DEPLOYED]:
                raise ValueError("Model not trained")
            
            # Prepare features
            data = await self.prepare_features(input_data)
            
            if len(data) == 0:
                raise ValueError("No valid data for prediction")
            
            # Get features
            X = data[self.feature_columns]
            
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Make predictions
            predictions = self.model.predict(X_scaled)
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"Error making predictions: {str(e)}")
            raise
    
    async def evaluate(self, test_data: pd.DataFrame) -> Dict[str, float]:
        """Evaluate model performance on test data"""
        try:
            # Prepare features
            data = await self.prepare_features(test_data)
            
            X = data[self.feature_columns]
            y = data[self.target_column]
            
            # Make predictions
            predictions = await self.predict(test_data)
            
            # Calculate metrics
            from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
            mse = mean_squared_error(y, predictions)
            mae = mean_absolute_error(y, predictions)
            r2 = r2_score(y, predictions)
            
            return {
                "mse": float(mse),
                "mae": float(mae),
                "r2": float(r2),
                "rmse": float(np.sqrt(mse))
            }
            
        except Exception as e:
            self.logger.error(f"Error evaluating model: {str(e)}")
            raise


class PatternRecognitionModel(BaseMLModel):
    """
    🔍 Pattern Recognition Model

    Identifies chart patterns and technical formations
    for enhanced signal generation.
    """

    def __init__(self, model_id: str = "pattern_recognizer"):
        super().__init__(model_id, ModelType.PATTERN_RECOGNITION)
        self.pattern_window = 48  # hours
        self.patterns = [
            "double_top", "double_bottom", "head_shoulders",
            "triangle", "flag", "pennant", "wedge"
        ]

    async def prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare features for pattern recognition"""
        try:
            # Price normalization within windows
            data['price_norm'] = data.groupby(data.index // self.pattern_window)['price'].transform(
                lambda x: (x - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0
            )

            # Local extrema detection
            data['local_max'] = data['price'].rolling(window=5, center=True).max() == data['price']
            data['local_min'] = data['price'].rolling(window=5, center=True).min() == data['price']

            # Trend features
            data['trend_5'] = data['price'].rolling(window=5).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 5 else 0
            )
            data['trend_10'] = data['price'].rolling(window=10).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 10 else 0
            )

            # Support/resistance levels
            data['support'] = data['price'].rolling(window=20).min()
            data['resistance'] = data['price'].rolling(window=20).max()
            data['support_distance'] = (data['price'] - data['support']) / data['price']
            data['resistance_distance'] = (data['resistance'] - data['price']) / data['price']

            # Volume patterns
            data['volume_trend'] = data['volume'].rolling(window=5).apply(
                lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 5 else 0
            )

            self.feature_columns = [
                'price_norm', 'local_max', 'local_min', 'trend_5', 'trend_10',
                'support_distance', 'resistance_distance', 'volume_trend'
            ]

            return data.dropna()

        except Exception as e:
            self.logger.error(f"Error preparing pattern features: {str(e)}")
            raise

    async def train(self, training_data: pd.DataFrame) -> Dict[str, Any]:
        """Train pattern recognition model"""
        try:
            self.logger.info("Training pattern recognition model")
            self.status = ModelStatus.TRAINING

            # For this implementation, we'll use a simplified approach
            # In a full implementation, this would use labeled pattern data

            data = await self.prepare_features(training_data)

            # Create synthetic pattern labels (simplified)
            data['pattern_strength'] = np.random.random(len(data))

            X = data[self.feature_columns]
            y = data['pattern_strength']

            # Train clustering model for pattern detection
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler

            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)

            self.model = KMeans(n_clusters=len(self.patterns), random_state=42)
            self.model.fit(X_scaled)

            self.status = ModelStatus.TRAINED
            self.last_trained = datetime.utcnow()

            return {
                "success": True,
                "model_id": self.model_id,
                "training_samples": len(X),
                "n_patterns": len(self.patterns),
                "training_time": datetime.utcnow()
            }

        except Exception as e:
            self.status = ModelStatus.ERROR
            self.logger.error(f"Error training pattern model: {str(e)}")
            raise

    async def predict(self, input_data: pd.DataFrame) -> np.ndarray:
        """Detect patterns in input data"""
        try:
            if self.status not in [ModelStatus.TRAINED, ModelStatus.DEPLOYED]:
                raise ValueError("Model not trained")

            data = await self.prepare_features(input_data)
            X = data[self.feature_columns]
            X_scaled = self.scaler.transform(X)

            # Predict pattern clusters
            pattern_clusters = self.model.predict(X_scaled)

            # Convert to pattern probabilities
            pattern_probs = np.zeros((len(pattern_clusters), len(self.patterns)))
            for i, cluster in enumerate(pattern_clusters):
                pattern_probs[i, cluster] = 1.0

            return pattern_probs

        except Exception as e:
            self.logger.error(f"Error detecting patterns: {str(e)}")
            raise

    async def evaluate(self, test_data: pd.DataFrame) -> Dict[str, float]:
        """Evaluate pattern recognition performance"""
        try:
            # Simplified evaluation
            predictions = await self.predict(test_data)

            return {
                "patterns_detected": int(np.sum(predictions > 0.5)),
                "confidence_avg": float(np.mean(predictions)),
                "coverage": float(len(predictions) / len(test_data))
            }

        except Exception as e:
            self.logger.error(f"Error evaluating pattern model: {str(e)}")
            raise


class MLModelManager:
    """
    🧠 ML Model Manager

    Manages multiple ML models for training, deployment,
    and inference coordination.
    """

    def __init__(self):
        self.logger = logger
        self.models: Dict[str, BaseMLModel] = {}
        self.data_aggregator = DataAggregator()

        # Initialize models
        self.models["price_predictor"] = PricePredictionModel()
        self.models["pattern_recognizer"] = PatternRecognitionModel()

    async def train_model(
        self,
        model_id: str,
        token_address: Optional[str] = None,
        training_period_days: int = 30
    ) -> Dict[str, Any]:
        """Train a specific model"""
        try:
            if model_id not in self.models:
                raise ValueError(f"Model {model_id} not found")

            model = self.models[model_id]

            # Get training data
            training_data = await self._get_training_data(token_address, training_period_days)

            if len(training_data) < 100:
                raise ValueError("Insufficient training data")

            # Train model
            result = await model.train(training_data)

            self.logger.info(f"Model {model_id} trained successfully")
            return result

        except Exception as e:
            self.logger.error(f"Error training model {model_id}: {str(e)}")
            raise

    async def predict_price(
        self,
        token_address: str,
        prediction_horizon: int = 24
    ) -> Dict[str, Any]:
        """Get price prediction for a token"""
        try:
            model = self.models["price_predictor"]

            if model.status not in [ModelStatus.TRAINED, ModelStatus.DEPLOYED]:
                raise ValueError("Price prediction model not trained")

            # Get recent data
            recent_data = await self._get_recent_data(token_address, hours=168)

            # Make prediction
            predictions = await model.predict(recent_data)

            if len(predictions) > 0:
                latest_price = recent_data['price'].iloc[-1]
                predicted_return = predictions[-1]
                predicted_price = latest_price * (1 + predicted_return)

                return {
                    "success": True,
                    "token_address": token_address,
                    "current_price": float(latest_price),
                    "predicted_price": float(predicted_price),
                    "predicted_return": float(predicted_return),
                    "prediction_horizon": prediction_horizon,
                    "confidence": float(model.performance_metrics.get("r2", 0)),
                    "model_id": model.model_id
                }
            else:
                raise ValueError("No predictions generated")

        except Exception as e:
            self.logger.error(f"Error predicting price: {str(e)}")
            raise

    async def detect_patterns(self, token_address: str) -> Dict[str, Any]:
        """Detect patterns for a token"""
        try:
            model = self.models["pattern_recognizer"]

            if model.status not in [ModelStatus.TRAINED, ModelStatus.DEPLOYED]:
                raise ValueError("Pattern recognition model not trained")

            # Get recent data
            recent_data = await self._get_recent_data(token_address, hours=48)

            # Detect patterns
            pattern_probs = await model.predict(recent_data)

            # Format results
            detected_patterns = []
            for i, pattern_name in enumerate(model.patterns):
                if len(pattern_probs) > 0:
                    confidence = float(np.mean(pattern_probs[:, i]))
                    if confidence > 0.3:  # Threshold for pattern detection
                        detected_patterns.append({
                            "pattern": pattern_name,
                            "confidence": confidence
                        })

            return {
                "success": True,
                "token_address": token_address,
                "patterns_detected": detected_patterns,
                "model_id": model.model_id
            }

        except Exception as e:
            self.logger.error(f"Error detecting patterns: {str(e)}")
            raise

    async def get_model_status(self, model_id: str) -> Dict[str, Any]:
        """Get status of a specific model"""
        try:
            if model_id not in self.models:
                raise ValueError(f"Model {model_id} not found")

            model = self.models[model_id]

            return {
                "model_id": model.model_id,
                "model_type": model.model_type,
                "status": model.status,
                "last_trained": model.last_trained,
                "performance_metrics": model.performance_metrics,
                "feature_count": len(model.feature_columns)
            }

        except Exception as e:
            self.logger.error(f"Error getting model status: {str(e)}")
            raise

    async def _get_training_data(self, token_address: Optional[str], days: int) -> pd.DataFrame:
        """Get training data for model"""
        try:
            # For now, generate synthetic data
            # In a real implementation, this would fetch from database

            hours = days * 24
            timestamps = pd.date_range(
                start=datetime.utcnow() - timedelta(days=days),
                periods=hours,
                freq='H'
            )

            # Generate synthetic price data
            np.random.seed(42)
            price_base = 100
            price_changes = np.random.normal(0, 0.02, hours)
            prices = [price_base]

            for change in price_changes[1:]:
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 0.01))  # Prevent negative prices

            # Generate synthetic volume data
            volumes = np.random.lognormal(10, 1, hours)

            data = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })

            return data

        except Exception as e:
            self.logger.error(f"Error getting training data: {str(e)}")
            raise

    async def _get_recent_data(self, token_address: str, hours: int) -> pd.DataFrame:
        """Get recent data for a token"""
        try:
            # For now, generate synthetic recent data
            # In a real implementation, this would fetch from database/API

            timestamps = pd.date_range(
                start=datetime.utcnow() - timedelta(hours=hours),
                periods=hours,
                freq='H'
            )

            # Generate synthetic data
            np.random.seed(int(datetime.utcnow().timestamp()))
            price_base = 50
            price_changes = np.random.normal(0, 0.015, hours)
            prices = [price_base]

            for change in price_changes[1:]:
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 0.01))

            volumes = np.random.lognormal(9, 0.8, hours)

            data = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })

            return data

        except Exception as e:
            self.logger.error(f"Error getting recent data: {str(e)}")
            raise
