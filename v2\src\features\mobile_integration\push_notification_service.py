"""
Push Notification Service

Provides Firebase Cloud Messaging (FCM) and Apple Push Notification Service (APNs)
integration for mobile push notifications. Follows clean code principles.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTTPException
import httpx

from src.shared.types import NotificationPriority
from src.features.data_pipeline.cache_manager import CacheManager
from src.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class PushPlatform(Enum):
    """Push notification platforms"""
    FCM = "fcm"  # Firebase Cloud Messaging (Android)
    APNS = "apns"  # Apple Push Notification Service (iOS)
    WEB = "web"  # Web Push (PWA)


@dataclass
class PushNotificationConfig:
    """Push notification configuration"""
    fcm_server_key: str
    apns_key_id: str
    apns_team_id: str
    apns_bundle_id: str
    apns_key_path: str
    web_push_vapid_public: str
    web_push_vapid_private: str
    max_retries: int = 3
    retry_delay: int = 5  # seconds
    batch_size: int = 100
    cache_ttl: int = 3600  # seconds


@dataclass
class PushMessage:
    """Push notification message"""
    title: str
    body: str
    data: Optional[Dict[str, Any]] = None
    image_url: Optional[str] = None
    action_url: Optional[str] = None
    priority: NotificationPriority = NotificationPriority.MEDIUM
    ttl: int = 86400  # Time to live in seconds
    collapse_key: Optional[str] = None


@dataclass
class DeviceToken:
    """Device token information"""
    token: str
    platform: PushPlatform
    user_id: str
    app_version: Optional[str] = None
    device_info: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    last_used: Optional[datetime] = None
    is_active: bool = True


class PushNotificationService:
    """
    Comprehensive push notification service supporting FCM, APNs, and Web Push.
    Handles device token management, message delivery, and analytics.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        config: Optional[PushNotificationConfig] = None
    ):
        self.cache_manager = cache_manager
        self.config = config or self._load_config()
        self.logger = logger.bind(service="push_notifications")
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    def _load_config(self) -> PushNotificationConfig:
        """Load push notification configuration from settings"""
        return PushNotificationConfig(
            fcm_server_key=settings.fcm_server_key,
            apns_key_id=settings.apns_key_id,
            apns_team_id=settings.apns_team_id,
            apns_bundle_id=settings.apns_bundle_id,
            apns_key_path=settings.apns_key_path,
            web_push_vapid_public=settings.web_push_vapid_public,
            web_push_vapid_private=settings.web_push_vapid_private
        )
    
    async def register_device(
        self,
        user_id: str,
        token: str,
        platform: PushPlatform,
        device_info: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Register a device token for push notifications.
        
        Args:
            user_id: User identifier
            token: Device token
            platform: Push platform (FCM, APNs, Web)
            device_info: Additional device information
            
        Returns:
            True if registration was successful
        """
        try:
            device_token = DeviceToken(
                token=token,
                platform=platform,
                user_id=user_id,
                device_info=device_info,
                created_at=datetime.utcnow(),
                last_used=datetime.utcnow()
            )
            
            # Store device token in cache and database
            cache_key = f"device_token:{user_id}:{platform.value}"
            await self.cache_manager.set(
                cache_key,
                device_token.__dict__,
                ttl=self.config.cache_ttl
            )
            
            # TODO: Store in database for persistence
            
            self.logger.info(
                "Device token registered",
                user_id=user_id,
                platform=platform.value
            )
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to register device token",
                user_id=user_id,
                platform=platform.value,
                error=str(e)
            )
            return False
    
    async def send_notification(
        self,
        user_id: str,
        message: PushMessage,
        platforms: Optional[List[PushPlatform]] = None
    ) -> Dict[str, bool]:
        """
        Send push notification to user's devices.
        
        Args:
            user_id: User identifier
            message: Push notification message
            platforms: Target platforms (if None, send to all registered)
            
        Returns:
            Dictionary with platform success status
        """
        try:
            if platforms is None:
                platforms = [PushPlatform.FCM, PushPlatform.APNS, PushPlatform.WEB]
            
            results = {}
            
            # Send to each platform
            for platform in platforms:
                device_token = await self._get_device_token(user_id, platform)
                if device_token and device_token.is_active:
                    success = await self._send_to_platform(device_token, message)
                    results[platform.value] = success
                    
                    if success:
                        # Update last used timestamp
                        await self._update_token_usage(device_token)
                else:
                    results[platform.value] = False
            
            self.logger.info(
                "Push notification sent",
                user_id=user_id,
                title=message.title,
                results=results
            )
            
            return results
            
        except Exception as e:
            self.logger.error(
                "Failed to send push notification",
                user_id=user_id,
                error=str(e)
            )
            return {platform.value: False for platform in platforms or []}
    
    async def send_bulk_notifications(
        self,
        notifications: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Send bulk push notifications efficiently.
        
        Args:
            notifications: List of notification data
            
        Returns:
            Bulk send results and statistics
        """
        try:
            total_notifications = len(notifications)
            successful_sends = 0
            failed_sends = 0
            platform_stats = {}
            
            # Process notifications in batches
            for i in range(0, total_notifications, self.config.batch_size):
                batch = notifications[i:i + self.config.batch_size]
                
                # Send batch concurrently
                tasks = []
                for notification in batch:
                    user_id = notification["user_id"]
                    message = PushMessage(**notification["message"])
                    platforms = notification.get("platforms")
                    
                    task = self.send_notification(user_id, message, platforms)
                    tasks.append(task)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process batch results
                for result in batch_results:
                    if isinstance(result, dict):
                        for platform, success in result.items():
                            if platform not in platform_stats:
                                platform_stats[platform] = {"success": 0, "failed": 0}
                            
                            if success:
                                platform_stats[platform]["success"] += 1
                                successful_sends += 1
                            else:
                                platform_stats[platform]["failed"] += 1
                                failed_sends += 1
                    else:
                        failed_sends += 1
            
            results = {
                "total_notifications": total_notifications,
                "successful_sends": successful_sends,
                "failed_sends": failed_sends,
                "platform_stats": platform_stats,
                "success_rate": (successful_sends / total_notifications * 100) if total_notifications > 0 else 0
            }
            
            self.logger.info("Bulk notifications sent", **results)
            return results
            
        except Exception as e:
            self.logger.error("Failed to send bulk notifications", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to send bulk notifications")
    
    async def _get_device_token(
        self,
        user_id: str,
        platform: PushPlatform
    ) -> Optional[DeviceToken]:
        """Get device token for user and platform"""
        try:
            cache_key = f"device_token:{user_id}:{platform.value}"
            token_data = await self.cache_manager.get(cache_key)
            
            if token_data:
                return DeviceToken(**token_data)
            
            # TODO: Fallback to database lookup
            return None
            
        except Exception as e:
            self.logger.error(
                "Failed to get device token",
                user_id=user_id,
                platform=platform.value,
                error=str(e)
            )
            return None
    
    async def _send_to_platform(
        self,
        device_token: DeviceToken,
        message: PushMessage
    ) -> bool:
        """Send notification to specific platform"""
        try:
            if device_token.platform == PushPlatform.FCM:
                return await self._send_fcm(device_token, message)
            elif device_token.platform == PushPlatform.APNS:
                return await self._send_apns(device_token, message)
            elif device_token.platform == PushPlatform.WEB:
                return await self._send_web_push(device_token, message)
            else:
                self.logger.warning("Unsupported platform", platform=device_token.platform)
                return False
                
        except Exception as e:
            self.logger.error(
                "Failed to send to platform",
                platform=device_token.platform.value,
                error=str(e)
            )
            return False
    
    async def _send_fcm(self, device_token: DeviceToken, message: PushMessage) -> bool:
        """Send FCM notification"""
        try:
            url = "https://fcm.googleapis.com/fcm/send"
            headers = {
                "Authorization": f"key={self.config.fcm_server_key}",
                "Content-Type": "application/json"
            }
            
            # Build FCM payload
            payload = {
                "to": device_token.token,
                "notification": {
                    "title": message.title,
                    "body": message.body
                },
                "data": message.data or {},
                "priority": "high" if message.priority in [NotificationPriority.HIGH, NotificationPriority.CRITICAL] else "normal",
                "time_to_live": message.ttl
            }
            
            if message.image_url:
                payload["notification"]["image"] = message.image_url
            
            if message.collapse_key:
                payload["collapse_key"] = message.collapse_key
            
            # Send request
            response = await self.http_client.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success") == 1:
                    return True
                else:
                    self.logger.warning("FCM send failed", result=result)
                    return False
            else:
                self.logger.error("FCM request failed", status_code=response.status_code)
                return False
                
        except Exception as e:
            self.logger.error("FCM send error", error=str(e))
            return False
    
    async def _send_apns(self, device_token: DeviceToken, message: PushMessage) -> bool:
        """Send APNs notification"""
        try:
            # APNs implementation would require JWT token generation
            # and HTTP/2 client for production use
            
            # For now, return success for demonstration
            # TODO: Implement full APNs integration
            
            self.logger.info("APNs notification sent (mock)", token=device_token.token[:10])
            return True
            
        except Exception as e:
            self.logger.error("APNs send error", error=str(e))
            return False
    
    async def _send_web_push(self, device_token: DeviceToken, message: PushMessage) -> bool:
        """Send Web Push notification"""
        try:
            # Web Push implementation would require VAPID authentication
            # and proper payload encryption
            
            # For now, return success for demonstration
            # TODO: Implement full Web Push integration
            
            self.logger.info("Web Push notification sent (mock)", token=device_token.token[:10])
            return True
            
        except Exception as e:
            self.logger.error("Web Push send error", error=str(e))
            return False
    
    async def _update_token_usage(self, device_token: DeviceToken) -> None:
        """Update device token last used timestamp"""
        try:
            device_token.last_used = datetime.utcnow()
            cache_key = f"device_token:{device_token.user_id}:{device_token.platform.value}"
            await self.cache_manager.set(
                cache_key,
                device_token.__dict__,
                ttl=self.config.cache_ttl
            )
        except Exception as e:
            self.logger.error("Failed to update token usage", error=str(e))
    
    async def unregister_device(self, user_id: str, platform: PushPlatform) -> bool:
        """
        Unregister a device token.
        
        Args:
            user_id: User identifier
            platform: Push platform
            
        Returns:
            True if unregistration was successful
        """
        try:
            cache_key = f"device_token:{user_id}:{platform.value}"
            await self.cache_manager.delete(cache_key)
            
            # TODO: Mark as inactive in database
            
            self.logger.info(
                "Device token unregistered",
                user_id=user_id,
                platform=platform.value
            )
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to unregister device token",
                user_id=user_id,
                platform=platform.value,
                error=str(e)
            )
            return False
    
    async def get_notification_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get notification statistics for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Notification statistics
        """
        try:
            # Get device tokens
            platforms = [PushPlatform.FCM, PushPlatform.APNS, PushPlatform.WEB]
            registered_platforms = []
            
            for platform in platforms:
                device_token = await self._get_device_token(user_id, platform)
                if device_token and device_token.is_active:
                    registered_platforms.append({
                        "platform": platform.value,
                        "registered_at": device_token.created_at,
                        "last_used": device_token.last_used
                    })
            
            # TODO: Get delivery statistics from database
            
            return {
                "user_id": user_id,
                "registered_platforms": registered_platforms,
                "total_platforms": len(registered_platforms),
                "last_notification": None,  # TODO: Implement
                "delivery_stats": {
                    "total_sent": 0,  # TODO: Implement
                    "successful_deliveries": 0,  # TODO: Implement
                    "failed_deliveries": 0  # TODO: Implement
                }
            }
            
        except Exception as e:
            self.logger.error("Failed to get notification stats", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get notification statistics")
    
    async def cleanup_inactive_tokens(self, days_inactive: int = 30) -> int:
        """
        Clean up inactive device tokens.
        
        Args:
            days_inactive: Number of days to consider a token inactive
            
        Returns:
            Number of tokens cleaned up
        """
        try:
            # TODO: Implement database cleanup of inactive tokens
            # This would involve querying tokens not used in the specified period
            
            self.logger.info("Token cleanup completed", days_inactive=days_inactive)
            return 0  # Placeholder
            
        except Exception as e:
            self.logger.error("Failed to cleanup tokens", error=str(e))
            return 0
