"""
News and Sentiment Service

Aggregates news and social media sentiment data for enhanced
market analysis and trading signal generation.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTTPException
import httpx

from src.features.third_party_integrations.data_provider_manager import (
    DataProviderManager, NewsArticle, MarketSentiment
)
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


class SentimentSource(Enum):
    """Sentiment data sources"""
    TWITTER = "twitter"
    REDDIT = "reddit"
    TELEGRAM = "telegram"
    NEWS = "news"
    DISCORD = "discord"


@dataclass
class SentimentConfig:
    """Sentiment analysis configuration"""
    enabled_sources: List[SentimentSource] = None
    update_interval: int = 300  # 5 minutes
    sentiment_threshold: float = 0.1  # Minimum sentiment change to trigger alert
    news_relevance_threshold: float = 0.5
    social_volume_threshold: int = 100
    cache_ttl: int = 1800  # 30 minutes


@dataclass
class SentimentSignal:
    """Sentiment-based signal"""
    token_address: str
    signal_type: str  # bullish, bearish, neutral
    sentiment_score: float
    confidence: float
    sources: List[SentimentSource]
    volume: int
    news_count: int
    social_mentions: int
    price_correlation: Optional[float] = None
    created_at: datetime = None


@dataclass
class TrendingTopic:
    """Trending topic data"""
    topic: str
    mentions: int
    sentiment: float
    related_tokens: List[str]
    sources: List[SentimentSource]
    growth_rate: float
    timestamp: datetime


class NewsSentimentService:
    """
    Aggregates and analyzes news and social media sentiment
    for enhanced market analysis and signal generation.
    """
    
    def __init__(
        self,
        data_provider_manager: DataProviderManager,
        cache_manager: CacheManager,
        config: Optional[SentimentConfig] = None
    ):
        self.data_provider_manager = data_provider_manager
        self.cache_manager = cache_manager
        self.config = config or SentimentConfig(
            enabled_sources=[SentimentSource.TWITTER, SentimentSource.NEWS, SentimentSource.REDDIT]
        )
        self.logger = logger.bind(service="news_sentiment")
        self.http_client = httpx.AsyncClient(timeout=30.0)
    
    async def get_token_sentiment(
        self,
        token_address: str,
        timeframe: str = "24h"
    ) -> MarketSentiment:
        """
        Get comprehensive sentiment analysis for a token.
        
        Args:
            token_address: Token contract address
            timeframe: Analysis timeframe
            
        Returns:
            Market sentiment data
        """
        try:
            cache_key = f"token_sentiment:{token_address}:{timeframe}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Token sentiment served from cache", token_address=token_address)
                return MarketSentiment(**cached_data)
            
            # Get sentiment from data provider manager
            sentiment = await self.data_provider_manager.get_market_sentiment(token_address, timeframe)
            
            # Enhance with additional sources
            enhanced_sentiment = await self._enhance_sentiment_data(sentiment, timeframe)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                enhanced_sentiment.__dict__,
                ttl=self.config.cache_ttl
            )
            
            self.logger.info(
                "Token sentiment analyzed",
                token_address=token_address,
                sentiment_score=enhanced_sentiment.sentiment_score,
                confidence=enhanced_sentiment.confidence
            )
            
            return enhanced_sentiment
            
        except Exception as e:
            self.logger.error("Failed to get token sentiment", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get token sentiment")
    
    async def get_news_analysis(
        self,
        token_address: str,
        limit: int = 20,
        hours_back: int = 24
    ) -> Dict[str, Any]:
        """
        Get news analysis for a token.
        
        Args:
            token_address: Token contract address
            limit: Maximum number of articles
            hours_back: How many hours back to analyze
            
        Returns:
            News analysis data
        """
        try:
            # Get news articles
            articles = await self.data_provider_manager.get_related_news(
                token_address=token_address,
                limit=limit,
                hours_back=hours_back
            )
            
            if not articles:
                return {
                    "total_articles": 0,
                    "sentiment_breakdown": {},
                    "key_topics": [],
                    "source_distribution": {},
                    "timeline": []
                }
            
            # Analyze articles
            analysis = await self._analyze_news_articles(articles)
            
            self.logger.info(
                "News analysis completed",
                token_address=token_address,
                articles=len(articles),
                avg_sentiment=analysis.get("average_sentiment", 0)
            )
            
            return analysis
            
        except Exception as e:
            self.logger.error("Failed to get news analysis", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get news analysis")
    
    async def generate_sentiment_signals(
        self,
        token_addresses: List[str],
        min_confidence: float = 0.6
    ) -> List[SentimentSignal]:
        """
        Generate sentiment-based trading signals.
        
        Args:
            token_addresses: List of token addresses to analyze
            min_confidence: Minimum confidence threshold
            
        Returns:
            List of sentiment signals
        """
        try:
            signals = []
            
            # Analyze sentiment for each token
            for token_address in token_addresses:
                try:
                    sentiment = await self.get_token_sentiment(token_address)
                    
                    # Generate signal based on sentiment
                    signal = await self._generate_signal_from_sentiment(sentiment, min_confidence)
                    
                    if signal:
                        signals.append(signal)
                        
                except Exception as e:
                    self.logger.error(
                        "Failed to analyze token sentiment",
                        token_address=token_address,
                        error=str(e)
                    )
                    continue
            
            # Sort by confidence
            signals.sort(key=lambda x: x.confidence, reverse=True)
            
            self.logger.info(
                "Sentiment signals generated",
                tokens_analyzed=len(token_addresses),
                signals_generated=len(signals)
            )
            
            return signals
            
        except Exception as e:
            self.logger.error("Failed to generate sentiment signals", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate sentiment signals")
    
    async def get_trending_topics(
        self,
        limit: int = 20,
        timeframe: str = "1h"
    ) -> List[TrendingTopic]:
        """
        Get trending topics across social media and news.
        
        Args:
            limit: Maximum number of topics
            timeframe: Trending timeframe
            
        Returns:
            List of trending topics
        """
        try:
            cache_key = f"trending_topics:{limit}:{timeframe}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return [TrendingTopic(**topic) for topic in cached_data]
            
            # Aggregate trending data from multiple sources
            trending_topics = await self._aggregate_trending_topics(limit, timeframe)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                [topic.__dict__ for topic in trending_topics],
                ttl=900  # 15 minutes
            )
            
            self.logger.info("Trending topics retrieved", count=len(trending_topics))
            return trending_topics
            
        except Exception as e:
            self.logger.error("Failed to get trending topics", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get trending topics")
    
    async def get_social_metrics(
        self,
        token_address: str,
        timeframe: str = "24h"
    ) -> Dict[str, Any]:
        """
        Get social media metrics for a token.
        
        Args:
            token_address: Token contract address
            timeframe: Metrics timeframe
            
        Returns:
            Social media metrics
        """
        try:
            cache_key = f"social_metrics:{token_address}:{timeframe}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return cached_data
            
            # Aggregate social metrics
            metrics = await self._aggregate_social_metrics(token_address, timeframe)
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                metrics,
                ttl=self.config.cache_ttl
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get social metrics", token_address=token_address, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get social metrics")
    
    async def _enhance_sentiment_data(
        self,
        base_sentiment: MarketSentiment,
        timeframe: str
    ) -> MarketSentiment:
        """Enhance sentiment data with additional sources"""
        try:
            # Get additional social media data
            social_data = await self._get_social_sentiment(base_sentiment.token_address, timeframe)
            
            # Get news sentiment
            news_data = await self._get_news_sentiment(base_sentiment.token_address, timeframe)
            
            # Combine sentiments with weights
            combined_sentiment = self._combine_sentiments([
                (base_sentiment.sentiment_score, 0.4),  # Base weight
                (social_data.get("sentiment", 0), 0.3),  # Social weight
                (news_data.get("sentiment", 0), 0.3)     # News weight
            ])
            
            # Update sentiment data
            enhanced_sentiment = MarketSentiment(
                token_address=base_sentiment.token_address,
                sentiment_score=combined_sentiment,
                confidence=min(base_sentiment.confidence + 0.1, 1.0),  # Slight confidence boost
                sources=base_sentiment.sources + ["social_media", "news"],
                positive_mentions=base_sentiment.positive_mentions + social_data.get("positive", 0),
                negative_mentions=base_sentiment.negative_mentions + social_data.get("negative", 0),
                neutral_mentions=base_sentiment.neutral_mentions + social_data.get("neutral", 0),
                trending_score=max(base_sentiment.trending_score, social_data.get("trending", 0)),
                social_volume=base_sentiment.social_volume + social_data.get("volume", 0),
                timestamp=datetime.utcnow()
            )
            
            return enhanced_sentiment
            
        except Exception as e:
            self.logger.error("Failed to enhance sentiment data", error=str(e))
            return base_sentiment
    
    async def _analyze_news_articles(
        self,
        articles: List[NewsArticle]
    ) -> Dict[str, Any]:
        """Analyze news articles for sentiment and topics"""
        if not articles:
            return {}
        
        # Sentiment breakdown
        sentiments = [article.sentiment for article in articles if article.sentiment is not None]
        positive_count = len([s for s in sentiments if s > 0.1])
        negative_count = len([s for s in sentiments if s < -0.1])
        neutral_count = len(sentiments) - positive_count - negative_count
        
        # Source distribution
        source_dist = {}
        for article in articles:
            source = article.source
            source_dist[source] = source_dist.get(source, 0) + 1
        
        # Timeline analysis
        timeline = []
        articles_by_hour = {}
        for article in articles:
            hour_key = article.published_at.strftime("%Y-%m-%d %H:00")
            if hour_key not in articles_by_hour:
                articles_by_hour[hour_key] = []
            articles_by_hour[hour_key].append(article)
        
        for hour, hour_articles in sorted(articles_by_hour.items()):
            hour_sentiment = sum(a.sentiment for a in hour_articles if a.sentiment) / len(hour_articles)
            timeline.append({
                "timestamp": hour,
                "article_count": len(hour_articles),
                "average_sentiment": hour_sentiment
            })
        
        # Key topics extraction (mock implementation)
        key_topics = ["defi", "trading", "market", "price", "analysis"]
        
        return {
            "total_articles": len(articles),
            "sentiment_breakdown": {
                "positive": positive_count,
                "negative": negative_count,
                "neutral": neutral_count
            },
            "average_sentiment": sum(sentiments) / len(sentiments) if sentiments else 0,
            "source_distribution": source_dist,
            "key_topics": key_topics,
            "timeline": timeline
        }
    
    async def _generate_signal_from_sentiment(
        self,
        sentiment: MarketSentiment,
        min_confidence: float
    ) -> Optional[SentimentSignal]:
        """Generate trading signal from sentiment data"""
        if sentiment.confidence < min_confidence:
            return None
        
        # Determine signal type
        if sentiment.sentiment_score > 0.3:
            signal_type = "bullish"
        elif sentiment.sentiment_score < -0.3:
            signal_type = "bearish"
        else:
            signal_type = "neutral"
        
        # Skip neutral signals with low volume
        if signal_type == "neutral" and sentiment.social_volume < self.config.social_volume_threshold:
            return None
        
        return SentimentSignal(
            token_address=sentiment.token_address,
            signal_type=signal_type,
            sentiment_score=sentiment.sentiment_score,
            confidence=sentiment.confidence,
            sources=[SentimentSource(source) for source in sentiment.sources if source in [s.value for s in SentimentSource]],
            volume=sentiment.social_volume,
            news_count=0,  # Would be calculated from news data
            social_mentions=sentiment.positive_mentions + sentiment.negative_mentions + sentiment.neutral_mentions,
            created_at=datetime.utcnow()
        )
    
    async def _aggregate_trending_topics(
        self,
        limit: int,
        timeframe: str
    ) -> List[TrendingTopic]:
        """Aggregate trending topics from multiple sources"""
        # Mock implementation - would integrate with social media APIs
        trending = []
        
        topics = ["DeFi", "NFTs", "Layer2", "Staking", "Governance", "Metaverse", "GameFi", "Web3"]
        
        for i, topic in enumerate(topics[:limit]):
            trending.append(TrendingTopic(
                topic=topic,
                mentions=1000 - (i * 50),
                sentiment=0.2 + (i * 0.1),
                related_tokens=[f"token_{i}", f"token_{i+1}"],
                sources=[SentimentSource.TWITTER, SentimentSource.REDDIT],
                growth_rate=50.0 - (i * 5),
                timestamp=datetime.utcnow()
            ))
        
        return trending
    
    async def _get_social_sentiment(
        self,
        token_address: str,
        timeframe: str
    ) -> Dict[str, Any]:
        """Get sentiment from social media sources"""
        # Mock implementation - would integrate with social media APIs
        return {
            "sentiment": 0.15,
            "positive": 120,
            "negative": 80,
            "neutral": 200,
            "volume": 400,
            "trending": 0.6
        }
    
    async def _get_news_sentiment(
        self,
        token_address: str,
        timeframe: str
    ) -> Dict[str, Any]:
        """Get sentiment from news sources"""
        # Mock implementation - would integrate with news APIs
        return {
            "sentiment": 0.25,
            "article_count": 15,
            "average_relevance": 0.7
        }
    
    def _combine_sentiments(
        self,
        sentiment_weights: List[Tuple[float, float]]
    ) -> float:
        """Combine multiple sentiment scores with weights"""
        total_weight = sum(weight for _, weight in sentiment_weights)
        if total_weight == 0:
            return 0.0
        
        weighted_sum = sum(sentiment * weight for sentiment, weight in sentiment_weights)
        return weighted_sum / total_weight
    
    async def _aggregate_social_metrics(
        self,
        token_address: str,
        timeframe: str
    ) -> Dict[str, Any]:
        """Aggregate social media metrics"""
        # Mock implementation - would integrate with social media APIs
        return {
            "twitter_mentions": 150,
            "reddit_posts": 25,
            "telegram_messages": 300,
            "discord_messages": 80,
            "total_engagement": 2500,
            "sentiment_distribution": {
                "positive": 45,
                "negative": 25,
                "neutral": 30
            },
            "influencer_mentions": 5,
            "viral_posts": 2
        }
    
    async def cleanup(self) -> None:
        """Cleanup HTTP client"""
        try:
            await self.http_client.aclose()
            self.logger.info("News sentiment service cleanup completed")
        except Exception as e:
            self.logger.error("Failed to cleanup news sentiment service", error=str(e))
