"""
Arbitrage Detector

Detects arbitrage opportunities across multiple exchanges
and provides real-time alerts for profitable trades.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTT<PERSON>Exception

from src.features.third_party_integrations.exchange_integrator import (
    ExchangeIntegrator, ExchangeType, ArbitrageOpportunity
)
from src.features.data_pipeline.cache_manager import CacheManager
from src.features.notifications.notification_manager import NotificationManager

logger = structlog.get_logger(__name__)


class ArbitrageType(Enum):
    """Types of arbitrage opportunities"""
    SIMPLE = "simple"  # Direct price difference
    TRIANGULAR = "triangular"  # Three-way arbitrage
    CROSS_EXCHANGE = "cross_exchange"  # Between exchanges


@dataclass
class ArbitrageConfig:
    """Arbitrage detection configuration"""
    min_profit_percentage: float = 1.0
    min_profit_amount: float = 10.0  # USD
    max_trade_amount: float = 10000.0  # USD
    scan_interval: int = 30  # seconds
    alert_threshold: float = 2.0  # Minimum profit % for alerts
    enabled_exchanges: List[ExchangeType] = None
    monitored_symbols: List[str] = None


@dataclass
class ArbitrageAlert:
    """Arbitrage opportunity alert"""
    opportunity: ArbitrageOpportunity
    alert_type: str
    priority: str
    estimated_execution_time: float
    risk_factors: List[str]
    recommended_action: str
    created_at: datetime


class ArbitrageDetector:
    """
    Detects and monitors arbitrage opportunities across exchanges.
    Provides real-time alerts and execution recommendations.
    """
    
    def __init__(
        self,
        exchange_integrator: ExchangeIntegrator,
        cache_manager: CacheManager,
        notification_manager: NotificationManager,
        config: Optional[ArbitrageConfig] = None
    ):
        self.exchange_integrator = exchange_integrator
        self.cache_manager = cache_manager
        self.notification_manager = notification_manager
        self.config = config or ArbitrageConfig()
        self.logger = logger.bind(service="arbitrage_detector")
        self._monitoring = False
    
    async def start_monitoring(self) -> None:
        """Start continuous arbitrage monitoring"""
        if self._monitoring:
            self.logger.warning("Arbitrage monitoring already running")
            return
        
        self._monitoring = True
        self.logger.info("Starting arbitrage monitoring", interval=self.config.scan_interval)
        
        # Start monitoring loop
        asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self) -> None:
        """Stop arbitrage monitoring"""
        self._monitoring = False
        self.logger.info("Arbitrage monitoring stopped")
    
    async def scan_opportunities(
        self,
        symbols: Optional[List[str]] = None,
        exchanges: Optional[List[ExchangeType]] = None
    ) -> List[ArbitrageOpportunity]:
        """
        Scan for arbitrage opportunities.
        
        Args:
            symbols: List of symbols to scan (if None, use configured symbols)
            exchanges: List of exchanges to scan (if None, use configured exchanges)
            
        Returns:
            List of arbitrage opportunities
        """
        try:
            if symbols is None:
                symbols = self.config.monitored_symbols or ["BTCUSDT", "ETHUSDT", "SOLUSDT"]
            
            if exchanges is None:
                exchanges = self.config.enabled_exchanges or list(ExchangeType)
            
            # Detect opportunities
            opportunities = await self.exchange_integrator.detect_arbitrage_opportunities(
                symbols=symbols,
                min_profit_percentage=self.config.min_profit_percentage
            )
            
            # Filter opportunities
            filtered_opportunities = self._filter_opportunities(opportunities)
            
            # Cache opportunities
            await self._cache_opportunities(filtered_opportunities)
            
            # Generate alerts for high-profit opportunities
            await self._generate_alerts(filtered_opportunities)
            
            self.logger.info(
                "Arbitrage scan completed",
                symbols=len(symbols),
                exchanges=len(exchanges),
                opportunities=len(filtered_opportunities)
            )
            
            return filtered_opportunities
            
        except Exception as e:
            self.logger.error("Failed to scan arbitrage opportunities", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to scan arbitrage opportunities")
    
    async def get_opportunity_details(
        self,
        opportunity_id: str
    ) -> Dict[str, Any]:
        """
        Get detailed information about an arbitrage opportunity.
        
        Args:
            opportunity_id: Opportunity identifier
            
        Returns:
            Detailed opportunity information
        """
        try:
            # Get opportunity from cache
            cache_key = f"arbitrage_opportunity:{opportunity_id}"
            opportunity_data = await self.cache_manager.get(cache_key)
            
            if not opportunity_data:
                raise HTTPException(status_code=404, detail="Opportunity not found")
            
            opportunity = ArbitrageOpportunity(**opportunity_data)
            
            # Calculate additional details
            details = await self._calculate_opportunity_details(opportunity)
            
            return {
                "opportunity": opportunity.__dict__,
                "execution_plan": details["execution_plan"],
                "risk_analysis": details["risk_analysis"],
                "profit_analysis": details["profit_analysis"],
                "market_conditions": details["market_conditions"]
            }
            
        except Exception as e:
            self.logger.error("Failed to get opportunity details", opportunity_id=opportunity_id, error=str(e))
            raise
    
    async def calculate_execution_plan(
        self,
        opportunity: ArbitrageOpportunity,
        trade_amount: float
    ) -> Dict[str, Any]:
        """
        Calculate execution plan for an arbitrage opportunity.
        
        Args:
            opportunity: Arbitrage opportunity
            trade_amount: Amount to trade in USD
            
        Returns:
            Execution plan details
        """
        try:
            # Calculate trade quantities
            buy_quantity = trade_amount / opportunity.buy_price
            sell_quantity = buy_quantity  # Assuming 1:1 for simplicity
            
            # Estimate fees
            buy_fees = await self._estimate_trading_fees(
                opportunity.buy_exchange, 
                opportunity.symbol, 
                trade_amount
            )
            sell_fees = await self._estimate_trading_fees(
                opportunity.sell_exchange, 
                opportunity.symbol, 
                trade_amount
            )
            
            # Calculate net profit
            gross_profit = (opportunity.sell_price - opportunity.buy_price) * buy_quantity
            total_fees = buy_fees + sell_fees
            net_profit = gross_profit - total_fees
            net_profit_percentage = (net_profit / trade_amount) * 100
            
            # Estimate execution time
            execution_time = await self._estimate_execution_time(opportunity)
            
            execution_plan = {
                "trade_amount_usd": trade_amount,
                "buy_exchange": opportunity.buy_exchange.value,
                "sell_exchange": opportunity.sell_exchange.value,
                "buy_quantity": buy_quantity,
                "sell_quantity": sell_quantity,
                "buy_price": opportunity.buy_price,
                "sell_price": opportunity.sell_price,
                "gross_profit": gross_profit,
                "buy_fees": buy_fees,
                "sell_fees": sell_fees,
                "total_fees": total_fees,
                "net_profit": net_profit,
                "net_profit_percentage": net_profit_percentage,
                "estimated_execution_time": execution_time,
                "steps": [
                    {
                        "step": 1,
                        "action": "buy",
                        "exchange": opportunity.buy_exchange.value,
                        "quantity": buy_quantity,
                        "price": opportunity.buy_price,
                        "estimated_time": execution_time / 2
                    },
                    {
                        "step": 2,
                        "action": "sell",
                        "exchange": opportunity.sell_exchange.value,
                        "quantity": sell_quantity,
                        "price": opportunity.sell_price,
                        "estimated_time": execution_time / 2
                    }
                ]
            }
            
            return execution_plan
            
        except Exception as e:
            self.logger.error("Failed to calculate execution plan", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to calculate execution plan")
    
    async def get_arbitrage_statistics(
        self,
        timeframe: str = "24h"
    ) -> Dict[str, Any]:
        """
        Get arbitrage statistics and analytics.
        
        Args:
            timeframe: Statistics timeframe
            
        Returns:
            Arbitrage statistics
        """
        try:
            # Parse timeframe
            hours = 24
            if timeframe.endswith('h'):
                hours = int(timeframe[:-1])
            elif timeframe.endswith('d'):
                hours = int(timeframe[:-1]) * 24
            
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            # Get historical opportunities
            opportunities = await self._get_historical_opportunities(start_time)
            
            if not opportunities:
                return {
                    "timeframe": timeframe,
                    "total_opportunities": 0,
                    "average_profit": 0.0,
                    "best_opportunity": None,
                    "exchange_pairs": {},
                    "symbol_distribution": {}
                }
            
            # Calculate statistics
            total_opportunities = len(opportunities)
            average_profit = sum(opp.profit_percentage for opp in opportunities) / total_opportunities
            best_opportunity = max(opportunities, key=lambda x: x.profit_percentage)
            
            # Exchange pair analysis
            exchange_pairs = {}
            for opp in opportunities:
                pair_key = f"{opp.buy_exchange.value}->{opp.sell_exchange.value}"
                if pair_key not in exchange_pairs:
                    exchange_pairs[pair_key] = {"count": 0, "avg_profit": 0.0}
                exchange_pairs[pair_key]["count"] += 1
                exchange_pairs[pair_key]["avg_profit"] += opp.profit_percentage
            
            # Calculate averages
            for pair in exchange_pairs.values():
                pair["avg_profit"] /= pair["count"]
            
            # Symbol distribution
            symbol_distribution = {}
            for opp in opportunities:
                if opp.symbol not in symbol_distribution:
                    symbol_distribution[opp.symbol] = 0
                symbol_distribution[opp.symbol] += 1
            
            statistics = {
                "timeframe": timeframe,
                "total_opportunities": total_opportunities,
                "average_profit": average_profit,
                "best_opportunity": {
                    "symbol": best_opportunity.symbol,
                    "profit_percentage": best_opportunity.profit_percentage,
                    "buy_exchange": best_opportunity.buy_exchange.value,
                    "sell_exchange": best_opportunity.sell_exchange.value,
                    "timestamp": best_opportunity.timestamp
                },
                "exchange_pairs": exchange_pairs,
                "symbol_distribution": symbol_distribution,
                "opportunities_per_hour": total_opportunities / hours if hours > 0 else 0
            }
            
            return statistics
            
        except Exception as e:
            self.logger.error("Failed to get arbitrage statistics", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get arbitrage statistics")
    
    async def _monitoring_loop(self) -> None:
        """Continuous monitoring loop"""
        while self._monitoring:
            try:
                await self.scan_opportunities()
                await asyncio.sleep(self.config.scan_interval)
            except Exception as e:
                self.logger.error("Error in monitoring loop", error=str(e))
                await asyncio.sleep(self.config.scan_interval)
    
    def _filter_opportunities(
        self,
        opportunities: List[ArbitrageOpportunity]
    ) -> List[ArbitrageOpportunity]:
        """Filter opportunities based on configuration"""
        filtered = []
        
        for opp in opportunities:
            # Check minimum profit percentage
            if opp.profit_percentage < self.config.min_profit_percentage:
                continue
            
            # Check minimum profit amount
            if opp.profit_amount < self.config.min_profit_amount:
                continue
            
            # Check if exchanges are enabled
            if (self.config.enabled_exchanges and 
                (opp.buy_exchange not in self.config.enabled_exchanges or
                 opp.sell_exchange not in self.config.enabled_exchanges)):
                continue
            
            # Check if symbol is monitored
            if (self.config.monitored_symbols and 
                opp.symbol not in self.config.monitored_symbols):
                continue
            
            filtered.append(opp)
        
        return filtered
    
    async def _cache_opportunities(
        self,
        opportunities: List[ArbitrageOpportunity]
    ) -> None:
        """Cache arbitrage opportunities"""
        try:
            for i, opp in enumerate(opportunities):
                opportunity_id = f"{opp.symbol}_{opp.buy_exchange.value}_{opp.sell_exchange.value}_{i}"
                cache_key = f"arbitrage_opportunity:{opportunity_id}"
                
                await self.cache_manager.set(
                    cache_key,
                    opp.__dict__,
                    ttl=300  # 5 minutes
                )
            
            # Cache opportunity list
            list_cache_key = "arbitrage_opportunities_list"
            opportunity_ids = [
                f"{opp.symbol}_{opp.buy_exchange.value}_{opp.sell_exchange.value}_{i}"
                for i, opp in enumerate(opportunities)
            ]
            
            await self.cache_manager.set(
                list_cache_key,
                opportunity_ids,
                ttl=300
            )
            
        except Exception as e:
            self.logger.error("Failed to cache opportunities", error=str(e))
    
    async def _generate_alerts(
        self,
        opportunities: List[ArbitrageOpportunity]
    ) -> None:
        """Generate alerts for high-profit opportunities"""
        try:
            for opp in opportunities:
                if opp.profit_percentage >= self.config.alert_threshold:
                    alert = ArbitrageAlert(
                        opportunity=opp,
                        alert_type="high_profit_arbitrage",
                        priority="high" if opp.profit_percentage >= 5.0 else "medium",
                        estimated_execution_time=await self._estimate_execution_time(opp),
                        risk_factors=await self._assess_risk_factors(opp),
                        recommended_action="execute" if opp.profit_percentage >= 3.0 else "monitor",
                        created_at=datetime.utcnow()
                    )
                    
                    # Send notification
                    await self._send_arbitrage_alert(alert)
                    
        except Exception as e:
            self.logger.error("Failed to generate alerts", error=str(e))
    
    async def _calculate_opportunity_details(
        self,
        opportunity: ArbitrageOpportunity
    ) -> Dict[str, Any]:
        """Calculate detailed opportunity information"""
        # Mock implementation - would include real market analysis
        return {
            "execution_plan": await self.calculate_execution_plan(opportunity, 1000.0),
            "risk_analysis": {
                "risk_level": "medium",
                "risk_factors": ["price_volatility", "execution_delay"],
                "confidence_score": 0.75
            },
            "profit_analysis": {
                "profit_potential": "high",
                "sustainability": "short_term",
                "market_impact": "low"
            },
            "market_conditions": {
                "volatility": "normal",
                "liquidity": "good",
                "spread_stability": "stable"
            }
        }
    
    async def _estimate_trading_fees(
        self,
        exchange: ExchangeType,
        symbol: str,
        trade_amount: float
    ) -> float:
        """Estimate trading fees for an exchange"""
        # Get fee structure from exchange integrator
        fees = await self.exchange_integrator.get_exchange_trading_fees(exchange)
        
        # Use taker fee as conservative estimate
        fee_rate = fees.get("taker_fee", 0.001)  # Default 0.1%
        return trade_amount * fee_rate
    
    async def _estimate_execution_time(
        self,
        opportunity: ArbitrageOpportunity
    ) -> float:
        """Estimate execution time for arbitrage opportunity"""
        # Mock implementation - would consider exchange latency, order book depth, etc.
        base_time = 30.0  # 30 seconds base execution time
        
        # Add complexity factors
        if opportunity.buy_exchange != opportunity.sell_exchange:
            base_time += 15.0  # Cross-exchange adds time
        
        return base_time
    
    async def _assess_risk_factors(
        self,
        opportunity: ArbitrageOpportunity
    ) -> List[str]:
        """Assess risk factors for an opportunity"""
        risk_factors = []
        
        if opportunity.profit_percentage > 10.0:
            risk_factors.append("unusually_high_profit")
        
        if opportunity.volume < 1000.0:
            risk_factors.append("low_liquidity")
        
        # Add more risk assessment logic
        
        return risk_factors
    
    async def _send_arbitrage_alert(self, alert: ArbitrageAlert) -> None:
        """Send arbitrage alert notification"""
        try:
            message = (
                f"🚨 Arbitrage Alert: {alert.opportunity.profit_percentage:.2f}% profit "
                f"on {alert.opportunity.symbol} "
                f"({alert.opportunity.buy_exchange.value} → {alert.opportunity.sell_exchange.value})"
            )
            
            # Send notification (would integrate with notification manager)
            self.logger.info("Arbitrage alert generated", alert=message)
            
        except Exception as e:
            self.logger.error("Failed to send arbitrage alert", error=str(e))
    
    async def _get_historical_opportunities(
        self,
        start_time: datetime
    ) -> List[ArbitrageOpportunity]:
        """Get historical arbitrage opportunities"""
        # Mock implementation - would query database
        return []
