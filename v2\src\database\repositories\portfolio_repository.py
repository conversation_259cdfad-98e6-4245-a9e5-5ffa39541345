"""
📊 Portfolio Repository

Repository for portfolio data access operations.
"""

from typing import List, Optional, Dict, Any
from ..models.portfolio import Portfolio
from .base_repository import BaseRepository


class PortfolioRepository(BaseRepository):
    """Repository for portfolio operations"""
    
    def __init__(self):
        super().__init__(Portfolio)
    
    async def get_by_user_id(self, user_id: str) -> List[Portfolio]:
        """Get portfolios by user ID"""
        return await self.find_many({"user_id": user_id})
    
    async def get_active_portfolios(self, user_id: str) -> List[Portfolio]:
        """Get active portfolios for user"""
        return await self.find_many({"user_id": user_id, "status": "active"})
