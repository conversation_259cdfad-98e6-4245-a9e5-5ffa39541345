#!/usr/bin/env python3
"""
🏥 Health Check Script for CI/CD Pipeline

Comprehensive health checking for TokenTracker V2 deployments.
Used in CI/CD pipeline to verify deployment health before traffic switching.
"""

import asyncio
import argparse
import sys
import time
from typing import Dict, Any, List
import httpx
import json
from datetime import datetime

# Health check configuration
HEALTH_CHECKS = {
    "api_health": {
        "endpoint": "/health",
        "timeout": 10,
        "expected_status": 200,
        "critical": True
    },
    "api_ready": {
        "endpoint": "/ready", 
        "timeout": 10,
        "expected_status": 200,
        "critical": True
    },
    "database_health": {
        "endpoint": "/health/database",
        "timeout": 15,
        "expected_status": 200,
        "critical": True
    },
    "cache_health": {
        "endpoint": "/health/cache",
        "timeout": 10,
        "expected_status": 200,
        "critical": False
    },
    "metrics": {
        "endpoint": "/metrics",
        "timeout": 10,
        "expected_status": 200,
        "critical": False
    },
    "api_version": {
        "endpoint": "/api/version",
        "timeout": 5,
        "expected_status": 200,
        "critical": False
    }
}

class HealthChecker:
    """Comprehensive health checker for TokenTracker V2"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.client = httpx.AsyncClient(timeout=timeout)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return results"""
        print(f"🏥 Running health checks for: {self.base_url}")
        print("=" * 60)
        
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "base_url": self.base_url,
            "overall_status": "healthy",
            "checks": {},
            "summary": {
                "total": len(HEALTH_CHECKS),
                "passed": 0,
                "failed": 0,
                "critical_failed": 0
            }
        }
        
        # Run all health checks
        for check_name, check_config in HEALTH_CHECKS.items():
            print(f"🔍 Checking {check_name}...")
            
            try:
                check_result = await self._run_single_check(check_name, check_config)
                results["checks"][check_name] = check_result
                
                if check_result["status"] == "passed":
                    results["summary"]["passed"] += 1
                    print(f"  ✅ {check_name}: PASSED")
                else:
                    results["summary"]["failed"] += 1
                    if check_config.get("critical", False):
                        results["summary"]["critical_failed"] += 1
                    print(f"  ❌ {check_name}: FAILED - {check_result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                results["checks"][check_name] = {
                    "status": "failed",
                    "error": str(e),
                    "critical": check_config.get("critical", False)
                }
                results["summary"]["failed"] += 1
                if check_config.get("critical", False):
                    results["summary"]["critical_failed"] += 1
                print(f"  ❌ {check_name}: FAILED - {str(e)}")
        
        # Determine overall status
        if results["summary"]["critical_failed"] > 0:
            results["overall_status"] = "critical"
        elif results["summary"]["failed"] > 0:
            results["overall_status"] = "degraded"
        else:
            results["overall_status"] = "healthy"
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 HEALTH CHECK SUMMARY")
        print("=" * 60)
        print(f"Overall Status: {results['overall_status'].upper()}")
        print(f"Total Checks: {results['summary']['total']}")
        print(f"Passed: {results['summary']['passed']}")
        print(f"Failed: {results['summary']['failed']}")
        print(f"Critical Failed: {results['summary']['critical_failed']}")
        
        return results
    
    async def _run_single_check(self, check_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single health check"""
        start_time = time.time()
        
        try:
            url = f"{self.base_url}{config['endpoint']}"
            response = await self.client.get(
                url,
                timeout=config.get("timeout", 10)
            )
            
            response_time = time.time() - start_time
            
            # Check status code
            expected_status = config.get("expected_status", 200)
            if response.status_code != expected_status:
                return {
                    "status": "failed",
                    "error": f"Expected status {expected_status}, got {response.status_code}",
                    "response_time": response_time,
                    "critical": config.get("critical", False)
                }
            
            # Try to parse JSON response for additional validation
            try:
                response_data = response.json()
                
                # Validate health endpoint responses
                if check_name in ["api_health", "api_ready"]:
                    if not response_data.get("status") == "healthy":
                        return {
                            "status": "failed",
                            "error": f"Health status is not healthy: {response_data.get('status')}",
                            "response_time": response_time,
                            "response_data": response_data,
                            "critical": config.get("critical", False)
                        }
                
                return {
                    "status": "passed",
                    "response_time": response_time,
                    "response_data": response_data,
                    "critical": config.get("critical", False)
                }
                
            except json.JSONDecodeError:
                # Non-JSON response is OK for some endpoints
                return {
                    "status": "passed",
                    "response_time": response_time,
                    "response_text": response.text[:200],  # First 200 chars
                    "critical": config.get("critical", False)
                }
                
        except httpx.TimeoutException:
            return {
                "status": "failed",
                "error": f"Request timeout after {config.get('timeout', 10)}s",
                "response_time": time.time() - start_time,
                "critical": config.get("critical", False)
            }
        except httpx.ConnectError:
            return {
                "status": "failed",
                "error": "Connection failed - service may be down",
                "response_time": time.time() - start_time,
                "critical": config.get("critical", False)
            }
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "response_time": time.time() - start_time,
                "critical": config.get("critical", False)
            }

async def main():
    """Main health check function"""
    parser = argparse.ArgumentParser(description="TokenTracker V2 Health Check")
    parser.add_argument("--url", required=True, help="Base URL to check")
    parser.add_argument("--timeout", type=int, default=30, help="Request timeout in seconds")
    parser.add_argument("--target", help="Deployment target (staging/production/green)")
    parser.add_argument("--namespace", help="Kubernetes namespace")
    parser.add_argument("--output", help="Output file for results (JSON)")
    parser.add_argument("--fail-on-degraded", action="store_true", help="Fail if status is degraded")
    
    args = parser.parse_args()
    
    # Determine URL based on target and namespace if provided
    if args.target and args.namespace:
        if args.target == "green":
            # For green deployment health checks
            service_url = f"http://tokentracker-v2-green.{args.namespace}.svc.cluster.local:8000"
        elif args.target == "staging":
            service_url = "https://staging.tokentracker.com"
        elif args.target == "production":
            service_url = "https://tokentracker.com"
        else:
            service_url = args.url
    else:
        service_url = args.url
    
    print(f"🎯 Target: {args.target or 'custom'}")
    print(f"🌐 URL: {service_url}")
    print(f"⏱️ Timeout: {args.timeout}s")
    print()
    
    # Run health checks
    async with HealthChecker(service_url, args.timeout) as checker:
        results = await checker.run_all_checks()
    
    # Save results if output file specified
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Results saved to: {args.output}")
    
    # Determine exit code
    overall_status = results["overall_status"]
    
    if overall_status == "critical":
        print("\n🚨 CRITICAL: Health checks failed!")
        sys.exit(1)
    elif overall_status == "degraded":
        if args.fail_on_degraded:
            print("\n⚠️ DEGRADED: Some health checks failed!")
            sys.exit(1)
        else:
            print("\n⚠️ DEGRADED: Some non-critical health checks failed, but continuing...")
            sys.exit(0)
    else:
        print("\n✅ HEALTHY: All health checks passed!")
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())
