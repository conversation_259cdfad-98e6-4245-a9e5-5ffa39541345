"""
📊 Market Regime Detector

Advanced market regime detection using statistical analysis and machine learning
to identify bull markets, bear markets, sideways trends, and volatility regimes.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import MarketData
from ...database.models import Token
from ...features.data_pipeline import DataAggregator

logger = get_logger(__name__)


class MarketRegime(str, Enum):
    """Market regime types"""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    ACCUMULATION = "accumulation"
    DISTRIBUTION = "distribution"
    BREAKOUT = "breakout"
    BREAKDOWN = "breakdown"


class RegimeStrength(str, Enum):
    """Regime strength levels"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"


class MarketRegimeDetector:
    """
    📊 Market Regime Detector
    
    Detects and analyzes market regimes using:
    - Trend analysis and momentum indicators
    - Volatility regime detection
    - Volume analysis and accumulation/distribution
    - Statistical regime change detection
    - Multi-timeframe regime analysis
    - Regime transition predictions
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        
        # Regime detection parameters
        self.trend_lookback = 30  # days
        self.volatility_window = 20  # days
        self.volume_window = 14  # days
        self.regime_confidence_threshold = 0.7
        
        # Regime thresholds
        self.bull_threshold = 0.15  # 15% gain over lookback period
        self.bear_threshold = -0.15  # 15% loss over lookback period
        self.high_vol_threshold = 0.04  # 4% daily volatility
        self.low_vol_threshold = 0.015  # 1.5% daily volatility
        
        # Regime cache
        self.regime_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 3600  # 1 hour
    
    async def detect_market_regime(
        self,
        token_address: str,
        timeframes: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Detect current market regime for a token
        
        Args:
            token_address: Token address
            timeframes: List of timeframes to analyze
            
        Returns:
            Market regime analysis
        """
        try:
            self.logger.info(f"Detecting market regime for {token_address}")
            
            # Check cache
            cache_key = f"{token_address}_regime"
            if self._is_cache_valid(cache_key):
                return self.regime_cache[cache_key]
            
            # Default timeframes
            if not timeframes:
                timeframes = ["1h", "4h", "1d", "1w"]
            
            # Get market data
            market_data = await self._get_market_data(token_address, days=90)
            
            if len(market_data) < 30:
                raise ValueError("Insufficient data for regime detection")
            
            # Analyze different aspects
            trend_regime = await self._detect_trend_regime(market_data)
            volatility_regime = await self._detect_volatility_regime(market_data)
            volume_regime = await self._detect_volume_regime(market_data)
            momentum_regime = await self._detect_momentum_regime(market_data)
            
            # Multi-timeframe analysis
            timeframe_regimes = {}
            for tf in timeframes:
                tf_data = self._resample_data(market_data, tf)
                tf_regime = await self._analyze_timeframe_regime(tf_data)
                timeframe_regimes[tf] = tf_regime
            
            # Combine regimes
            primary_regime = self._determine_primary_regime(
                trend_regime, volatility_regime, volume_regime, momentum_regime
            )
            
            # Calculate regime confidence
            confidence = self._calculate_regime_confidence(
                trend_regime, volatility_regime, volume_regime, momentum_regime
            )
            
            # Detect regime transitions
            regime_transition = await self._detect_regime_transition(market_data)
            
            # Generate regime signals
            regime_signals = self._generate_regime_signals(primary_regime, confidence)
            
            result = {
                "token_address": token_address,
                "analysis_time": datetime.utcnow(),
                "primary_regime": primary_regime,
                "regime_confidence": confidence,
                "regime_components": {
                    "trend": trend_regime,
                    "volatility": volatility_regime,
                    "volume": volume_regime,
                    "momentum": momentum_regime
                },
                "timeframe_analysis": timeframe_regimes,
                "regime_transition": regime_transition,
                "regime_signals": regime_signals
            }
            
            # Cache result
            self.regime_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {str(e)}")
            raise
    
    async def get_regime_history(
        self,
        token_address: str,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get historical regime analysis"""
        try:
            # Get historical data
            market_data = await self._get_market_data(token_address, days=days)
            
            # Analyze regime changes over time
            regime_history = []
            window_size = 7  # 7-day windows
            
            for i in range(window_size, len(market_data), window_size):
                window_data = market_data.iloc[i-window_size:i]
                
                if len(window_data) >= window_size:
                    trend_regime = await self._detect_trend_regime(window_data)
                    vol_regime = await self._detect_volatility_regime(window_data)
                    
                    primary_regime = self._determine_primary_regime(
                        trend_regime, vol_regime, {}, {}
                    )
                    
                    regime_history.append({
                        "date": window_data.index[-1],
                        "regime": primary_regime["regime"],
                        "strength": primary_regime["strength"],
                        "trend_return": trend_regime["return_pct"],
                        "volatility": vol_regime["current_volatility"]
                    })
            
            return {
                "token_address": token_address,
                "regime_history": regime_history,
                "analysis_period_days": days
            }
            
        except Exception as e:
            self.logger.error(f"Error getting regime history: {str(e)}")
            raise
    
    async def _detect_trend_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect trend-based regime"""
        try:
            # Calculate returns over different periods
            current_price = data['price'].iloc[-1]
            price_30d_ago = data['price'].iloc[-min(30, len(data))]
            price_7d_ago = data['price'].iloc[-min(7, len(data))]
            
            return_30d = (current_price - price_30d_ago) / price_30d_ago
            return_7d = (current_price - price_7d_ago) / price_7d_ago
            
            # Moving averages
            sma_20 = data['price'].rolling(window=20).mean().iloc[-1]
            sma_50 = data['price'].rolling(window=min(50, len(data))).mean().iloc[-1]
            
            # Determine trend regime
            if return_30d > self.bull_threshold and current_price > sma_20 > sma_50:
                regime = MarketRegime.BULL_MARKET
                strength = RegimeStrength.STRONG if return_30d > 0.3 else RegimeStrength.MODERATE
            elif return_30d < self.bear_threshold and current_price < sma_20 < sma_50:
                regime = MarketRegime.BEAR_MARKET
                strength = RegimeStrength.STRONG if return_30d < -0.3 else RegimeStrength.MODERATE
            else:
                regime = MarketRegime.SIDEWAYS
                strength = RegimeStrength.WEAK
            
            return {
                "regime": regime,
                "strength": strength,
                "return_30d": float(return_30d),
                "return_7d": float(return_7d),
                "price_vs_sma20": float((current_price - sma_20) / sma_20),
                "sma20_vs_sma50": float((sma_20 - sma_50) / sma_50)
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting trend regime: {str(e)}")
            return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}
    
    async def _detect_volatility_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect volatility-based regime"""
        try:
            # Calculate daily returns
            returns = data['price'].pct_change().dropna()
            
            # Current volatility (20-day rolling)
            current_vol = returns.rolling(window=min(20, len(returns))).std().iloc[-1]
            
            # Historical volatility percentile
            vol_series = returns.rolling(window=min(20, len(returns))).std()
            vol_percentile = (vol_series <= current_vol).mean()
            
            # Determine volatility regime
            if current_vol > self.high_vol_threshold:
                regime = MarketRegime.HIGH_VOLATILITY
                strength = RegimeStrength.STRONG if current_vol > 0.06 else RegimeStrength.MODERATE
            elif current_vol < self.low_vol_threshold:
                regime = MarketRegime.LOW_VOLATILITY
                strength = RegimeStrength.STRONG if current_vol < 0.01 else RegimeStrength.MODERATE
            else:
                regime = MarketRegime.SIDEWAYS
                strength = RegimeStrength.MODERATE
            
            return {
                "regime": regime,
                "strength": strength,
                "current_volatility": float(current_vol),
                "volatility_percentile": float(vol_percentile),
                "volatility_trend": "increasing" if vol_percentile > 0.7 else "decreasing" if vol_percentile < 0.3 else "stable"
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting volatility regime: {str(e)}")
            return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}
    
    async def _detect_volume_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect volume-based regime"""
        try:
            # Volume analysis
            current_volume = data['volume'].iloc[-1]
            avg_volume = data['volume'].rolling(window=min(20, len(data))).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Volume trend
            volume_trend = data['volume'].rolling(window=min(7, len(data))).mean().iloc[-1] / \
                          data['volume'].rolling(window=min(14, len(data))).mean().iloc[-1]
            
            # On-Balance Volume (OBV)
            price_change = data['price'].diff()
            obv = (data['volume'] * np.sign(price_change)).cumsum()
            obv_trend = obv.iloc[-1] / obv.iloc[-min(20, len(obv))] if len(obv) > 20 else 1
            
            # Determine volume regime
            if volume_ratio > 2 and obv_trend > 1.1:
                regime = MarketRegime.ACCUMULATION
                strength = RegimeStrength.STRONG
            elif volume_ratio > 2 and obv_trend < 0.9:
                regime = MarketRegime.DISTRIBUTION
                strength = RegimeStrength.STRONG
            elif volume_ratio > 1.5:
                regime = MarketRegime.BREAKOUT if obv_trend > 1 else MarketRegime.BREAKDOWN
                strength = RegimeStrength.MODERATE
            else:
                regime = MarketRegime.SIDEWAYS
                strength = RegimeStrength.WEAK
            
            return {
                "regime": regime,
                "strength": strength,
                "volume_ratio": float(volume_ratio),
                "volume_trend": float(volume_trend),
                "obv_trend": float(obv_trend)
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting volume regime: {str(e)}")
            return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}
    
    async def _detect_momentum_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect momentum-based regime"""
        try:
            # RSI
            delta = data['price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1]
            
            # MACD
            ema_12 = data['price'].ewm(span=12).mean()
            ema_26 = data['price'].ewm(span=26).mean()
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean()
            macd_histogram = macd - macd_signal
            
            # Momentum signals
            momentum_bullish = (
                current_rsi > 50 and
                macd.iloc[-1] > macd_signal.iloc[-1] and
                macd_histogram.iloc[-1] > 0
            )
            
            momentum_bearish = (
                current_rsi < 50 and
                macd.iloc[-1] < macd_signal.iloc[-1] and
                macd_histogram.iloc[-1] < 0
            )
            
            # Determine momentum regime
            if momentum_bullish and current_rsi > 60:
                regime = MarketRegime.BULL_MARKET
                strength = RegimeStrength.STRONG if current_rsi > 70 else RegimeStrength.MODERATE
            elif momentum_bearish and current_rsi < 40:
                regime = MarketRegime.BEAR_MARKET
                strength = RegimeStrength.STRONG if current_rsi < 30 else RegimeStrength.MODERATE
            else:
                regime = MarketRegime.SIDEWAYS
                strength = RegimeStrength.WEAK
            
            return {
                "regime": regime,
                "strength": strength,
                "rsi": float(current_rsi),
                "macd": float(macd.iloc[-1]),
                "macd_signal": float(macd_signal.iloc[-1]),
                "macd_histogram": float(macd_histogram.iloc[-1])
            }
            
        except Exception as e:
            self.logger.error(f"Error detecting momentum regime: {str(e)}")
            return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}
    
    def _determine_primary_regime(
        self,
        trend_regime: Dict[str, Any],
        volatility_regime: Dict[str, Any],
        volume_regime: Dict[str, Any],
        momentum_regime: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Determine primary regime from component analyses"""
        try:
            # Weight different regime components
            regime_votes = {}
            
            # Add weighted votes
            regimes = [trend_regime, volatility_regime, volume_regime, momentum_regime]
            weights = [0.4, 0.2, 0.2, 0.2]  # Trend gets highest weight
            
            for regime_data, weight in zip(regimes, weights):
                if regime_data:
                    regime_type = regime_data.get("regime", MarketRegime.SIDEWAYS)
                    strength = regime_data.get("strength", RegimeStrength.WEAK)
                    
                    # Convert strength to numeric
                    strength_value = {
                        RegimeStrength.WEAK: 1,
                        RegimeStrength.MODERATE: 2,
                        RegimeStrength.STRONG: 3,
                        RegimeStrength.VERY_STRONG: 4
                    }.get(strength, 1)
                    
                    vote_weight = weight * strength_value
                    regime_votes[regime_type] = regime_votes.get(regime_type, 0) + vote_weight
            
            # Determine primary regime
            if regime_votes:
                primary_regime = max(regime_votes, key=regime_votes.get)
                max_vote = regime_votes[primary_regime]
                
                # Determine overall strength
                if max_vote > 3:
                    strength = RegimeStrength.VERY_STRONG
                elif max_vote > 2:
                    strength = RegimeStrength.STRONG
                elif max_vote > 1:
                    strength = RegimeStrength.MODERATE
                else:
                    strength = RegimeStrength.WEAK
            else:
                primary_regime = MarketRegime.SIDEWAYS
                strength = RegimeStrength.WEAK
            
            return {
                "regime": primary_regime,
                "strength": strength,
                "regime_votes": regime_votes
            }
            
        except Exception as e:
            self.logger.error(f"Error determining primary regime: {str(e)}")
            return {
                "regime": MarketRegime.SIDEWAYS,
                "strength": RegimeStrength.WEAK,
                "regime_votes": {}
            }

    def _calculate_regime_confidence(
        self,
        trend_regime: Dict[str, Any],
        volatility_regime: Dict[str, Any],
        volume_regime: Dict[str, Any],
        momentum_regime: Dict[str, Any]
    ) -> float:
        """Calculate confidence in regime detection"""
        try:
            # Count strong signals
            strong_signals = 0
            total_signals = 0

            for regime_data in [trend_regime, volatility_regime, volume_regime, momentum_regime]:
                if regime_data:
                    total_signals += 1
                    strength = regime_data.get("strength", RegimeStrength.WEAK)
                    if strength in [RegimeStrength.STRONG, RegimeStrength.VERY_STRONG]:
                        strong_signals += 1

            # Base confidence on signal strength
            confidence = strong_signals / total_signals if total_signals > 0 else 0

            return min(confidence, 1.0)

        except Exception as e:
            self.logger.error(f"Error calculating regime confidence: {str(e)}")
            return 0.0

    async def _detect_regime_transition(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Detect potential regime transitions"""
        try:
            # Compare recent regime with historical regime
            recent_data = data.tail(7)  # Last 7 days
            historical_data = data.tail(30).head(23)  # Previous 23 days

            if len(recent_data) < 7 or len(historical_data) < 20:
                return {"transition_detected": False, "transition_type": None}

            # Analyze recent vs historical
            recent_trend = await self._detect_trend_regime(recent_data)
            historical_trend = await self._detect_trend_regime(historical_data)

            recent_vol = await self._detect_volatility_regime(recent_data)
            historical_vol = await self._detect_volatility_regime(historical_data)

            # Check for regime changes
            trend_changed = recent_trend["regime"] != historical_trend["regime"]
            vol_changed = recent_vol["regime"] != historical_vol["regime"]

            if trend_changed or vol_changed:
                transition_type = f"{historical_trend['regime']}_to_{recent_trend['regime']}"
                confidence = 0.8 if trend_changed and vol_changed else 0.6

                return {
                    "transition_detected": True,
                    "transition_type": transition_type,
                    "confidence": confidence,
                    "from_regime": historical_trend["regime"],
                    "to_regime": recent_trend["regime"],
                    "trend_changed": trend_changed,
                    "volatility_changed": vol_changed
                }

            return {"transition_detected": False, "transition_type": None}

        except Exception as e:
            self.logger.error(f"Error detecting regime transition: {str(e)}")
            return {"transition_detected": False, "transition_type": None}

    def _generate_regime_signals(
        self,
        primary_regime: Dict[str, Any],
        confidence: float
    ) -> List[Dict[str, Any]]:
        """Generate trading signals based on regime"""
        try:
            signals = []
            regime = primary_regime["regime"]
            strength = primary_regime["strength"]

            if confidence < 0.5:
                return signals  # Low confidence, no signals

            # Bull market signals
            if regime == MarketRegime.BULL_MARKET and strength in [RegimeStrength.STRONG, RegimeStrength.VERY_STRONG]:
                signals.append({
                    "signal_type": "BUY",
                    "reason": f"Strong bull market regime detected",
                    "confidence": confidence,
                    "regime": regime,
                    "strength": strength
                })

            # Bear market signals
            elif regime == MarketRegime.BEAR_MARKET and strength in [RegimeStrength.STRONG, RegimeStrength.VERY_STRONG]:
                signals.append({
                    "signal_type": "SELL",
                    "reason": f"Strong bear market regime detected",
                    "confidence": confidence,
                    "regime": regime,
                    "strength": strength
                })

            # High volatility signals
            elif regime == MarketRegime.HIGH_VOLATILITY:
                signals.append({
                    "signal_type": "REDUCE_POSITION",
                    "reason": "High volatility regime - reduce position size",
                    "confidence": confidence,
                    "regime": regime,
                    "strength": strength
                })

            # Accumulation signals
            elif regime == MarketRegime.ACCUMULATION:
                signals.append({
                    "signal_type": "BUY",
                    "reason": "Accumulation regime detected",
                    "confidence": confidence * 0.8,  # Slightly lower confidence
                    "regime": regime,
                    "strength": strength
                })

            # Distribution signals
            elif regime == MarketRegime.DISTRIBUTION:
                signals.append({
                    "signal_type": "SELL",
                    "reason": "Distribution regime detected",
                    "confidence": confidence * 0.8,
                    "regime": regime,
                    "strength": strength
                })

            return signals

        except Exception as e:
            self.logger.error(f"Error generating regime signals: {str(e)}")
            return []

    async def _analyze_timeframe_regime(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze regime for a specific timeframe"""
        try:
            if len(data) < 10:
                return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}

            # Simplified timeframe analysis
            trend_regime = await self._detect_trend_regime(data)
            vol_regime = await self._detect_volatility_regime(data)

            # Combine for timeframe regime
            primary_regime = self._determine_primary_regime(trend_regime, vol_regime, {}, {})

            return primary_regime

        except Exception as e:
            self.logger.error(f"Error analyzing timeframe regime: {str(e)}")
            return {"regime": MarketRegime.SIDEWAYS, "strength": RegimeStrength.WEAK}

    def _resample_data(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample data to different timeframe"""
        try:
            # Convert timeframe to pandas frequency
            freq_map = {
                "1h": "1H",
                "4h": "4H",
                "1d": "1D",
                "1w": "1W"
            }

            freq = freq_map.get(timeframe, "1H")

            # Ensure datetime index
            if 'timestamp' in data.columns:
                data = data.set_index('timestamp')

            # Resample OHLCV data
            resampled = data.resample(freq).agg({
                'price': 'last',
                'volume': 'sum'
            }).dropna()

            return resampled

        except Exception as e:
            self.logger.error(f"Error resampling data: {str(e)}")
            return data

    async def _get_market_data(self, token_address: str, days: int) -> pd.DataFrame:
        """Get market data for analysis"""
        try:
            # For now, generate synthetic data
            # In a real implementation, this would fetch from database/API

            hours = days * 24
            timestamps = pd.date_range(
                start=datetime.utcnow() - timedelta(days=days),
                periods=hours,
                freq='H'
            )

            # Generate synthetic price data with trends
            np.random.seed(42)
            price_base = 50
            trend = np.random.choice([-0.001, 0, 0.001], size=hours, p=[0.3, 0.4, 0.3])
            noise = np.random.normal(0, 0.02, hours)

            prices = [price_base]
            for i in range(1, hours):
                change = trend[i] + noise[i]
                new_price = prices[-1] * (1 + change)
                prices.append(max(new_price, 0.01))

            # Generate volume data
            volumes = np.random.lognormal(10, 1, hours)

            data = pd.DataFrame({
                'timestamp': timestamps,
                'price': prices,
                'volume': volumes
            })

            data.set_index('timestamp', inplace=True)

            return data

        except Exception as e:
            self.logger.error(f"Error getting market data: {str(e)}")
            raise

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid"""
        try:
            if cache_key not in self.regime_cache:
                return False

            cache_time = self.regime_cache[cache_key]["analysis_time"]
            age = (datetime.utcnow() - cache_time).total_seconds()

            return age < self.cache_ttl

        except Exception as e:
            self.logger.error(f"Error checking cache validity: {str(e)}")
            return False
