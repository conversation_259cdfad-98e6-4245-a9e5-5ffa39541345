"""
Progressive Web App (PWA) Service

Provides PWA functionality including service worker management,
offline caching, and app manifest generation for mobile web experience.
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import structlog
from fastapi import HTTPException
from fastapi.responses import JSONResponse, Response

from src.features.data_pipeline.cache_manager import CacheManager
from src.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


@dataclass
class PWAConfig:
    """PWA configuration"""
    app_name: str = "TokenTracker"
    app_short_name: str = "TokenTracker"
    app_description: str = "Advanced Trading Automation System"
    theme_color: str = "#3B82F6"
    background_color: str = "#FFFFFF"
    start_url: str = "/"
    display: str = "standalone"
    orientation: str = "portrait"
    cache_version: str = "v1"
    offline_fallback_url: str = "/offline.html"


@dataclass
class ServiceWorkerConfig:
    """Service worker configuration"""
    cache_name: str = "tokentracker-cache-v1"
    cache_urls: List[str] = None
    cache_strategies: Dict[str, str] = None
    background_sync: bool = True
    push_notifications: bool = True


class PWAService:
    """
    Progressive Web App service providing PWA functionality,
    offline support, and mobile web optimizations.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        config: Optional[PWAConfig] = None
    ):
        self.cache_manager = cache_manager
        self.config = config or PWAConfig()
        self.logger = logger.bind(service="pwa")
    
    async def generate_manifest(self) -> Dict[str, Any]:
        """
        Generate PWA manifest.json file.
        
        Returns:
            PWA manifest configuration
        """
        try:
            manifest = {
                "name": self.config.app_name,
                "short_name": self.config.app_short_name,
                "description": self.config.app_description,
                "start_url": self.config.start_url,
                "display": self.config.display,
                "orientation": self.config.orientation,
                "theme_color": self.config.theme_color,
                "background_color": self.config.background_color,
                "scope": "/",
                "lang": "en",
                "dir": "ltr",
                "icons": [
                    {
                        "src": "/static/icons/icon-72x72.png",
                        "sizes": "72x72",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-96x96.png",
                        "sizes": "96x96",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-128x128.png",
                        "sizes": "128x128",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-144x144.png",
                        "sizes": "144x144",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-152x152.png",
                        "sizes": "152x152",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-192x192.png",
                        "sizes": "192x192",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-384x384.png",
                        "sizes": "384x384",
                        "type": "image/png",
                        "purpose": "maskable any"
                    },
                    {
                        "src": "/static/icons/icon-512x512.png",
                        "sizes": "512x512",
                        "type": "image/png",
                        "purpose": "maskable any"
                    }
                ],
                "categories": ["finance", "productivity", "business"],
                "screenshots": [
                    {
                        "src": "/static/screenshots/desktop-1.png",
                        "sizes": "1280x720",
                        "type": "image/png",
                        "form_factor": "wide"
                    },
                    {
                        "src": "/static/screenshots/mobile-1.png",
                        "sizes": "375x812",
                        "type": "image/png",
                        "form_factor": "narrow"
                    }
                ],
                "shortcuts": [
                    {
                        "name": "Dashboard",
                        "short_name": "Dashboard",
                        "description": "View portfolio dashboard",
                        "url": "/dashboard",
                        "icons": [
                            {
                                "src": "/static/icons/dashboard-96x96.png",
                                "sizes": "96x96"
                            }
                        ]
                    },
                    {
                        "name": "Signals",
                        "short_name": "Signals",
                        "description": "View trading signals",
                        "url": "/signals",
                        "icons": [
                            {
                                "src": "/static/icons/signals-96x96.png",
                                "sizes": "96x96"
                            }
                        ]
                    },
                    {
                        "name": "Portfolio",
                        "short_name": "Portfolio",
                        "description": "View portfolio",
                        "url": "/portfolio",
                        "icons": [
                            {
                                "src": "/static/icons/portfolio-96x96.png",
                                "sizes": "96x96"
                            }
                        ]
                    }
                ]
            }
            
            self.logger.info("PWA manifest generated")
            return manifest
            
        except Exception as e:
            self.logger.error("Failed to generate PWA manifest", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate PWA manifest")
    
    async def generate_service_worker(
        self,
        sw_config: Optional[ServiceWorkerConfig] = None
    ) -> str:
        """
        Generate service worker JavaScript code.
        
        Args:
            sw_config: Service worker configuration
            
        Returns:
            Service worker JavaScript code
        """
        try:
            if not sw_config:
                sw_config = ServiceWorkerConfig(
                    cache_urls=[
                        "/",
                        "/dashboard",
                        "/portfolio",
                        "/signals",
                        "/static/css/main.css",
                        "/static/js/main.js",
                        "/static/icons/icon-192x192.png",
                        "/offline.html"
                    ],
                    cache_strategies={
                        "/api/": "network-first",
                        "/static/": "cache-first",
                        "/": "stale-while-revalidate"
                    }
                )
            
            service_worker_code = f"""
// TokenTracker PWA Service Worker
// Generated: {datetime.utcnow().isoformat()}

const CACHE_NAME = '{sw_config.cache_name}';
const OFFLINE_URL = '{self.config.offline_fallback_url}';

// URLs to cache on install
const CACHE_URLS = {json.dumps(sw_config.cache_urls)};

// Cache strategies
const CACHE_STRATEGIES = {json.dumps(sw_config.cache_strategies)};

// Install event - cache essential resources
self.addEventListener('install', event => {{
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {{
                console.log('Caching essential resources');
                return cache.addAll(CACHE_URLS);
            }})
            .then(() => {{
                console.log('Service Worker installed successfully');
                return self.skipWaiting();
            }})
            .catch(error => {{
                console.error('Service Worker installation failed:', error);
            }})
    );
}});

// Activate event - clean up old caches
self.addEventListener('activate', event => {{
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {{
                return Promise.all(
                    cacheNames.map(cacheName => {{
                        if (cacheName !== CACHE_NAME) {{
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }}
                    }})
                );
            }})
            .then(() => {{
                console.log('Service Worker activated');
                return self.clients.claim();
            }})
    );
}});

// Fetch event - handle network requests
self.addEventListener('fetch', event => {{
    const url = new URL(event.request.url);
    
    // Skip non-GET requests
    if (event.request.method !== 'GET') {{
        return;
    }}
    
    // Determine cache strategy
    let strategy = 'network-first'; // default
    
    for (const [pattern, strategyName] of Object.entries(CACHE_STRATEGIES)) {{
        if (url.pathname.startsWith(pattern)) {{
            strategy = strategyName;
            break;
        }}
    }}
    
    // Apply cache strategy
    if (strategy === 'cache-first') {{
        event.respondWith(cacheFirst(event.request));
    }} else if (strategy === 'network-first') {{
        event.respondWith(networkFirst(event.request));
    }} else if (strategy === 'stale-while-revalidate') {{
        event.respondWith(staleWhileRevalidate(event.request));
    }} else {{
        event.respondWith(networkFirst(event.request));
    }}
}});

// Cache-first strategy
async function cacheFirst(request) {{
    try {{
        const cache = await caches.open(CACHE_NAME);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {{
            return cachedResponse;
        }}
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {{
            cache.put(request, networkResponse.clone());
        }}
        
        return networkResponse;
    }} catch (error) {{
        console.error('Cache-first strategy failed:', error);
        return await handleOffline(request);
    }}
}}

// Network-first strategy
async function networkFirst(request) {{
    try {{
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {{
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }}
        
        return networkResponse;
    }} catch (error) {{
        console.log('Network failed, trying cache:', error);
        
        const cache = await caches.open(CACHE_NAME);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {{
            return cachedResponse;
        }}
        
        return await handleOffline(request);
    }}
}}

// Stale-while-revalidate strategy
async function staleWhileRevalidate(request) {{
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    // Fetch from network in background
    const networkResponsePromise = fetch(request)
        .then(networkResponse => {{
            if (networkResponse.ok) {{
                cache.put(request, networkResponse.clone());
            }}
            return networkResponse;
        }})
        .catch(error => {{
            console.log('Background fetch failed:', error);
        }});
    
    // Return cached response immediately if available
    if (cachedResponse) {{
        return cachedResponse;
    }}
    
    // Otherwise wait for network response
    try {{
        return await networkResponsePromise;
    }} catch (error) {{
        return await handleOffline(request);
    }}
}}

// Handle offline scenarios
async function handleOffline(request) {{
    const url = new URL(request.url);
    
    // For navigation requests, return offline page
    if (request.mode === 'navigate') {{
        const cache = await caches.open(CACHE_NAME);
        const offlineResponse = await cache.match(OFFLINE_URL);
        
        if (offlineResponse) {{
            return offlineResponse;
        }}
    }}
    
    // For other requests, return a generic offline response
    return new Response(
        JSON.stringify({{
            error: 'Offline',
            message: 'You are currently offline. Please check your internet connection.'
        }}),
        {{
            status: 503,
            statusText: 'Service Unavailable',
            headers: {{
                'Content-Type': 'application/json'
            }}
        }}
    );
}}

// Background sync for offline actions
{f'''
self.addEventListener('sync', event => {{
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync') {{
        event.waitUntil(doBackgroundSync());
    }}
}});

async function doBackgroundSync() {{
    try {{
        // Sync offline data when connection is restored
        const offlineData = await getOfflineData();
        
        if (offlineData.length > 0) {{
            for (const data of offlineData) {{
                await syncDataToServer(data);
            }}
            
            await clearOfflineData();
            console.log('Background sync completed');
        }}
    }} catch (error) {{
        console.error('Background sync failed:', error);
    }}
}}

async function getOfflineData() {{
    // TODO: Implement offline data retrieval
    return [];
}}

async function syncDataToServer(data) {{
    // TODO: Implement data synchronization
    console.log('Syncing data:', data);
}}

async function clearOfflineData() {{
    // TODO: Implement offline data cleanup
    console.log('Offline data cleared');
}}
''' if sw_config.background_sync else ''}

// Push notification handling
{f'''
self.addEventListener('push', event => {{
    console.log('Push notification received:', event);
    
    const options = {{
        body: 'You have new trading signals!',
        icon: '/static/icons/icon-192x192.png',
        badge: '/static/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {{
            dateOfArrival: Date.now(),
            primaryKey: 1
        }},
        actions: [
            {{
                action: 'explore',
                title: 'View Signals',
                icon: '/static/icons/checkmark.png'
            }},
            {{
                action: 'close',
                title: 'Close',
                icon: '/static/icons/xmark.png'
            }}
        ]
    }};
    
    if (event.data) {{
        const payload = event.data.json();
        options.body = payload.body || options.body;
        options.data = {{ ...options.data, ...payload.data }};
    }}
    
    event.waitUntil(
        self.registration.showNotification('TokenTracker', options)
    );
}});

self.addEventListener('notificationclick', event => {{
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {{
        event.waitUntil(
            clients.openWindow('/signals')
        );
    }} else if (event.action === 'close') {{
        // Just close the notification
    }} else {{
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('/')
        );
    }}
}});
''' if sw_config.push_notifications else ''}

console.log('Service Worker loaded successfully');
"""
            
            self.logger.info("Service worker generated")
            return service_worker_code
            
        except Exception as e:
            self.logger.error("Failed to generate service worker", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate service worker")
    
    async def get_offline_page(self) -> str:
        """
        Generate offline fallback page.
        
        Returns:
            HTML content for offline page
        """
        try:
            offline_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - {self.config.app_name}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: {self.config.background_color};
            color: #333;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }}
        
        .offline-container {{
            max-width: 400px;
            padding: 40px 20px;
        }}
        
        .offline-icon {{
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background-color: {self.config.theme_color};
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
        }}
        
        h1 {{
            color: {self.config.theme_color};
            margin-bottom: 10px;
        }}
        
        p {{
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }}
        
        .retry-button {{
            background-color: {self.config.theme_color};
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }}
        
        .retry-button:hover {{
            background-color: #2563EB;
        }}
        
        .cached-data {{
            margin-top: 40px;
            padding: 20px;
            background-color: #F3F4F6;
            border-radius: 8px;
            text-align: left;
        }}
        
        .cached-data h3 {{
            margin-top: 0;
            color: {self.config.theme_color};
        }}
        
        .cached-data ul {{
            margin: 0;
            padding-left: 20px;
        }}
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>You're Offline</h1>
        <p>
            It looks like you're not connected to the internet. 
            Don't worry, you can still view some cached data while offline.
        </p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="cached-data">
            <h3>Available Offline:</h3>
            <ul>
                <li>Portfolio summary</li>
                <li>Recent signals</li>
                <li>Performance metrics</li>
                <li>Trade history</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Check for connection and auto-reload
        window.addEventListener('online', function() {{
            console.log('Connection restored');
            window.location.reload();
        }});
        
        // Show connection status
        if (navigator.onLine) {{
            console.log('Online');
        }} else {{
            console.log('Offline');
        }}
    </script>
</body>
</html>
"""
            
            self.logger.info("Offline page generated")
            return offline_html
            
        except Exception as e:
            self.logger.error("Failed to generate offline page", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate offline page")
    
    async def get_pwa_installation_prompt(self) -> Dict[str, Any]:
        """
        Get PWA installation prompt data.
        
        Returns:
            Installation prompt configuration
        """
        try:
            prompt_data = {
                "canInstall": True,
                "installPrompt": {
                    "title": f"Install {self.config.app_name}",
                    "message": "Get quick access to your trading dashboard with our mobile app experience.",
                    "benefits": [
                        "Offline access to portfolio data",
                        "Push notifications for signals",
                        "Faster loading times",
                        "Native app-like experience"
                    ],
                    "installButton": "Install App",
                    "cancelButton": "Maybe Later"
                },
                "installInstructions": {
                    "ios": [
                        "Tap the Share button in Safari",
                        "Scroll down and tap 'Add to Home Screen'",
                        "Tap 'Add' to install the app"
                    ],
                    "android": [
                        "Tap the menu button (three dots)",
                        "Select 'Add to Home screen'",
                        "Tap 'Add' to install the app"
                    ],
                    "desktop": [
                        "Click the install button in the address bar",
                        "Or use the browser menu to install the app"
                    ]
                }
            }
            
            return prompt_data
            
        except Exception as e:
            self.logger.error("Failed to get installation prompt", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get installation prompt")
    
    async def track_pwa_usage(self, user_id: str, event: str, data: Dict[str, Any]) -> None:
        """
        Track PWA usage analytics.
        
        Args:
            user_id: User identifier
            event: Event name
            data: Event data
        """
        try:
            analytics_data = {
                "user_id": user_id,
                "event": event,
                "data": data,
                "timestamp": datetime.utcnow(),
                "user_agent": data.get("user_agent"),
                "platform": data.get("platform")
            }
            
            # Store analytics data
            cache_key = f"pwa_analytics:{user_id}:{datetime.utcnow().strftime('%Y%m%d')}"
            existing_data = await self.cache_manager.get(cache_key) or []
            existing_data.append(analytics_data)
            
            await self.cache_manager.set(cache_key, existing_data, ttl=86400 * 7)  # 7 days
            
            self.logger.info("PWA usage tracked", user_id=user_id, event=event)
            
        except Exception as e:
            self.logger.error("Failed to track PWA usage", user_id=user_id, event=event, error=str(e))
