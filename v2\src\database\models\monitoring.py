"""
📊 Monitoring Database Models

Database models for monitoring data including metrics, alerts, health checks,
and performance logs following DATABASE_PATTERNS.md.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from beanie import Document
from pydantic import Field, validator
from pymongo import IndexModel, ASCENDING, DESCENDING, TEXT

from .base import BaseDocument


class MetricRecord(BaseDocument):
    """
    📊 Metric record model for storing time-series metrics
    """
    
    metric_name: str = Field(..., description="Name of the metric")
    metric_type: str = Field(..., description="Type of metric (gauge, counter, histogram)")
    value: float = Field(..., description="Metric value")
    unit: str = Field(..., description="Unit of measurement")
    labels: Dict[str, str] = Field(default_factory=dict, description="Metric labels/tags")
    source: str = Field(..., description="Source system/module")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Metric timestamp")
    
    class Settings:
        name = "metric_records"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            # Time-series indexes
            IndexModel([("timestamp", DESCENDING)], name="timestamp_desc"),
            IndexModel([("metric_name", ASCENDING), ("timestamp", DESCENDING)], name="metric_time"),
            IndexModel([("source", ASCENDING), ("timestamp", DESCENDING)], name="source_time"),
            
            # Query optimization indexes
            IndexModel([("metric_name", ASCENDING)], name="metric_name_asc"),
            IndexModel([("metric_type", ASCENDING)], name="metric_type_asc"),
            IndexModel([("source", ASCENDING)], name="source_asc"),
            
            # Compound indexes for common queries
            IndexModel([
                ("metric_name", ASCENDING),
                ("source", ASCENDING),
                ("timestamp", DESCENDING)
            ], name="metric_source_time"),
            
            # TTL index for automatic cleanup (30 days)
            IndexModel([("timestamp", ASCENDING)], name="ttl_index", expireAfterSeconds=2592000)
        ]
    
    @validator("value")
    def validate_value(cls, v):
        """Validate metric value is finite"""
        if not isinstance(v, (int, float)) or not (-float('inf') < v < float('inf')):
            raise ValueError("Metric value must be a finite number")
        return float(v)


class AlertRule(BaseDocument):
    """
    🚨 Alert rule configuration model
    """
    
    name: str = Field(..., description="Alert rule name", unique=True)
    description: str = Field(..., description="Alert rule description")
    condition: str = Field(..., description="Alert condition expression")
    threshold: float = Field(..., description="Alert threshold value")
    severity: str = Field(..., description="Alert severity level")
    enabled: bool = Field(default=True, description="Whether rule is enabled")
    cooldown_minutes: int = Field(default=15, description="Cooldown period in minutes")
    escalation_minutes: int = Field(default=60, description="Escalation time in minutes")
    tags: Dict[str, str] = Field(default_factory=dict, description="Rule tags")
    notification_channels: List[str] = Field(default_factory=list, description="Notification channels")
    
    class Settings:
        name = "alert_rules"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            IndexModel([("name", ASCENDING)], name="name_asc", unique=True),
            IndexModel([("enabled", ASCENDING)], name="enabled_asc"),
            IndexModel([("severity", ASCENDING)], name="severity_asc"),
            IndexModel([("created_at", DESCENDING)], name="created_desc"),
        ]
    
    @validator("severity")
    def validate_severity(cls, v):
        """Validate severity level"""
        valid_severities = ["critical", "high", "medium", "low", "info"]
        if v.lower() not in valid_severities:
            raise ValueError(f"Severity must be one of: {valid_severities}")
        return v.lower()
    
    @validator("cooldown_minutes", "escalation_minutes")
    def validate_positive_minutes(cls, v):
        """Validate positive minute values"""
        if v < 0:
            raise ValueError("Minutes must be positive")
        return v


class AlertInstance(BaseDocument):
    """
    🚨 Alert instance model for active and historical alerts
    """
    
    rule_id: str = Field(..., description="Alert rule ID")
    rule_name: str = Field(..., description="Alert rule name")
    severity: str = Field(..., description="Alert severity")
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    value: float = Field(..., description="Metric value that triggered alert")
    threshold: float = Field(..., description="Alert threshold")
    status: str = Field(default="active", description="Alert status")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment timestamp")
    acknowledged_by: Optional[str] = Field(None, description="User who acknowledged")
    tags: Dict[str, str] = Field(default_factory=dict, description="Alert tags")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    class Settings:
        name = "alert_instances"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            IndexModel([("status", ASCENDING)], name="status_asc"),
            IndexModel([("severity", ASCENDING)], name="severity_asc"),
            IndexModel([("rule_name", ASCENDING)], name="rule_name_asc"),
            IndexModel([("created_at", DESCENDING)], name="created_desc"),
            IndexModel([("resolved_at", DESCENDING)], name="resolved_desc"),
            
            # Compound indexes for common queries
            IndexModel([
                ("status", ASCENDING),
                ("created_at", DESCENDING)
            ], name="status_created"),
            IndexModel([
                ("rule_name", ASCENDING),
                ("status", ASCENDING),
                ("created_at", DESCENDING)
            ], name="rule_status_created"),
            
            # TTL index for resolved alerts (90 days)
            IndexModel([("resolved_at", ASCENDING)], name="resolved_ttl", expireAfterSeconds=7776000)
        ]
    
    @validator("status")
    def validate_status(cls, v):
        """Validate alert status"""
        valid_statuses = ["active", "resolved", "suppressed", "acknowledged"]
        if v.lower() not in valid_statuses:
            raise ValueError(f"Status must be one of: {valid_statuses}")
        return v.lower()


class HealthCheckRecord(BaseDocument):
    """
    🏥 Health check record model
    """
    
    service_name: str = Field(..., description="Service name")
    check_type: str = Field(..., description="Type of health check")
    status: str = Field(..., description="Health check status")
    response_time_ms: float = Field(..., description="Response time in milliseconds")
    details: Dict[str, Any] = Field(default_factory=dict, description="Check details")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    endpoint: Optional[str] = Field(None, description="Endpoint checked")
    
    class Settings:
        name = "health_check_records"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            IndexModel([("service_name", ASCENDING)], name="service_name_asc"),
            IndexModel([("status", ASCENDING)], name="status_asc"),
            IndexModel([("created_at", DESCENDING)], name="created_desc"),
            
            # Compound indexes
            IndexModel([
                ("service_name", ASCENDING),
                ("created_at", DESCENDING)
            ], name="service_created"),
            IndexModel([
                ("service_name", ASCENDING),
                ("status", ASCENDING),
                ("created_at", DESCENDING)
            ], name="service_status_created"),
            
            # TTL index (7 days)
            IndexModel([("created_at", ASCENDING)], name="health_ttl", expireAfterSeconds=604800)
        ]
    
    @validator("status")
    def validate_status(cls, v):
        """Validate health check status"""
        valid_statuses = ["healthy", "unhealthy", "degraded", "unknown"]
        if v.lower() not in valid_statuses:
            raise ValueError(f"Status must be one of: {valid_statuses}")
        return v.lower()
    
    @validator("response_time_ms")
    def validate_response_time(cls, v):
        """Validate response time is positive"""
        if v < 0:
            raise ValueError("Response time must be positive")
        return v


class PerformanceLog(BaseDocument):
    """
    ⚡ Performance log model for request and operation profiling
    """
    
    operation_type: str = Field(..., description="Type of operation (request, database, etc.)")
    operation_name: str = Field(..., description="Name of the operation")
    duration_ms: float = Field(..., description="Operation duration in milliseconds")
    memory_usage_mb: Optional[float] = Field(None, description="Memory usage in MB")
    cpu_usage_percent: Optional[float] = Field(None, description="CPU usage percentage")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional performance data")
    endpoint: Optional[str] = Field(None, description="API endpoint if applicable")
    method: Optional[str] = Field(None, description="HTTP method if applicable")
    status_code: Optional[int] = Field(None, description="Response status code")
    user_id: Optional[str] = Field(None, description="User ID if applicable")
    
    class Settings:
        name = "performance_logs"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            IndexModel([("operation_type", ASCENDING)], name="operation_type_asc"),
            IndexModel([("operation_name", ASCENDING)], name="operation_name_asc"),
            IndexModel([("created_at", DESCENDING)], name="created_desc"),
            IndexModel([("duration_ms", DESCENDING)], name="duration_desc"),
            
            # Compound indexes for performance analysis
            IndexModel([
                ("operation_type", ASCENDING),
                ("created_at", DESCENDING)
            ], name="type_created"),
            IndexModel([
                ("operation_name", ASCENDING),
                ("created_at", DESCENDING)
            ], name="name_created"),
            IndexModel([
                ("endpoint", ASCENDING),
                ("method", ASCENDING),
                ("created_at", DESCENDING)
            ], name="endpoint_method_created"),
            
            # TTL index (7 days)
            IndexModel([("created_at", ASCENDING)], name="performance_ttl", expireAfterSeconds=604800)
        ]
    
    @validator("duration_ms")
    def validate_duration(cls, v):
        """Validate duration is positive"""
        if v < 0:
            raise ValueError("Duration must be positive")
        return v
    
    @validator("memory_usage_mb")
    def validate_memory_usage(cls, v):
        """Validate memory usage is positive"""
        if v is not None and v < 0:
            raise ValueError("Memory usage must be positive")
        return v
    
    @validator("cpu_usage_percent")
    def validate_cpu_usage(cls, v):
        """Validate CPU usage percentage"""
        if v is not None and (v < 0 or v > 100):
            raise ValueError("CPU usage must be between 0 and 100")
        return v


class SystemMetrics(BaseDocument):
    """
    🖥️ System metrics model for resource monitoring
    """
    
    hostname: str = Field(..., description="System hostname")
    cpu_usage_percent: float = Field(..., description="CPU usage percentage")
    memory_usage_percent: float = Field(..., description="Memory usage percentage")
    memory_used_bytes: int = Field(..., description="Memory used in bytes")
    memory_total_bytes: int = Field(..., description="Total memory in bytes")
    disk_usage_percent: float = Field(..., description="Disk usage percentage")
    disk_used_bytes: int = Field(..., description="Disk used in bytes")
    disk_total_bytes: int = Field(..., description="Total disk in bytes")
    network_bytes_sent: int = Field(default=0, description="Network bytes sent")
    network_bytes_received: int = Field(default=0, description="Network bytes received")
    active_connections: int = Field(default=0, description="Active network connections")
    load_average: Optional[List[float]] = Field(None, description="System load average")
    
    class Settings:
        name = "system_metrics"
        use_state_management = True
        validate_on_save = True
        
        indexes = [
            IndexModel([("hostname", ASCENDING)], name="hostname_asc"),
            IndexModel([("created_at", DESCENDING)], name="created_desc"),
            
            # Compound indexes
            IndexModel([
                ("hostname", ASCENDING),
                ("created_at", DESCENDING)
            ], name="hostname_created"),
            
            # TTL index (30 days)
            IndexModel([("created_at", ASCENDING)], name="system_ttl", expireAfterSeconds=2592000)
        ]
    
    @validator("cpu_usage_percent", "memory_usage_percent", "disk_usage_percent")
    def validate_percentage(cls, v):
        """Validate percentage values"""
        if v < 0 or v > 100:
            raise ValueError("Percentage must be between 0 and 100")
        return v
