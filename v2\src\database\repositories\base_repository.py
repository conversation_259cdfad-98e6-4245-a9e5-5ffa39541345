"""
🗄️ Base Repository

Base repository class with common CRUD operations.
"""

from typing import Any, Dict, List, Optional, Type, TypeVar
from beanie import Document

from ...config.logging_config import get_logger

logger = get_logger(__name__)

T = TypeVar('T', bound=Document)


class BaseRepository:
    """
    Base repository with common CRUD operations
    """
    
    def __init__(self, model: Type[T]):
        self.model = model
        self.logger = logger
    
    async def create(self, data: Dict[str, Any]) -> T:
        """Create a new document"""
        try:
            document = self.model(**data)
            await document.save()
            return document
        except Exception as e:
            self.logger.error(f"Error creating {self.model.__name__}: {str(e)}")
            raise
    
    async def get_by_id(self, doc_id: str) -> Optional[T]:
        """Get document by ID"""
        try:
            return await self.model.get(doc_id)
        except Exception as e:
            self.logger.error(f"Error getting {self.model.__name__} by ID: {str(e)}")
            return None
    
    async def find_one(self, filters: Dict[str, Any]) -> Optional[T]:
        """Find one document by filters"""
        try:
            return await self.model.find_one(filters)
        except Exception as e:
            self.logger.error(f"Error finding {self.model.__name__}: {str(e)}")
            return None
    
    async def find_many(
        self,
        filters: Dict[str, Any] = None,
        limit: int = 100,
        skip: int = 0
    ) -> List[T]:
        """Find multiple documents"""
        try:
            query = self.model.find(filters or {})
            return await query.skip(skip).limit(limit).to_list()
        except Exception as e:
            self.logger.error(f"Error finding {self.model.__name__} documents: {str(e)}")
            return []
    
    async def update(self, doc_id: str, data: Dict[str, Any]) -> Optional[T]:
        """Update document by ID"""
        try:
            document = await self.model.get(doc_id)
            if not document:
                return None
            
            for key, value in data.items():
                setattr(document, key, value)
            
            await document.save()
            return document
        except Exception as e:
            self.logger.error(f"Error updating {self.model.__name__}: {str(e)}")
            return None
    
    async def delete(self, doc_id: str) -> bool:
        """Delete document by ID"""
        try:
            document = await self.model.get(doc_id)
            if not document:
                return False
            
            await document.delete()
            return True
        except Exception as e:
            self.logger.error(f"Error deleting {self.model.__name__}: {str(e)}")
            return False
    
    async def count(self, filters: Dict[str, Any] = None) -> int:
        """Count documents"""
        try:
            return await self.model.find(filters or {}).count()
        except Exception as e:
            self.logger.error(f"Error counting {self.model.__name__} documents: {str(e)}")
            return 0
