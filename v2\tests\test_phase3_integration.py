"""
🧪 Phase 3 Integration Tests - Production Optimization

Comprehensive integration tests for all Phase 3 features including:
- Advanced database optimization
- Multi-level caching
- API optimization (batching, load balancing)
- CI/CD pipeline components
"""

import pytest
import asyncio
import time
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from src.config.database import DatabaseConfig
from src.features.data_pipeline.advanced_cache_manager import AdvancedCacheManager, CacheLevel, CacheStrategy
from src.features.monitoring.cache_monitor import CacheMonitor
from src.features.api_optimization.request_batcher import RequestBatcher, BatchStrategy
from src.features.api_optimization.load_balancer import LoadBalancer, LoadBalancingStrategy, BackendServer
from src.features.api_optimization.optimization_middleware import APIOptimizationMiddleware


class TestDatabaseOptimization:
    """Test advanced database optimization features"""
    
    @pytest.fixture
    async def db_config(self):
        """Database configuration fixture"""
        config = DatabaseConfig()
        yield config
        await config.disconnect()
    
    @pytest.mark.asyncio
    async def test_read_replica_setup(self, db_config):
        """Test read replica configuration"""
        # Mock read replica URI
        with patch.object(db_config.settings, 'mongodb_read_replica_uri', 'mongodb://read-replica:27017/test'):
            await db_config._setup_read_replica()
            
            assert db_config.read_replica_enabled is True
            assert db_config.read_client is not None
            assert db_config.read_database is not None
    
    @pytest.mark.asyncio
    async def test_query_performance_tracking(self, db_config):
        """Test query performance monitoring"""
        # Track some queries
        await db_config.track_query_performance("read", 0.05, False)
        await db_config.track_query_performance("write", 0.15, True)
        await db_config.track_query_performance("read", 0.03, False)
        
        stats = db_config.get_query_stats()
        
        assert stats["total_queries"] == 3
        assert stats["read_queries"] == 2
        assert stats["write_queries"] == 1
        assert stats["slow_queries"] == 1
        assert stats["avg_query_time"] > 0
    
    @pytest.mark.asyncio
    async def test_index_optimization(self, db_config):
        """Test index optimization analysis"""
        # Mock database connection
        with patch.object(db_config, 'database') as mock_db:
            mock_db.list_collection_names.return_value = ["tokens", "signals"]
            
            # Mock collection with index stats
            mock_collection = Mock()
            mock_collection.aggregate.return_value.to_list = AsyncMock(return_value=[
                {"name": "test_index", "accesses": {"ops": 0}, "spec": {"field": 1}},
                {"name": "_id_", "accesses": {"ops": 100}, "spec": {"_id": 1}}
            ])
            mock_db.__getitem__.return_value = mock_collection
            
            results = await db_config.optimize_indexes()
            
            assert "analyzed_collections" in results
            assert "unused_indexes" in results
            assert len(results["unused_indexes"]) == 1  # test_index is unused


class TestAdvancedCaching:
    """Test multi-level caching system"""
    
    @pytest.fixture
    async def cache_manager(self):
        """Advanced cache manager fixture"""
        manager = AdvancedCacheManager("test")
        async with manager:
            yield manager
    
    @pytest.mark.asyncio
    async def test_multi_level_cache_get(self, cache_manager):
        """Test multi-level cache retrieval"""
        # Set data in memory cache
        await cache_manager._set_in_memory("test_key", {"data": "test"})
        
        # Get from cache (should hit memory)
        result = await cache_manager.get("test_key")
        
        assert result == {"data": "test"}
        assert cache_manager.stats[CacheLevel.MEMORY].hits == 1
    
    @pytest.mark.asyncio
    async def test_cache_promotion(self, cache_manager):
        """Test cache promotion from Redis to memory"""
        # Mock Redis cache hit
        with patch.object(cache_manager.redis_cache, 'get', return_value={"data": "from_redis"}):
            result = await cache_manager.get("test_key")
            
            assert result == {"data": "from_redis"}
            # Should be promoted to memory cache
            memory_result = await cache_manager._get_from_memory("test_key")
            assert memory_result == {"data": "from_redis"}
    
    @pytest.mark.asyncio
    async def test_cache_invalidation_by_tags(self, cache_manager):
        """Test cache invalidation by tags"""
        # Set data with tags
        await cache_manager.set("key1", "data1", tags=["tag1", "tag2"])
        await cache_manager.set("key2", "data2", tags=["tag2", "tag3"])
        await cache_manager.set("key3", "data3", tags=["tag3"])
        
        # Invalidate by tag
        invalidated = await cache_manager.invalidate_by_tags(["tag2"])
        
        assert invalidated == 2  # key1 and key2 should be invalidated
    
    @pytest.mark.asyncio
    async def test_cache_warming(self, cache_manager):
        """Test cache warming functionality"""
        warming_config = {
            "warm_tokens": True,
            "warm_signals": False,
            "warm_portfolios": False,
            "token_addresses": ["0x123", "0x456"]
        }
        
        # Mock data aggregator
        with patch('src.features.data_pipeline.advanced_cache_manager.DataAggregator') as mock_aggregator:
            mock_instance = mock_aggregator.return_value
            mock_instance.get_aggregated_data = AsyncMock(return_value={"price": 1.0})
            
            results = await cache_manager.warm_cache(warming_config)
            
            assert "entries_warmed" in results
            assert results["entries_warmed"] >= 0


class TestCacheMonitoring:
    """Test cache monitoring and analytics"""
    
    @pytest.fixture
    def cache_monitor(self):
        """Cache monitor fixture"""
        return CacheMonitor()
    
    @pytest.mark.asyncio
    async def test_cache_health_assessment(self, cache_monitor):
        """Test cache health assessment"""
        # Mock cache metrics
        mock_metrics = {
            "cache_levels": {
                "memory": {"hits": 80, "misses": 20, "avg_response_time_ms": 1.0},
                "redis": {"hits": 60, "misses": 40, "avg_response_time_ms": 5.0}
            },
            "derived_metrics": {"overall_hit_ratio": 70.0},
            "memory_cache": {"memory_utilization_percent": 60.0},
            "performance": {"avg_access_time_ms": 3.0}
        }
        
        health_assessment = await cache_monitor.assess_cache_health(mock_metrics)
        
        assert "overall_status" in health_assessment
        assert "health_metrics" in health_assessment
        assert "recommendations" in health_assessment
    
    @pytest.mark.asyncio
    async def test_performance_trends(self, cache_monitor):
        """Test performance trend analysis"""
        # Add some historical data
        for i in range(10):
            cache_monitor.historical_metrics.append({
                "timestamp": datetime.utcnow().isoformat(),
                "hit_ratio": 70 + i,  # Increasing trend
                "avg_access_time": 10 - i,  # Decreasing trend
                "memory_utilization": 50 + i,
                "total_queries": 100 + i * 10
            })
        
        trends = await cache_monitor.get_performance_trends(hours=1)
        
        assert "hit_ratio_trend" in trends
        assert trends["hit_ratio_trend"]["trend"] == "increasing"


class TestAPIOptimization:
    """Test API optimization features"""
    
    @pytest.fixture
    async def request_batcher(self):
        """Request batcher fixture"""
        batcher = RequestBatcher()
        await batcher.start()
        yield batcher
        await batcher.stop()
    
    @pytest.fixture
    async def load_balancer(self):
        """Load balancer fixture"""
        lb = LoadBalancer(LoadBalancingStrategy.ROUND_ROBIN)
        lb.add_backend("backend1", "localhost", 8001)
        lb.add_backend("backend2", "localhost", 8002)
        await lb.start()
        yield lb
        await lb.stop()
    
    @pytest.mark.asyncio
    async def test_request_batching(self, request_batcher):
        """Test request batching functionality"""
        results = []
        
        async def callback(result, error=None):
            results.append(result)
        
        # Add requests to batch
        await request_batcher.add_request(
            endpoint="/api/tokens/price",
            params={"token_address": "0x123"},
            callback=callback,
            priority=2
        )
        
        await request_batcher.add_request(
            endpoint="/api/tokens/price", 
            params={"token_address": "0x456"},
            callback=callback,
            priority=1
        )
        
        # Wait for batch processing
        await asyncio.sleep(0.2)
        
        stats = await request_batcher.get_batch_stats()
        assert stats["total_requests"] == 2
    
    @pytest.mark.asyncio
    async def test_load_balancing(self, load_balancer):
        """Test load balancing functionality"""
        # Route some requests
        backend1 = await load_balancer.route_request({"path": "/api/test"})
        backend2 = await load_balancer.route_request({"path": "/api/test"})
        
        assert backend1 is not None
        assert backend2 is not None
        
        # With round-robin, should get different backends
        assert backend1.id != backend2.id
        
        # Complete requests
        await load_balancer.complete_request(backend1, 0.1, True)
        await load_balancer.complete_request(backend2, 0.2, True)
        
        stats = await load_balancer.get_load_balancer_stats()
        assert stats["global_stats"]["total_requests"] == 2
        assert stats["global_stats"]["successful_requests"] == 2
    
    @pytest.mark.asyncio
    async def test_backend_health_monitoring(self, load_balancer):
        """Test backend health monitoring"""
        backend = load_balancer.backends["backend1"]
        
        # Simulate some failed requests
        backend.update_metrics(0.1, False)  # Failed request
        backend.update_metrics(0.1, False)  # Failed request
        backend.update_metrics(0.1, True)   # Successful request
        
        # Check health score calculation
        assert backend.health_score < 100.0  # Should be penalized for failures
        assert backend.error_rate > 0  # Should have error rate
    
    @pytest.mark.asyncio
    async def test_optimization_middleware_integration(self):
        """Test optimization middleware integration"""
        middleware = APIOptimizationMiddleware(
            app=None,
            enable_batching=True,
            enable_load_balancing=True
        )
        
        await middleware.startup()
        
        # Test middleware components
        assert middleware.request_batcher is not None
        assert middleware.load_balancer is not None
        
        # Get optimization stats
        stats = await middleware.get_optimization_stats()
        assert "middleware_stats" in stats
        assert "batcher_stats" in stats
        assert "load_balancer_stats" in stats
        
        await middleware.shutdown()


class TestProductionReadiness:
    """Test production readiness features"""
    
    @pytest.mark.asyncio
    async def test_health_endpoints_integration(self):
        """Test that all health endpoints work together"""
        # This would test the actual FastAPI app health endpoints
        # For now, we'll test the components individually
        
        # Database health
        db_config = DatabaseConfig()
        assert hasattr(db_config, 'get_query_stats')
        
        # Cache health
        cache_manager = AdvancedCacheManager("test")
        async with cache_manager:
            analytics = await cache_manager.get_cache_analytics()
            assert "cache_levels" in analytics
        
        # API optimization health
        middleware = APIOptimizationMiddleware(app=None)
        await middleware.startup()
        stats = await middleware.get_optimization_stats()
        assert "middleware_stats" in stats
        await middleware.shutdown()
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """Test system performance under simulated load"""
        cache_manager = AdvancedCacheManager("test")
        
        async with cache_manager:
            # Simulate concurrent cache operations
            tasks = []
            for i in range(100):
                task = asyncio.create_task(
                    cache_manager.set(f"key_{i}", f"value_{i}", ttl=60)
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            # All operations should succeed
            assert all(results)
            
            # Get analytics
            analytics = await cache_manager.get_cache_analytics()
            assert analytics["memory_cache"]["entries_count"] > 0
    
    def test_configuration_validation(self):
        """Test that all configurations are valid"""
        # Test database configuration
        db_config = DatabaseConfig()
        assert hasattr(db_config, 'settings')
        assert hasattr(db_config, 'query_stats')
        
        # Test cache configuration
        cache_manager = AdvancedCacheManager("test")
        assert cache_manager.memory_cache_max_size > 0
        assert cache_manager.memory_cache_max_bytes > 0
        
        # Test load balancer configuration
        lb = LoadBalancer()
        assert hasattr(lb, 'health_thresholds')
        assert hasattr(lb, 'circuit_breaker_threshold')


@pytest.mark.integration
class TestEndToEndWorkflow:
    """End-to-end integration tests"""
    
    @pytest.mark.asyncio
    async def test_complete_request_flow(self):
        """Test complete request flow through all optimization layers"""
        # Initialize all components
        cache_manager = AdvancedCacheManager("test")
        request_batcher = RequestBatcher()
        load_balancer = LoadBalancer()
        
        # Add backend
        load_balancer.add_backend("test_backend", "localhost", 8000)
        
        try:
            # Start components
            async with cache_manager:
                await request_batcher.start()
                await load_balancer.start()
                
                # Simulate request flow
                # 1. Cache miss
                cached_data = await cache_manager.get("test_request")
                assert cached_data is None
                
                # 2. Route request through load balancer
                backend = await load_balancer.route_request({"path": "/api/test"})
                assert backend is not None
                
                # 3. Process request (simulated)
                start_time = time.time()
                await asyncio.sleep(0.01)  # Simulate processing
                response_time = time.time() - start_time
                
                # 4. Complete request
                await load_balancer.complete_request(backend, response_time, True)
                
                # 5. Cache result
                result_data = {"result": "success", "timestamp": datetime.utcnow().isoformat()}
                await cache_manager.set("test_request", result_data, ttl=300)
                
                # 6. Verify cached data
                cached_result = await cache_manager.get("test_request")
                assert cached_result == result_data
                
                # 7. Get performance metrics
                cache_analytics = await cache_manager.get_cache_analytics()
                lb_stats = await load_balancer.get_load_balancer_stats()
                
                assert cache_analytics["cache_levels"]["memory"]["sets"] > 0
                assert lb_stats["global_stats"]["total_requests"] > 0
                
        finally:
            # Cleanup
            await request_batcher.stop()
            await load_balancer.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
