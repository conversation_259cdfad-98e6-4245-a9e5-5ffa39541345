"""
SMS Service

Provides SMS notification capabilities using Twilio API.
Supports international SMS, delivery tracking, and rate limiting.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import re

import structlog
from fastapi import HTT<PERSON>Ex<PERSON>
import httpx

from src.shared.types import NotificationPriority
from src.features.data_pipeline.cache_manager import CacheManager
from src.config.settings import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()


class SMSStatus(Enum):
    """SMS delivery status"""
    QUEUED = "queued"
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    UNDELIVERED = "undelivered"


@dataclass
class SMSConfig:
    """SMS service configuration"""
    twilio_account_sid: str
    twilio_auth_token: str
    twilio_phone_number: str
    max_message_length: int = 160
    rate_limit_per_hour: int = 100
    rate_limit_per_day: int = 1000
    delivery_tracking: bool = True
    international_enabled: bool = True


@dataclass
class SMSMessage:
    """SMS message data"""
    to_phone: str
    message: str
    priority: NotificationPriority = NotificationPriority.MEDIUM
    scheduled_time: Optional[datetime] = None
    callback_url: Optional[str] = None
    max_price: Optional[float] = None


@dataclass
class SMSDeliveryReport:
    """SMS delivery report"""
    message_id: str
    to_phone: str
    status: SMSStatus
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    error_message: Optional[str] = None
    price: Optional[float] = None
    segments: int = 1


class SMSService:
    """
    Comprehensive SMS service using Twilio API.
    Handles message sending, delivery tracking, and rate limiting.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        config: Optional[SMSConfig] = None
    ):
        self.cache_manager = cache_manager
        self.config = config or self._load_config()
        self.logger = logger.bind(service="sms")
        self.http_client = httpx.AsyncClient(
            auth=(self.config.twilio_account_sid, self.config.twilio_auth_token),
            timeout=30.0
        )
        self.base_url = f"https://api.twilio.com/2010-04-01/Accounts/{self.config.twilio_account_sid}"
    
    def _load_config(self) -> SMSConfig:
        """Load SMS configuration from settings"""
        return SMSConfig(
            twilio_account_sid=settings.twilio_account_sid,
            twilio_auth_token=settings.twilio_auth_token,
            twilio_phone_number=settings.twilio_phone_number
        )
    
    async def send_sms(
        self,
        user_id: str,
        phone_number: str,
        message: str,
        priority: NotificationPriority = NotificationPriority.MEDIUM
    ) -> Dict[str, Any]:
        """
        Send SMS message to a phone number.
        
        Args:
            user_id: User identifier
            phone_number: Recipient phone number
            message: SMS message content
            priority: Message priority
            
        Returns:
            SMS send result with message ID and status
        """
        try:
            # Validate phone number
            if not self._validate_phone_number(phone_number):
                raise HTTPException(status_code=400, detail="Invalid phone number format")
            
            # Check rate limits
            if not await self._check_rate_limits(user_id):
                raise HTTPException(status_code=429, detail="SMS rate limit exceeded")
            
            # Validate message length
            if len(message) > self.config.max_message_length:
                message = message[:self.config.max_message_length - 3] + "..."
            
            # Create SMS message
            sms_message = SMSMessage(
                to_phone=phone_number,
                message=message,
                priority=priority
            )
            
            # Send SMS via Twilio
            result = await self._send_via_twilio(sms_message)
            
            # Update rate limit counters
            await self._update_rate_limits(user_id)
            
            # Store delivery tracking info
            if self.config.delivery_tracking:
                await self._store_delivery_info(user_id, result)
            
            self.logger.info(
                "SMS sent successfully",
                user_id=user_id,
                phone=phone_number[-4:],  # Log only last 4 digits for privacy
                message_id=result.get("message_id")
            )
            
            return result
            
        except Exception as e:
            self.logger.error(
                "Failed to send SMS",
                user_id=user_id,
                phone=phone_number[-4:] if phone_number else "unknown",
                error=str(e)
            )
            raise
    
    async def send_bulk_sms(
        self,
        messages: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Send bulk SMS messages efficiently.
        
        Args:
            messages: List of SMS message data
            
        Returns:
            Bulk send results and statistics
        """
        try:
            total_messages = len(messages)
            successful_sends = 0
            failed_sends = 0
            results = []
            
            # Process messages concurrently with rate limiting
            semaphore = asyncio.Semaphore(10)  # Limit concurrent requests
            
            async def send_single_message(msg_data):
                async with semaphore:
                    try:
                        result = await self.send_sms(
                            user_id=msg_data["user_id"],
                            phone_number=msg_data["phone_number"],
                            message=msg_data["message"],
                            priority=msg_data.get("priority", NotificationPriority.MEDIUM)
                        )
                        return {"success": True, "result": result}
                    except Exception as e:
                        return {"success": False, "error": str(e)}
            
            # Send all messages
            tasks = [send_single_message(msg) for msg in messages]
            send_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in send_results:
                if isinstance(result, dict):
                    if result["success"]:
                        successful_sends += 1
                    else:
                        failed_sends += 1
                    results.append(result)
                else:
                    failed_sends += 1
                    results.append({"success": False, "error": str(result)})
            
            bulk_result = {
                "total_messages": total_messages,
                "successful_sends": successful_sends,
                "failed_sends": failed_sends,
                "success_rate": (successful_sends / total_messages * 100) if total_messages > 0 else 0,
                "results": results
            }
            
            self.logger.info("Bulk SMS sent", **bulk_result)
            return bulk_result
            
        except Exception as e:
            self.logger.error("Failed to send bulk SMS", error=str(e))
            raise HTTPException(status_code=500, detail="Failed to send bulk SMS")
    
    async def get_delivery_status(
        self,
        user_id: str,
        message_id: str
    ) -> SMSDeliveryReport:
        """
        Get SMS delivery status.
        
        Args:
            user_id: User identifier
            message_id: SMS message ID
            
        Returns:
            SMS delivery report
        """
        try:
            # Get from cache first
            cache_key = f"sms_delivery:{user_id}:{message_id}"
            cached_report = await self.cache_manager.get(cache_key)
            
            if cached_report:
                return SMSDeliveryReport(**cached_report)
            
            # Query Twilio API for status
            url = f"{self.base_url}/Messages/{message_id}.json"
            response = await self.http_client.get(url)
            
            if response.status_code == 200:
                data = response.json()
                
                report = SMSDeliveryReport(
                    message_id=message_id,
                    to_phone=data.get("to"),
                    status=SMSStatus(data.get("status", "unknown")),
                    sent_at=self._parse_twilio_date(data.get("date_sent")),
                    delivered_at=self._parse_twilio_date(data.get("date_updated")),
                    error_message=data.get("error_message"),
                    price=float(data.get("price", 0)) if data.get("price") else None,
                    segments=int(data.get("num_segments", 1))
                )
                
                # Cache the report
                await self.cache_manager.set(
                    cache_key,
                    report.__dict__,
                    ttl=3600  # Cache for 1 hour
                )
                
                return report
            else:
                raise HTTPException(status_code=404, detail="Message not found")
                
        except Exception as e:
            self.logger.error(
                "Failed to get delivery status",
                user_id=user_id,
                message_id=message_id,
                error=str(e)
            )
            raise
    
    async def get_user_sms_history(
        self,
        user_id: str,
        limit: int = 50,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[SMSDeliveryReport]:
        """
        Get SMS history for a user.
        
        Args:
            user_id: User identifier
            limit: Maximum number of records
            start_date: Start date filter
            end_date: End date filter
            
        Returns:
            List of SMS delivery reports
        """
        try:
            # TODO: Implement database query for user SMS history
            # For now, return empty list
            
            self.logger.info("SMS history retrieved", user_id=user_id, limit=limit)
            return []
            
        except Exception as e:
            self.logger.error("Failed to get SMS history", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get SMS history")
    
    async def _send_via_twilio(self, sms_message: SMSMessage) -> Dict[str, Any]:
        """Send SMS via Twilio API"""
        try:
            url = f"{self.base_url}/Messages.json"
            
            data = {
                "From": self.config.twilio_phone_number,
                "To": sms_message.to_phone,
                "Body": sms_message.message
            }
            
            # Add optional parameters
            if sms_message.callback_url:
                data["StatusCallback"] = sms_message.callback_url
            
            if sms_message.max_price:
                data["MaxPrice"] = str(sms_message.max_price)
            
            response = await self.http_client.post(url, data=data)
            
            if response.status_code == 201:
                result = response.json()
                return {
                    "success": True,
                    "message_id": result["sid"],
                    "status": result["status"],
                    "to": result["to"],
                    "price": result.get("price"),
                    "segments": result.get("num_segments", 1)
                }
            else:
                error_data = response.json()
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Twilio API error: {error_data.get('message', 'Unknown error')}"
                )
                
        except Exception as e:
            self.logger.error("Twilio API error", error=str(e))
            raise
    
    def _validate_phone_number(self, phone_number: str) -> bool:
        """Validate phone number format"""
        # Remove all non-digit characters except +
        cleaned = re.sub(r'[^\d+]', '', phone_number)
        
        # Check if it starts with + and has 10-15 digits
        if cleaned.startswith('+') and len(cleaned) >= 11 and len(cleaned) <= 16:
            return True
        
        # Check if it's a US number without country code
        if len(cleaned) == 10 and cleaned.isdigit():
            return True
        
        return False
    
    async def _check_rate_limits(self, user_id: str) -> bool:
        """Check if user has exceeded SMS rate limits"""
        try:
            # Check hourly limit
            hourly_key = f"sms_rate_hour:{user_id}:{datetime.utcnow().strftime('%Y%m%d%H')}"
            hourly_count = await self.cache_manager.get(hourly_key) or 0
            
            if hourly_count >= self.config.rate_limit_per_hour:
                return False
            
            # Check daily limit
            daily_key = f"sms_rate_day:{user_id}:{datetime.utcnow().strftime('%Y%m%d')}"
            daily_count = await self.cache_manager.get(daily_key) or 0
            
            if daily_count >= self.config.rate_limit_per_day:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to check rate limits", user_id=user_id, error=str(e))
            return False
    
    async def _update_rate_limits(self, user_id: str) -> None:
        """Update rate limit counters"""
        try:
            now = datetime.utcnow()
            
            # Update hourly counter
            hourly_key = f"sms_rate_hour:{user_id}:{now.strftime('%Y%m%d%H')}"
            hourly_count = await self.cache_manager.get(hourly_key) or 0
            await self.cache_manager.set(hourly_key, hourly_count + 1, ttl=3600)
            
            # Update daily counter
            daily_key = f"sms_rate_day:{user_id}:{now.strftime('%Y%m%d')}"
            daily_count = await self.cache_manager.get(daily_key) or 0
            await self.cache_manager.set(daily_key, daily_count + 1, ttl=86400)
            
        except Exception as e:
            self.logger.error("Failed to update rate limits", user_id=user_id, error=str(e))
    
    async def _store_delivery_info(self, user_id: str, result: Dict[str, Any]) -> None:
        """Store SMS delivery information for tracking"""
        try:
            delivery_info = {
                "user_id": user_id,
                "message_id": result["message_id"],
                "status": result["status"],
                "sent_at": datetime.utcnow().isoformat(),
                "to": result["to"],
                "price": result.get("price"),
                "segments": result.get("segments", 1)
            }
            
            cache_key = f"sms_delivery:{user_id}:{result['message_id']}"
            await self.cache_manager.set(cache_key, delivery_info, ttl=86400)
            
            # TODO: Store in database for persistence
            
        except Exception as e:
            self.logger.error("Failed to store delivery info", error=str(e))
    
    def _parse_twilio_date(self, date_string: Optional[str]) -> Optional[datetime]:
        """Parse Twilio date string to datetime"""
        if not date_string:
            return None
        
        try:
            # Twilio uses RFC 2822 format
            from email.utils import parsedate_to_datetime
            return parsedate_to_datetime(date_string)
        except Exception:
            return None
    
    async def get_sms_statistics(self, user_id: str) -> Dict[str, Any]:
        """
        Get SMS usage statistics for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            SMS usage statistics
        """
        try:
            now = datetime.utcnow()
            
            # Get current rate limit usage
            hourly_key = f"sms_rate_hour:{user_id}:{now.strftime('%Y%m%d%H')}"
            daily_key = f"sms_rate_day:{user_id}:{now.strftime('%Y%m%d')}"
            
            hourly_count = await self.cache_manager.get(hourly_key) or 0
            daily_count = await self.cache_manager.get(daily_key) or 0
            
            # TODO: Get historical statistics from database
            
            return {
                "user_id": user_id,
                "current_hour_usage": hourly_count,
                "current_day_usage": daily_count,
                "hourly_limit": self.config.rate_limit_per_hour,
                "daily_limit": self.config.rate_limit_per_day,
                "hourly_remaining": max(0, self.config.rate_limit_per_hour - hourly_count),
                "daily_remaining": max(0, self.config.rate_limit_per_day - daily_count),
                "total_sent": 0,  # TODO: Implement
                "total_delivered": 0,  # TODO: Implement
                "total_failed": 0,  # TODO: Implement
                "total_cost": 0.0  # TODO: Implement
            }
            
        except Exception as e:
            self.logger.error("Failed to get SMS statistics", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get SMS statistics")
