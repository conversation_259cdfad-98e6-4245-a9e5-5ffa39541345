"""
🔗 Correlation Analyzer

Advanced correlation analysis for portfolio optimization, risk management,
and market relationship understanding.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import MarketData
from ...database.models import Token, Portfolio
from ...features.data_pipeline import DataAggregator

logger = get_logger(__name__)


class CorrelationType(str, Enum):
    """Types of correlation analysis"""
    PEARSON = "pearson"
    SPEARMAN = "spearman"
    KENDALL = "kendall"
    ROLLING = "rolling"
    DYNAMIC = "dynamic"


class CorrelationStrength(str, Enum):
    """Correlation strength classifications"""
    VERY_STRONG = "very_strong"      # |r| >= 0.8
    STRONG = "strong"                # 0.6 <= |r| < 0.8
    MODERATE = "moderate"            # 0.4 <= |r| < 0.6
    WEAK = "weak"                    # 0.2 <= |r| < 0.4
    VERY_WEAK = "very_weak"          # |r| < 0.2


class CorrelationAnalyzer:
    """
    🔗 Correlation Analyzer
    
    Provides comprehensive correlation analysis:
    - Token-to-token correlations
    - Portfolio correlation analysis
    - Rolling correlation tracking
    - Dynamic correlation detection
    - Correlation clustering
    - Risk-based correlation metrics
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        
        # Analysis parameters
        self.default_window = 30  # days
        self.rolling_window = 7   # days for rolling correlations
        self.min_observations = 20
        
        # Correlation cache
        self.correlation_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 1800  # 30 minutes
    
    async def analyze_portfolio_correlations(
        self,
        portfolio_id: str,
        analysis_window_days: int = 30
    ) -> Dict[str, Any]:
        """
        Analyze correlations within a portfolio
        
        Args:
            portfolio_id: Portfolio ID
            analysis_window_days: Analysis window in days
            
        Returns:
            Portfolio correlation analysis
        """
        try:
            self.logger.info(f"Analyzing portfolio correlations: {portfolio_id}")
            
            # Get portfolio holdings
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio or not portfolio.positions:
                raise ValueError("Portfolio not found or has no positions")
            
            token_addresses = list(portfolio.positions.keys())
            
            # Get correlation matrix
            correlation_matrix = await self._calculate_correlation_matrix(
                token_addresses, analysis_window_days
            )
            
            # Analyze correlation structure
            correlation_analysis = await self._analyze_correlation_structure(correlation_matrix)
            
            # Risk implications
            risk_analysis = await self._analyze_correlation_risk(
                correlation_matrix, portfolio.positions
            )
            
            # Diversification metrics
            diversification_metrics = await self._calculate_diversification_metrics(
                correlation_matrix, portfolio.positions
            )
            
            # Correlation clusters
            clusters = await self._identify_correlation_clusters(correlation_matrix)
            
            return {
                "portfolio_id": portfolio_id,
                "analysis_time": datetime.utcnow(),
                "analysis_window_days": analysis_window_days,
                "token_count": len(token_addresses),
                "correlation_matrix": correlation_matrix,
                "correlation_analysis": correlation_analysis,
                "risk_analysis": risk_analysis,
                "diversification_metrics": diversification_metrics,
                "correlation_clusters": clusters
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing portfolio correlations: {str(e)}")
            raise
    
    async def analyze_token_correlations(
        self,
        token_addresses: List[str],
        correlation_type: CorrelationType = CorrelationType.PEARSON,
        analysis_window_days: int = 30
    ) -> Dict[str, Any]:
        """
        Analyze correlations between specific tokens
        
        Args:
            token_addresses: List of token addresses
            correlation_type: Type of correlation analysis
            analysis_window_days: Analysis window in days
            
        Returns:
            Token correlation analysis
        """
        try:
            self.logger.info(f"Analyzing correlations for {len(token_addresses)} tokens")
            
            # Calculate correlation matrix
            correlation_matrix = await self._calculate_correlation_matrix(
                token_addresses, analysis_window_days, correlation_type
            )
            
            # Pairwise correlation analysis
            pairwise_analysis = await self._analyze_pairwise_correlations(correlation_matrix)
            
            # Time-varying correlations
            rolling_correlations = await self._calculate_rolling_correlations(
                token_addresses, analysis_window_days
            )
            
            # Correlation stability
            stability_analysis = await self._analyze_correlation_stability(
                token_addresses, analysis_window_days
            )
            
            return {
                "token_addresses": token_addresses,
                "correlation_type": correlation_type,
                "analysis_time": datetime.utcnow(),
                "analysis_window_days": analysis_window_days,
                "correlation_matrix": correlation_matrix,
                "pairwise_analysis": pairwise_analysis,
                "rolling_correlations": rolling_correlations,
                "stability_analysis": stability_analysis
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing token correlations: {str(e)}")
            raise
    
    async def get_correlation_heatmap_data(
        self,
        token_addresses: List[str],
        analysis_window_days: int = 30
    ) -> Dict[str, Any]:
        """Get correlation data formatted for heatmap visualization"""
        try:
            correlation_matrix = await self._calculate_correlation_matrix(
                token_addresses, analysis_window_days
            )
            
            # Format for heatmap
            heatmap_data = []
            for i, token1 in enumerate(token_addresses):
                for j, token2 in enumerate(token_addresses):
                    correlation = correlation_matrix.get(f"{token1}_{token2}", 0)
                    heatmap_data.append({
                        "token1": token1,
                        "token2": token2,
                        "correlation": correlation,
                        "strength": self._classify_correlation_strength(correlation)
                    })
            
            return {
                "heatmap_data": heatmap_data,
                "token_addresses": token_addresses,
                "analysis_time": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting heatmap data: {str(e)}")
            raise
    
    async def find_uncorrelated_tokens(
        self,
        base_tokens: List[str],
        candidate_tokens: List[str],
        max_correlation: float = 0.3,
        analysis_window_days: int = 30
    ) -> Dict[str, Any]:
        """
        Find tokens with low correlation to base tokens
        
        Args:
            base_tokens: Base token addresses
            candidate_tokens: Candidate token addresses to evaluate
            max_correlation: Maximum acceptable correlation
            analysis_window_days: Analysis window in days
            
        Returns:
            Uncorrelated tokens analysis
        """
        try:
            self.logger.info(f"Finding uncorrelated tokens from {len(candidate_tokens)} candidates")
            
            uncorrelated_tokens = []
            
            for candidate in candidate_tokens:
                if candidate in base_tokens:
                    continue
                
                # Calculate correlations with all base tokens
                correlations = []
                for base_token in base_tokens:
                    correlation = await self._calculate_pairwise_correlation(
                        base_token, candidate, analysis_window_days
                    )
                    correlations.append(abs(correlation))
                
                # Check if all correlations are below threshold
                max_correlation_found = max(correlations) if correlations else 0
                avg_correlation = np.mean(correlations) if correlations else 0
                
                if max_correlation_found <= max_correlation:
                    uncorrelated_tokens.append({
                        "token_address": candidate,
                        "max_correlation": float(max_correlation_found),
                        "avg_correlation": float(avg_correlation),
                        "correlations_with_base": {
                            base_token: float(corr)
                            for base_token, corr in zip(base_tokens, correlations)
                        }
                    })
            
            # Sort by average correlation (lowest first)
            uncorrelated_tokens.sort(key=lambda x: x["avg_correlation"])
            
            return {
                "base_tokens": base_tokens,
                "candidate_tokens": candidate_tokens,
                "max_correlation_threshold": max_correlation,
                "uncorrelated_tokens": uncorrelated_tokens,
                "uncorrelated_count": len(uncorrelated_tokens),
                "analysis_time": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error finding uncorrelated tokens: {str(e)}")
            raise
    
    async def _calculate_correlation_matrix(
        self,
        token_addresses: List[str],
        window_days: int,
        correlation_type: CorrelationType = CorrelationType.PEARSON
    ) -> Dict[str, float]:
        """Calculate correlation matrix for tokens"""
        try:
            # Get price data for all tokens
            price_data = {}
            for token_address in token_addresses:
                prices = await self._get_token_price_series(token_address, window_days)
                if len(prices) >= self.min_observations:
                    price_data[token_address] = prices
            
            if len(price_data) < 2:
                return {}
            
            # Calculate correlations
            correlation_matrix = {}
            token_list = list(price_data.keys())
            
            for i, token1 in enumerate(token_list):
                for j, token2 in enumerate(token_list):
                    if i <= j:  # Calculate upper triangle + diagonal
                        correlation = self._calculate_correlation(
                            price_data[token1], price_data[token2], correlation_type
                        )
                        correlation_matrix[f"{token1}_{token2}"] = correlation
                        if i != j:  # Mirror for lower triangle
                            correlation_matrix[f"{token2}_{token1}"] = correlation
            
            return correlation_matrix
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation matrix: {str(e)}")
            return {}
    
    def _calculate_correlation(
        self,
        series1: pd.Series,
        series2: pd.Series,
        correlation_type: CorrelationType
    ) -> float:
        """Calculate correlation between two series"""
        try:
            # Align series
            aligned_data = pd.concat([series1, series2], axis=1).dropna()
            if len(aligned_data) < self.min_observations:
                return 0.0
            
            s1, s2 = aligned_data.iloc[:, 0], aligned_data.iloc[:, 1]
            
            if correlation_type == CorrelationType.PEARSON:
                correlation = s1.corr(s2, method='pearson')
            elif correlation_type == CorrelationType.SPEARMAN:
                correlation = s1.corr(s2, method='spearman')
            elif correlation_type == CorrelationType.KENDALL:
                correlation = s1.corr(s2, method='kendall')
            else:
                correlation = s1.corr(s2, method='pearson')
            
            return float(correlation) if not np.isnan(correlation) else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation: {str(e)}")
            return 0.0
    
    async def _analyze_correlation_structure(self, correlation_matrix: Dict[str, float]) -> Dict[str, Any]:
        """Analyze the structure of correlations"""
        try:
            if not correlation_matrix:
                return {}
            
            # Extract correlation values (excluding diagonal)
            correlations = []
            for key, value in correlation_matrix.items():
                token1, token2 = key.split('_')
                if token1 != token2:  # Exclude self-correlations
                    correlations.append(abs(value))
            
            if not correlations:
                return {}
            
            # Calculate statistics
            avg_correlation = np.mean(correlations)
            max_correlation = np.max(correlations)
            min_correlation = np.min(correlations)
            correlation_std = np.std(correlations)
            
            # Classify correlations by strength
            strength_distribution = {
                "very_strong": len([c for c in correlations if c >= 0.8]),
                "strong": len([c for c in correlations if 0.6 <= c < 0.8]),
                "moderate": len([c for c in correlations if 0.4 <= c < 0.6]),
                "weak": len([c for c in correlations if 0.2 <= c < 0.4]),
                "very_weak": len([c for c in correlations if c < 0.2])
            }
            
            return {
                "avg_correlation": float(avg_correlation),
                "max_correlation": float(max_correlation),
                "min_correlation": float(min_correlation),
                "correlation_std": float(correlation_std),
                "strength_distribution": strength_distribution,
                "total_pairs": len(correlations)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing correlation structure: {str(e)}")
            return {}
    
    async def _analyze_correlation_risk(
        self,
        correlation_matrix: Dict[str, float],
        positions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze risk implications of correlations"""
        try:
            if not correlation_matrix or not positions:
                return {}
            
            # Calculate weighted average correlation
            total_value = sum(pos.get("value_usd", 0) for pos in positions.values())
            if total_value == 0:
                return {}
            
            weighted_correlations = []
            for key, correlation in correlation_matrix.items():
                token1, token2 = key.split('_')
                if token1 != token2 and token1 in positions and token2 in positions:
                    weight1 = positions[token1].get("value_usd", 0) / total_value
                    weight2 = positions[token2].get("value_usd", 0) / total_value
                    weighted_correlation = correlation * weight1 * weight2
                    weighted_correlations.append(weighted_correlation)
            
            portfolio_correlation = sum(weighted_correlations) if weighted_correlations else 0
            
            # Risk concentration
            high_correlation_pairs = [
                key for key, corr in correlation_matrix.items()
                if abs(corr) > 0.7 and key.split('_')[0] != key.split('_')[1]
            ]
            
            return {
                "portfolio_correlation": float(portfolio_correlation),
                "correlation_risk_level": self._classify_correlation_risk(portfolio_correlation),
                "high_correlation_pairs": len(high_correlation_pairs),
                "concentration_risk": float(len(high_correlation_pairs) / len(correlation_matrix)) if correlation_matrix else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing correlation risk: {str(e)}")
            return {}
    
    async def _calculate_diversification_metrics(
        self,
        correlation_matrix: Dict[str, float],
        positions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate diversification metrics"""
        try:
            if not correlation_matrix or not positions:
                return {}
            
            # Extract unique tokens
            tokens = set()
            for key in correlation_matrix.keys():
                token1, token2 = key.split('_')
                tokens.add(token1)
                tokens.add(token2)
            
            n_tokens = len(tokens)
            
            # Calculate average correlation
            correlations = [
                corr for key, corr in correlation_matrix.items()
                if key.split('_')[0] != key.split('_')[1]
            ]
            avg_correlation = np.mean(correlations) if correlations else 0
            
            # Diversification ratio
            # DR = 1 - (average correlation * (n-1) / n)
            diversification_ratio = 1 - (avg_correlation * (n_tokens - 1) / n_tokens) if n_tokens > 1 else 0
            
            # Effective number of assets
            # Based on correlation structure
            effective_assets = 1 + (n_tokens - 1) * (1 - avg_correlation) if avg_correlation < 1 else 1
            
            return {
                "diversification_ratio": float(diversification_ratio),
                "effective_number_of_assets": float(effective_assets),
                "diversification_benefit": float(effective_assets / n_tokens) if n_tokens > 0 else 0,
                "avg_correlation": float(avg_correlation),
                "total_assets": n_tokens
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating diversification metrics: {str(e)}")
            return {}

    async def _identify_correlation_clusters(self, correlation_matrix: Dict[str, float]) -> Dict[str, Any]:
        """Identify clusters of highly correlated tokens"""
        try:
            if not correlation_matrix:
                return {}

            # Extract unique tokens
            tokens = set()
            for key in correlation_matrix.keys():
                token1, token2 = key.split('_')
                tokens.add(token1)
                tokens.add(token2)

            tokens = list(tokens)
            n_tokens = len(tokens)

            if n_tokens < 3:
                return {"clusters": [], "cluster_count": 0}

            # Build adjacency matrix for clustering
            adjacency = np.zeros((n_tokens, n_tokens))
            for i, token1 in enumerate(tokens):
                for j, token2 in enumerate(tokens):
                    key = f"{token1}_{token2}"
                    if key in correlation_matrix:
                        adjacency[i, j] = abs(correlation_matrix[key])

            # Simple clustering based on correlation threshold
            clusters = []
            visited = set()
            correlation_threshold = 0.6

            for i, token in enumerate(tokens):
                if i in visited:
                    continue

                # Find highly correlated tokens
                cluster = [token]
                visited.add(i)

                for j, other_token in enumerate(tokens):
                    if j != i and j not in visited and adjacency[i, j] > correlation_threshold:
                        cluster.append(other_token)
                        visited.add(j)

                if len(cluster) > 1:
                    # Calculate cluster statistics
                    cluster_correlations = []
                    for k in range(len(cluster)):
                        for l in range(k+1, len(cluster)):
                            key = f"{cluster[k]}_{cluster[l]}"
                            if key in correlation_matrix:
                                cluster_correlations.append(correlation_matrix[key])

                    avg_correlation = np.mean(cluster_correlations) if cluster_correlations else 0

                    clusters.append({
                        "tokens": cluster,
                        "size": len(cluster),
                        "avg_correlation": float(avg_correlation),
                        "strength": self._classify_correlation_strength(avg_correlation)
                    })

            return {
                "clusters": clusters,
                "cluster_count": len(clusters),
                "clustered_tokens": sum(len(cluster["tokens"]) for cluster in clusters),
                "unclustered_tokens": n_tokens - sum(len(cluster["tokens"]) for cluster in clusters)
            }

        except Exception as e:
            self.logger.error(f"Error identifying correlation clusters: {str(e)}")
            return {"clusters": [], "cluster_count": 0}

    async def _analyze_pairwise_correlations(self, correlation_matrix: Dict[str, float]) -> Dict[str, Any]:
        """Analyze pairwise correlations in detail"""
        try:
            if not correlation_matrix:
                return {}

            pairwise_data = []
            for key, correlation in correlation_matrix.items():
                token1, token2 = key.split('_')
                if token1 != token2:  # Exclude self-correlations
                    pairwise_data.append({
                        "token1": token1,
                        "token2": token2,
                        "correlation": float(correlation),
                        "abs_correlation": float(abs(correlation)),
                        "strength": self._classify_correlation_strength(abs(correlation))
                    })

            # Sort by absolute correlation
            pairwise_data.sort(key=lambda x: x["abs_correlation"], reverse=True)

            # Top and bottom correlations
            top_correlations = pairwise_data[:5]
            bottom_correlations = pairwise_data[-5:]

            return {
                "total_pairs": len(pairwise_data),
                "top_correlations": top_correlations,
                "bottom_correlations": bottom_correlations,
                "strongest_positive": max(pairwise_data, key=lambda x: x["correlation"]) if pairwise_data else None,
                "strongest_negative": min(pairwise_data, key=lambda x: x["correlation"]) if pairwise_data else None
            }

        except Exception as e:
            self.logger.error(f"Error analyzing pairwise correlations: {str(e)}")
            return {}

    async def _calculate_rolling_correlations(
        self,
        token_addresses: List[str],
        window_days: int
    ) -> Dict[str, Any]:
        """Calculate rolling correlations over time"""
        try:
            if len(token_addresses) < 2:
                return {}

            # For simplicity, calculate rolling correlation for first two tokens
            token1, token2 = token_addresses[0], token_addresses[1]

            # Get price series
            prices1 = await self._get_token_price_series(token1, window_days)
            prices2 = await self._get_token_price_series(token2, window_days)

            if len(prices1) < self.rolling_window or len(prices2) < self.rolling_window:
                return {}

            # Calculate rolling correlations
            rolling_correlations = []
            for i in range(self.rolling_window, min(len(prices1), len(prices2))):
                window_prices1 = prices1.iloc[i-self.rolling_window:i]
                window_prices2 = prices2.iloc[i-self.rolling_window:i]

                correlation = window_prices1.corr(window_prices2)
                if not np.isnan(correlation):
                    rolling_correlations.append({
                        "date": prices1.index[i],
                        "correlation": float(correlation)
                    })

            if not rolling_correlations:
                return {}

            # Calculate statistics
            correlations = [r["correlation"] for r in rolling_correlations]

            return {
                "token_pair": f"{token1}_{token2}",
                "rolling_window_days": self.rolling_window,
                "rolling_correlations": rolling_correlations[-20:],  # Last 20 observations
                "avg_rolling_correlation": float(np.mean(correlations)),
                "correlation_volatility": float(np.std(correlations)),
                "min_correlation": float(min(correlations)),
                "max_correlation": float(max(correlations))
            }

        except Exception as e:
            self.logger.error(f"Error calculating rolling correlations: {str(e)}")
            return {}

    async def _analyze_correlation_stability(
        self,
        token_addresses: List[str],
        window_days: int
    ) -> Dict[str, Any]:
        """Analyze stability of correlations over time"""
        try:
            if len(token_addresses) < 2:
                return {}

            # Calculate correlations for different time periods
            periods = [7, 14, 30, 60]  # days
            stability_data = {}

            for period in periods:
                if period <= window_days:
                    correlation_matrix = await self._calculate_correlation_matrix(
                        token_addresses, period
                    )

                    # Calculate average correlation for this period
                    correlations = [
                        abs(corr) for key, corr in correlation_matrix.items()
                        if key.split('_')[0] != key.split('_')[1]
                    ]

                    if correlations:
                        stability_data[f"{period}d"] = {
                            "avg_correlation": float(np.mean(correlations)),
                            "correlation_std": float(np.std(correlations))
                        }

            # Calculate stability score
            if len(stability_data) >= 2:
                avg_correlations = [data["avg_correlation"] for data in stability_data.values()]
                stability_score = 1 - (np.std(avg_correlations) / np.mean(avg_correlations)) if np.mean(avg_correlations) > 0 else 0
            else:
                stability_score = 0

            return {
                "stability_score": float(max(0, min(1, stability_score))),
                "stability_classification": self._classify_stability(stability_score),
                "period_analysis": stability_data
            }

        except Exception as e:
            self.logger.error(f"Error analyzing correlation stability: {str(e)}")
            return {}

    async def _calculate_pairwise_correlation(
        self,
        token1: str,
        token2: str,
        window_days: int
    ) -> float:
        """Calculate correlation between two specific tokens"""
        try:
            prices1 = await self._get_token_price_series(token1, window_days)
            prices2 = await self._get_token_price_series(token2, window_days)

            return self._calculate_correlation(prices1, prices2, CorrelationType.PEARSON)

        except Exception as e:
            self.logger.error(f"Error calculating pairwise correlation: {str(e)}")
            return 0.0

    async def _get_token_price_series(self, token_address: str, days: int) -> pd.Series:
        """Get price series for a token"""
        try:
            # For now, generate synthetic price data
            # In a real implementation, this would fetch from database/API

            np.random.seed(hash(token_address))
            dates = pd.date_range(
                start=datetime.utcnow() - timedelta(days=days),
                periods=days,
                freq='D'
            )

            # Generate correlated price movements
            returns = np.random.normal(0.001, 0.02, days)
            prices = [100]  # Starting price

            for ret in returns[1:]:
                new_price = prices[-1] * (1 + ret)
                prices.append(max(new_price, 0.01))

            return pd.Series(prices, index=dates)

        except Exception as e:
            self.logger.error(f"Error getting token price series: {str(e)}")
            return pd.Series()

    def _classify_correlation_strength(self, correlation: float) -> str:
        """Classify correlation strength"""
        abs_corr = abs(correlation)

        if abs_corr >= 0.8:
            return CorrelationStrength.VERY_STRONG
        elif abs_corr >= 0.6:
            return CorrelationStrength.STRONG
        elif abs_corr >= 0.4:
            return CorrelationStrength.MODERATE
        elif abs_corr >= 0.2:
            return CorrelationStrength.WEAK
        else:
            return CorrelationStrength.VERY_WEAK

    def _classify_correlation_risk(self, portfolio_correlation: float) -> str:
        """Classify correlation risk level"""
        abs_corr = abs(portfolio_correlation)

        if abs_corr >= 0.7:
            return "high"
        elif abs_corr >= 0.5:
            return "medium"
        elif abs_corr >= 0.3:
            return "low"
        else:
            return "very_low"

    def _classify_stability(self, stability_score: float) -> str:
        """Classify correlation stability"""
        if stability_score >= 0.8:
            return "very_stable"
        elif stability_score >= 0.6:
            return "stable"
        elif stability_score >= 0.4:
            return "moderately_stable"
        elif stability_score >= 0.2:
            return "unstable"
        else:
            return "very_unstable"
