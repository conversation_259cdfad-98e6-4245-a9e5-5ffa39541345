"""
🚀 Request Batcher - Phase 3 API Optimization

Advanced request batching system for optimizing API performance by grouping
similar requests and reducing external API calls.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import json

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)


class BatchStrategy(Enum):
    """Request batching strategies"""
    TIME_WINDOW = "time_window"
    SIZE_THRESHOLD = "size_threshold"
    ADAPTIVE = "adaptive"


@dataclass
class BatchRequest:
    """Individual request in a batch"""
    request_id: str
    endpoint: str
    params: Dict[str, Any]
    callback: Callable
    created_at: datetime
    priority: int = 1  # 1=low, 2=medium, 3=high
    timeout: float = 30.0


@dataclass
class BatchGroup:
    """Group of similar requests to be batched"""
    batch_key: str
    requests: List[BatchRequest] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.utcnow)
    last_updated: datetime = field(default_factory=datetime.utcnow)
    max_size: int = 10
    max_wait_time: float = 5.0  # seconds
    
    def add_request(self, request: BatchRequest) -> None:
        """Add request to batch group"""
        self.requests.append(request)
        self.last_updated = datetime.utcnow()
    
    def is_ready_for_execution(self) -> bool:
        """Check if batch is ready for execution"""
        if not self.requests:
            return False
        
        # Check size threshold
        if len(self.requests) >= self.max_size:
            return True
        
        # Check time threshold
        time_elapsed = (datetime.utcnow() - self.created_at).total_seconds()
        if time_elapsed >= self.max_wait_time:
            return True
        
        # Check if any high priority requests are waiting too long
        high_priority_requests = [r for r in self.requests if r.priority >= 3]
        if high_priority_requests:
            oldest_high_priority = min(high_priority_requests, key=lambda x: x.created_at)
            high_priority_wait = (datetime.utcnow() - oldest_high_priority.created_at).total_seconds()
            if high_priority_wait >= 2.0:  # High priority requests wait max 2 seconds
                return True
        
        return False


class RequestBatcher:
    """
    🚀 Advanced Request Batcher
    
    Features:
    - Intelligent request grouping
    - Multiple batching strategies
    - Priority-based execution
    - Adaptive batch sizing
    - Performance monitoring
    """
    
    def __init__(self):
        self.settings = get_settings()
        
        # Batch groups storage
        self.batch_groups: Dict[str, BatchGroup] = {}
        
        # Configuration
        self.default_batch_size = 10
        self.default_wait_time = 5.0
        self.max_concurrent_batches = 20
        self.cleanup_interval = 60.0  # seconds
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "batched_requests": 0,
            "batch_executions": 0,
            "avg_batch_size": 0.0,
            "avg_wait_time": 0.0,
            "timeouts": 0,
            "errors": 0
        }
        
        # Background tasks
        self.batch_processor_task: Optional[asyncio.Task] = None
        self.cleanup_task: Optional[asyncio.Task] = None
        self.running = False
        
        logger.info("Request batcher initialized")
    
    async def start(self) -> None:
        """Start the request batcher"""
        if self.running:
            return
        
        self.running = True
        
        # Start background tasks
        self.batch_processor_task = asyncio.create_task(self._batch_processor())
        self.cleanup_task = asyncio.create_task(self._cleanup_task())
        
        logger.info("Request batcher started")
    
    async def stop(self) -> None:
        """Stop the request batcher"""
        if not self.running:
            return
        
        self.running = False
        
        # Cancel background tasks
        if self.batch_processor_task:
            self.batch_processor_task.cancel()
            try:
                await self.batch_processor_task
            except asyncio.CancelledError:
                pass
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Process remaining batches
        await self._process_remaining_batches()
        
        logger.info("Request batcher stopped")
    
    async def add_request(
        self,
        endpoint: str,
        params: Dict[str, Any],
        callback: Callable,
        priority: int = 1,
        timeout: float = 30.0,
        batch_key: Optional[str] = None
    ) -> str:
        """
        📥 Add request to batch queue
        
        Args:
            endpoint: API endpoint
            params: Request parameters
            callback: Callback function for result
            priority: Request priority (1=low, 2=medium, 3=high)
            timeout: Request timeout
            batch_key: Custom batch key (auto-generated if None)
            
        Returns:
            Request ID
        """
        try:
            # Generate request ID
            request_id = self._generate_request_id(endpoint, params)
            
            # Generate batch key if not provided
            if batch_key is None:
                batch_key = self._generate_batch_key(endpoint, params)
            
            # Create request
            request = BatchRequest(
                request_id=request_id,
                endpoint=endpoint,
                params=params,
                callback=callback,
                created_at=datetime.utcnow(),
                priority=priority,
                timeout=timeout
            )
            
            # Add to appropriate batch group
            await self._add_to_batch_group(batch_key, request)
            
            self.stats["total_requests"] += 1
            
            logger.debug(
                "Request added to batch",
                request_id=request_id,
                batch_key=batch_key,
                priority=priority
            )
            
            return request_id
            
        except Exception as e:
            logger.error(f"Error adding request to batch: {str(e)}")
            self.stats["errors"] += 1
            raise
    
    async def get_batch_stats(self) -> Dict[str, Any]:
        """
        📊 Get batching statistics
        """
        try:
            # Calculate current metrics
            total_requests_in_queue = sum(len(group.requests) for group in self.batch_groups.values())
            active_batch_groups = len(self.batch_groups)
            
            # Calculate batch efficiency
            batch_efficiency = (
                (self.stats["batched_requests"] / self.stats["total_requests"] * 100)
                if self.stats["total_requests"] > 0 else 0
            )
            
            return {
                **self.stats,
                "current_queue_size": total_requests_in_queue,
                "active_batch_groups": active_batch_groups,
                "batch_efficiency_percent": round(batch_efficiency, 2),
                "avg_requests_per_batch": (
                    self.stats["batched_requests"] / self.stats["batch_executions"]
                    if self.stats["batch_executions"] > 0 else 0
                ),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting batch stats: {str(e)}")
            return {"error": str(e)}
    
    # Private methods
    
    def _generate_request_id(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate unique request ID"""
        content = f"{endpoint}:{json.dumps(params, sort_keys=True)}:{time.time()}"
        return hashlib.md5(content.encode()).hexdigest()[:16]
    
    def _generate_batch_key(self, endpoint: str, params: Dict[str, Any]) -> str:
        """Generate batch key for grouping similar requests"""
        # Group by endpoint and certain parameter patterns
        key_params = {}
        
        # Include endpoint
        key_params["endpoint"] = endpoint
        
        # Include certain parameters that should group requests
        groupable_params = ["token_address", "user_id", "portfolio_id", "signal_type"]
        for param in groupable_params:
            if param in params:
                key_params[param] = params[param]
        
        # Create hash of key parameters
        content = json.dumps(key_params, sort_keys=True)
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    async def _add_to_batch_group(self, batch_key: str, request: BatchRequest) -> None:
        """Add request to appropriate batch group"""
        if batch_key not in self.batch_groups:
            # Create new batch group
            self.batch_groups[batch_key] = BatchGroup(
                batch_key=batch_key,
                max_size=self._get_adaptive_batch_size(request.endpoint),
                max_wait_time=self._get_adaptive_wait_time(request.priority)
            )
        
        # Add request to group
        self.batch_groups[batch_key].add_request(request)
    
    def _get_adaptive_batch_size(self, endpoint: str) -> int:
        """Get adaptive batch size based on endpoint characteristics"""
        # Different endpoints may have different optimal batch sizes
        endpoint_configs = {
            "/api/tokens/price": 20,  # Price requests can be batched heavily
            "/api/signals/generate": 5,  # Signal generation needs smaller batches
            "/api/portfolio/update": 10,  # Portfolio updates moderate batching
        }
        
        return endpoint_configs.get(endpoint, self.default_batch_size)
    
    def _get_adaptive_wait_time(self, priority: int) -> float:
        """Get adaptive wait time based on request priority"""
        priority_wait_times = {
            1: 10.0,  # Low priority can wait longer
            2: 5.0,   # Medium priority standard wait
            3: 2.0    # High priority shorter wait
        }
        
        return priority_wait_times.get(priority, self.default_wait_time)

    async def _batch_processor(self) -> None:
        """Background task to process ready batches"""
        while self.running:
            try:
                # Find ready batches
                ready_batches = [
                    group for group in self.batch_groups.values()
                    if group.is_ready_for_execution()
                ]

                # Process ready batches
                if ready_batches:
                    # Sort by priority (highest priority first)
                    ready_batches.sort(
                        key=lambda g: max(r.priority for r in g.requests),
                        reverse=True
                    )

                    # Process batches concurrently (up to max concurrent)
                    batch_tasks = []
                    for batch in ready_batches[:self.max_concurrent_batches]:
                        task = asyncio.create_task(self._execute_batch(batch))
                        batch_tasks.append(task)

                    if batch_tasks:
                        await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Sleep briefly before next check
                await asyncio.sleep(0.1)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in batch processor: {str(e)}")
                await asyncio.sleep(1.0)

    async def _execute_batch(self, batch_group: BatchGroup) -> None:
        """Execute a batch of requests"""
        try:
            batch_start_time = time.time()

            logger.info(
                "Executing batch",
                batch_key=batch_group.batch_key,
                request_count=len(batch_group.requests),
                avg_priority=sum(r.priority for r in batch_group.requests) / len(batch_group.requests)
            )

            # Remove batch from queue
            if batch_group.batch_key in self.batch_groups:
                del self.batch_groups[batch_group.batch_key]

            # Group requests by endpoint for efficient processing
            endpoint_groups = {}
            for request in batch_group.requests:
                if request.endpoint not in endpoint_groups:
                    endpoint_groups[request.endpoint] = []
                endpoint_groups[request.endpoint].append(request)

            # Process each endpoint group
            for endpoint, requests in endpoint_groups.items():
                try:
                    await self._process_endpoint_batch(endpoint, requests)
                except Exception as e:
                    logger.error(f"Error processing endpoint batch {endpoint}: {str(e)}")
                    # Call error callbacks
                    for request in requests:
                        try:
                            await request.callback(None, error=str(e))
                        except Exception as callback_error:
                            logger.error(f"Error in error callback: {str(callback_error)}")

            # Update statistics
            batch_execution_time = time.time() - batch_start_time
            self.stats["batch_executions"] += 1
            self.stats["batched_requests"] += len(batch_group.requests)
            self.stats["avg_batch_size"] = (
                self.stats["batched_requests"] / self.stats["batch_executions"]
            )
            self.stats["avg_wait_time"] = (
                (batch_start_time - batch_group.created_at.timestamp()) +
                self.stats["avg_wait_time"] * (self.stats["batch_executions"] - 1)
            ) / self.stats["batch_executions"]

            logger.debug(
                "Batch execution completed",
                batch_key=batch_group.batch_key,
                execution_time=f"{batch_execution_time:.3f}s",
                request_count=len(batch_group.requests)
            )

        except Exception as e:
            logger.error(f"Error executing batch: {str(e)}")
            self.stats["errors"] += 1

    async def _process_endpoint_batch(self, endpoint: str, requests: List[BatchRequest]) -> None:
        """Process a batch of requests for a specific endpoint"""
        try:
            # Different endpoints may have different batch processing logic
            if endpoint.startswith("/api/tokens/"):
                await self._process_token_batch(requests)
            elif endpoint.startswith("/api/signals/"):
                await self._process_signal_batch(requests)
            elif endpoint.startswith("/api/portfolio/"):
                await self._process_portfolio_batch(requests)
            else:
                # Default: process requests individually
                await self._process_individual_requests(requests)

        except Exception as e:
            logger.error(f"Error processing endpoint batch {endpoint}: {str(e)}")
            raise

    async def _process_token_batch(self, requests: List[BatchRequest]) -> None:
        """Process batched token requests efficiently"""
        try:
            # Extract unique token addresses
            token_addresses = set()
            for request in requests:
                if "token_address" in request.params:
                    token_addresses.add(request.params["token_address"])
                elif "addresses" in request.params:
                    token_addresses.update(request.params["addresses"])

            if not token_addresses:
                await self._process_individual_requests(requests)
                return

            # Batch fetch token data (this would integrate with data pipeline)
            logger.info(f"Batch fetching data for {len(token_addresses)} tokens")

            # Simulate batch data fetching
            batch_results = {}
            for address in token_addresses:
                # This would be replaced with actual batch API call
                batch_results[address] = {
                    "address": address,
                    "price": 1.0,  # Placeholder
                    "volume": 1000.0,  # Placeholder
                    "timestamp": datetime.utcnow().isoformat()
                }

            # Distribute results to individual request callbacks
            for request in requests:
                try:
                    request_addresses = []
                    if "token_address" in request.params:
                        request_addresses = [request.params["token_address"]]
                    elif "addresses" in request.params:
                        request_addresses = request.params["addresses"]

                    # Prepare result for this request
                    request_result = {
                        address: batch_results.get(address)
                        for address in request_addresses
                        if address in batch_results
                    }

                    # Call request callback
                    await request.callback(request_result)

                except Exception as e:
                    logger.error(f"Error calling callback for request {request.request_id}: {str(e)}")
                    await request.callback(None, error=str(e))

        except Exception as e:
            logger.error(f"Error processing token batch: {str(e)}")
            # Fallback to individual processing
            await self._process_individual_requests(requests)

    async def _process_signal_batch(self, requests: List[BatchRequest]) -> None:
        """Process batched signal requests"""
        # For now, process individually as signal generation is complex
        await self._process_individual_requests(requests)

    async def _process_portfolio_batch(self, requests: List[BatchRequest]) -> None:
        """Process batched portfolio requests"""
        # For now, process individually as portfolio operations are complex
        await self._process_individual_requests(requests)

    async def _process_individual_requests(self, requests: List[BatchRequest]) -> None:
        """Process requests individually when batching is not beneficial"""
        tasks = []
        for request in requests:
            task = asyncio.create_task(self._process_single_request(request))
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _process_single_request(self, request: BatchRequest) -> None:
        """Process a single request"""
        try:
            # This would integrate with the actual API processing logic
            # For now, we'll simulate processing
            await asyncio.sleep(0.01)  # Simulate processing time

            result = {
                "request_id": request.request_id,
                "endpoint": request.endpoint,
                "params": request.params,
                "processed_at": datetime.utcnow().isoformat(),
                "simulated": True
            }

            await request.callback(result)

        except Exception as e:
            logger.error(f"Error processing single request {request.request_id}: {str(e)}")
            await request.callback(None, error=str(e))

    async def _cleanup_task(self) -> None:
        """Background task for cleanup operations"""
        while self.running:
            try:
                await asyncio.sleep(self.cleanup_interval)

                # Clean up expired batch groups
                current_time = datetime.utcnow()
                expired_groups = []

                for batch_key, group in self.batch_groups.items():
                    # Check if group has been waiting too long
                    wait_time = (current_time - group.created_at).total_seconds()
                    if wait_time > group.max_wait_time * 2:  # Double the max wait time
                        expired_groups.append(batch_key)

                # Process expired groups
                for batch_key in expired_groups:
                    group = self.batch_groups.pop(batch_key, None)
                    if group:
                        logger.warning(
                            "Processing expired batch group",
                            batch_key=batch_key,
                            request_count=len(group.requests),
                            wait_time=wait_time
                        )
                        asyncio.create_task(self._execute_batch(group))
                        self.stats["timeouts"] += 1

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {str(e)}")

    async def _process_remaining_batches(self) -> None:
        """Process all remaining batches during shutdown"""
        try:
            remaining_groups = list(self.batch_groups.values())
            self.batch_groups.clear()

            if remaining_groups:
                logger.info(f"Processing {len(remaining_groups)} remaining batch groups")

                tasks = [
                    asyncio.create_task(self._execute_batch(group))
                    for group in remaining_groups
                ]

                await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logger.error(f"Error processing remaining batches: {str(e)}")
