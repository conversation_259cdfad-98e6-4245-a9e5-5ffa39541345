"""
🪙 Token Repository

Repository for token data access operations.
"""

from typing import List, Optional, Dict, Any
from ..models.token import Token
from .base_repository import BaseRepository


class TokenRepository(BaseRepository):
    """Repository for token operations"""
    
    def __init__(self):
        super().__init__(Token)
    
    async def get_by_address(self, address: str) -> Optional[Token]:
        """Get token by address"""
        return await self.find_one({"address": address})
    
    async def get_by_symbol(self, symbol: str) -> Optional[Token]:
        """Get token by symbol"""
        return await self.find_one({"symbol": symbol})
    
    async def get_active_tokens(self) -> List[Token]:
        """Get all active tokens"""
        return await self.find_many({"is_active": True})
    
    async def search_tokens(self, query: str, limit: int = 20) -> List[Token]:
        """Search tokens by name or symbol"""
        filters = {
            "$or": [
                {"name": {"$regex": query, "$options": "i"}},
                {"symbol": {"$regex": query, "$options": "i"}}
            ]
        }
        return await self.find_many(filters, limit=limit)
