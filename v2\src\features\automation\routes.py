"""
🤖 Automation API Routes

FastAPI routes for trading automation including order management,
risk controls, execution engine, and trading bot operations.
"""

from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, OrderStatus
from ...database.models import Order, Portfolio
from ...database.models.order import OrderPriority
from .order_manager import OrderManager
from .risk_manager import RiskManager, RiskLevel
from .execution_engine import ExecutionEngine, ExecutionStrategy
from .trading_bot import TradingBot, TradingMode
from .dex_integrator import DEXIntegrator

logger = get_logger(__name__)
router = APIRouter()

# Initialize components
order_manager = OrderManager()
risk_manager = RiskManager()
execution_engine = ExecutionEngine()
dex_integrator = DEXIntegrator()

# Active trading bots
active_bots: Dict[str, TradingBot] = {}


# Request/Response Models
class CreateOrderRequest(BaseModel):
    portfolio_id: str = Field(..., description="Portfolio ID")
    token_address: str = Field(..., description="Token address")
    side: SignalType = Field(..., description="Order side")
    order_type: OrderType = Field(..., description="Order type")
    quantity: Decimal = Field(..., description="Order quantity")
    price: Optional[Decimal] = Field(None, description="Order price")
    stop_price: Optional[Decimal] = Field(None, description="Stop price")
    limit_price: Optional[Decimal] = Field(None, description="Limit price")
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    priority: OrderPriority = Field(OrderPriority.NORMAL, description="Order priority")
    expires_hours: int = Field(24, description="Order expiration in hours")


class ExecuteOrderRequest(BaseModel):
    order_id: str = Field(..., description="Order ID to execute")
    strategy: ExecutionStrategy = Field(ExecutionStrategy.SMART_ROUTING, description="Execution strategy")
    max_slippage: Optional[Decimal] = Field(None, description="Maximum slippage")
    time_limit: Optional[int] = Field(None, description="Time limit in seconds")


class TradingBotConfig(BaseModel):
    trading_mode: TradingMode = Field(TradingMode.PAPER_TRADING, description="Trading mode")
    signal_check_interval: int = Field(60, description="Signal check interval in seconds")
    max_concurrent_orders: int = Field(5, description="Maximum concurrent orders")
    min_signal_strength: int = Field(2, description="Minimum signal strength")
    auto_execute: bool = Field(True, description="Auto-execute orders")
    risk_limits: Dict[str, Any] = Field(default_factory=dict, description="Risk limits")


# Order Management Routes
@router.post("/orders", response_model=Dict[str, Any])
async def create_order(request: CreateOrderRequest):
    """Create a new trading order"""
    try:
        expires_at = datetime.utcnow() + timedelta(hours=request.expires_hours)
        
        order = await order_manager.create_order(
            portfolio_id=request.portfolio_id,
            token_address=request.token_address,
            side=request.side,
            order_type=request.order_type,
            quantity=request.quantity,
            price=request.price,
            stop_price=request.stop_price,
            limit_price=request.limit_price,
            stop_loss=request.stop_loss,
            take_profit=request.take_profit,
            priority=request.priority,
            expires_at=expires_at
        )
        
        if order:
            return {
                "success": True,
                "order_id": str(order.id),
                "order": order.to_dict()
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to create order")
            
    except Exception as e:
        logger.error(f"Error creating order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/orders/{order_id}", response_model=Dict[str, Any])
async def get_order_status(order_id: str):
    """Get order status and details"""
    try:
        order_status = await order_manager.get_order_status(order_id)
        
        if order_status:
            return {"success": True, "order": order_status}
        else:
            raise HTTPException(status_code=404, detail="Order not found")
            
    except Exception as e:
        logger.error(f"Error getting order status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/orders/{order_id}/modify", response_model=Dict[str, Any])
async def modify_order(
    order_id: str,
    quantity: Optional[Decimal] = Body(None),
    price: Optional[Decimal] = Body(None),
    stop_price: Optional[Decimal] = Body(None),
    limit_price: Optional[Decimal] = Body(None)
):
    """Modify an existing order"""
    try:
        success = await order_manager.modify_order(
            order_id=order_id,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            limit_price=limit_price
        )
        
        if success:
            return {"success": True, "message": "Order modified successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to modify order")
            
    except Exception as e:
        logger.error(f"Error modifying order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/orders/{order_id}", response_model=Dict[str, Any])
async def cancel_order(order_id: str, reason: str = "User requested"):
    """Cancel an order"""
    try:
        success = await order_manager.cancel_order(order_id, reason)
        
        if success:
            return {"success": True, "message": "Order cancelled successfully"}
        else:
            raise HTTPException(status_code=400, detail="Failed to cancel order")
            
    except Exception as e:
        logger.error(f"Error cancelling order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/portfolios/{portfolio_id}/orders", response_model=Dict[str, Any])
async def get_portfolio_orders(
    portfolio_id: str,
    status_filter: Optional[List[OrderStatus]] = Query(None),
    limit: int = Query(100, le=1000)
):
    """Get orders for a portfolio"""
    try:
        orders = await order_manager.get_portfolio_orders(
            portfolio_id=portfolio_id,
            status_filter=status_filter,
            limit=limit
        )
        
        return {
            "success": True,
            "portfolio_id": portfolio_id,
            "orders": orders,
            "count": len(orders)
        }
        
    except Exception as e:
        logger.error(f"Error getting portfolio orders: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Risk Management Routes
@router.post("/risk/assess", response_model=Dict[str, Any])
async def assess_trade_risk(
    portfolio_id: str = Body(...),
    token_address: str = Body(...),
    side: SignalType = Body(...),
    quantity: Decimal = Body(...),
    price: Decimal = Body(...)
):
    """Assess risk for a proposed trade"""
    try:
        risk_assessment = await risk_manager.assess_trade_risk(
            portfolio_id=portfolio_id,
            token_address=token_address,
            side=side,
            quantity=quantity,
            price=price
        )
        
        return {"success": True, "risk_assessment": risk_assessment}
        
    except Exception as e:
        logger.error(f"Error assessing trade risk: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk/limits/{portfolio_id}", response_model=Dict[str, Any])
async def check_risk_limits(portfolio_id: str):
    """Check if portfolio is within risk limits"""
    try:
        risk_check = await risk_manager.check_risk_limits(portfolio_id)
        return {"success": True, "risk_check": risk_check}
        
    except Exception as e:
        logger.error(f"Error checking risk limits: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/risk/emergency-stop", response_model=Dict[str, Any])
async def trigger_emergency_stop(
    reason: str = Body(...),
    portfolio_id: Optional[str] = Body(None)
):
    """Trigger emergency stop"""
    try:
        await risk_manager.trigger_emergency_stop(reason, portfolio_id)
        return {"success": True, "message": "Emergency stop triggered"}
        
    except Exception as e:
        logger.error(f"Error triggering emergency stop: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Execution Engine Routes
@router.post("/execute", response_model=Dict[str, Any])
async def execute_order(request: ExecuteOrderRequest):
    """Execute an order"""
    try:
        result = await execution_engine.execute_order(
            order_id=request.order_id,
            strategy=request.strategy,
            max_slippage=request.max_slippage,
            time_limit=request.time_limit
        )
        
        return {"success": True, "execution_result": result}
        
    except Exception as e:
        logger.error(f"Error executing order: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/execute/batch", response_model=Dict[str, Any])
async def batch_execute_orders(
    order_ids: List[str] = Body(...),
    strategy: ExecutionStrategy = Body(ExecutionStrategy.SMART_ROUTING)
):
    """Execute multiple orders as a batch"""
    try:
        result = await execution_engine.batch_execute_orders(order_ids, strategy)
        return {"success": True, "batch_result": result}
        
    except Exception as e:
        logger.error(f"Error batch executing orders: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# DEX Integration Routes
@router.get("/dex/routes", response_model=Dict[str, Any])
async def find_best_route(
    input_token: str = Query(...),
    output_token: str = Query(...),
    amount: Decimal = Query(...),
    side: SignalType = Query(...)
):
    """Find best trading route across DEXs"""
    try:
        route = await dex_integrator.find_best_route(input_token, output_token, amount, side)
        
        if route:
            return {
                "success": True,
                "route": {
                    "dex": route.dex,
                    "input_token": route.input_token,
                    "output_token": route.output_token,
                    "input_amount": route.input_amount,
                    "output_amount": route.output_amount,
                    "effective_price": route.effective_price,
                    "price_impact": route.price_impact,
                    "fees": route.fees
                }
            }
        else:
            return {"success": False, "message": "No suitable route found"}
            
    except Exception as e:
        logger.error(f"Error finding best route: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/dex/arbitrage", response_model=Dict[str, Any])
async def detect_arbitrage_opportunities(
    token_pairs: List[List[str]] = Body(...),
    min_profit_usd: Optional[Decimal] = Body(None)
):
    """Detect arbitrage opportunities"""
    try:
        # Convert list of lists to list of tuples
        pairs = [(pair[0], pair[1]) for pair in token_pairs]
        
        opportunities = await dex_integrator.detect_arbitrage_opportunities(
            pairs, min_profit_usd
        )
        
        return {
            "success": True,
            "opportunities": opportunities,
            "count": len(opportunities)
        }
        
    except Exception as e:
        logger.error(f"Error detecting arbitrage opportunities: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Trading Bot Routes
@router.post("/bots/{portfolio_id}/start", response_model=Dict[str, Any])
async def start_trading_bot(portfolio_id: str, config: TradingBotConfig):
    """Start a trading bot for a portfolio"""
    try:
        if portfolio_id in active_bots:
            raise HTTPException(status_code=400, detail="Trading bot already running for this portfolio")

        # Create and start bot
        bot = TradingBot(portfolio_id, config.dict())
        await bot.start()

        # Add to active bots
        active_bots[portfolio_id] = bot

        return {
            "success": True,
            "message": "Trading bot started successfully",
            "portfolio_id": portfolio_id,
            "config": config.dict()
        }

    except Exception as e:
        logger.error(f"Error starting trading bot: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{portfolio_id}/stop", response_model=Dict[str, Any])
async def stop_trading_bot(portfolio_id: str):
    """Stop a trading bot"""
    try:
        if portfolio_id not in active_bots:
            raise HTTPException(status_code=404, detail="No active trading bot found for this portfolio")

        bot = active_bots[portfolio_id]
        await bot.stop()

        # Remove from active bots
        del active_bots[portfolio_id]

        return {
            "success": True,
            "message": "Trading bot stopped successfully",
            "portfolio_id": portfolio_id
        }

    except Exception as e:
        logger.error(f"Error stopping trading bot: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{portfolio_id}/pause", response_model=Dict[str, Any])
async def pause_trading_bot(portfolio_id: str):
    """Pause a trading bot"""
    try:
        if portfolio_id not in active_bots:
            raise HTTPException(status_code=404, detail="No active trading bot found for this portfolio")

        bot = active_bots[portfolio_id]
        await bot.pause()

        return {
            "success": True,
            "message": "Trading bot paused successfully",
            "portfolio_id": portfolio_id
        }

    except Exception as e:
        logger.error(f"Error pausing trading bot: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{portfolio_id}/resume", response_model=Dict[str, Any])
async def resume_trading_bot(portfolio_id: str):
    """Resume a trading bot"""
    try:
        if portfolio_id not in active_bots:
            raise HTTPException(status_code=404, detail="No active trading bot found for this portfolio")

        bot = active_bots[portfolio_id]
        await bot.resume()

        return {
            "success": True,
            "message": "Trading bot resumed successfully",
            "portfolio_id": portfolio_id
        }

    except Exception as e:
        logger.error(f"Error resuming trading bot: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bots/{portfolio_id}/emergency-stop", response_model=Dict[str, Any])
async def emergency_stop_trading_bot(portfolio_id: str, reason: str = Body(...)):
    """Emergency stop a trading bot"""
    try:
        if portfolio_id not in active_bots:
            raise HTTPException(status_code=404, detail="No active trading bot found for this portfolio")

        bot = active_bots[portfolio_id]
        await bot.emergency_stop(reason)

        return {
            "success": True,
            "message": "Trading bot emergency stop triggered",
            "portfolio_id": portfolio_id,
            "reason": reason
        }

    except Exception as e:
        logger.error(f"Error emergency stopping trading bot: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bots/{portfolio_id}/status", response_model=Dict[str, Any])
async def get_trading_bot_status(portfolio_id: str):
    """Get trading bot status"""
    try:
        if portfolio_id not in active_bots:
            return {
                "success": True,
                "portfolio_id": portfolio_id,
                "status": "stopped",
                "message": "No active trading bot found"
            }

        bot = active_bots[portfolio_id]
        status = await bot.get_status()

        return {"success": True, "bot_status": status}

    except Exception as e:
        logger.error(f"Error getting trading bot status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/bots", response_model=Dict[str, Any])
async def list_active_bots():
    """List all active trading bots"""
    try:
        bot_statuses = {}

        for portfolio_id, bot in active_bots.items():
            status = await bot.get_status()
            bot_statuses[portfolio_id] = status

        return {
            "success": True,
            "active_bots": bot_statuses,
            "count": len(active_bots)
        }

    except Exception as e:
        logger.error(f"Error listing active bots: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# System Status Routes
@router.get("/status", response_model=Dict[str, Any])
async def get_automation_status():
    """Get overall automation system status"""
    try:
        # Get system statistics
        total_active_orders = await Order.find({"status": {"$in": ["pending", "partially_filled"]}}).count()
        total_portfolios = len(active_bots)

        return {
            "success": True,
            "system_status": {
                "active_bots": total_portfolios,
                "active_orders": total_active_orders,
                "emergency_stop_active": risk_manager.emergency_stop_active,
                "emergency_stop_reason": risk_manager.emergency_stop_reason
            },
            "components": {
                "order_manager": "operational",
                "risk_manager": "operational",
                "execution_engine": "operational",
                "dex_integrator": "operational"
            }
        }

    except Exception as e:
        logger.error(f"Error getting automation status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
