"""
📊 Cache Monitor - Phase 3 Production Optimization

Comprehensive cache monitoring and analytics service for multi-level caching system.
Provides real-time cache performance metrics, health monitoring, and optimization recommendations.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.types import APIResponse

logger = get_logger(__name__)


class CacheHealthStatus(Enum):
    """Cache health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class CacheHealthMetric:
    """Cache health metric"""
    name: str
    value: float
    threshold_warning: float
    threshold_critical: float
    unit: str
    status: CacheHealthStatus
    message: str
    timestamp: datetime


@dataclass
class CacheOptimizationRecommendation:
    """Cache optimization recommendation"""
    type: str
    priority: str  # high, medium, low
    description: str
    impact: str
    implementation_effort: str
    estimated_improvement: str


class CacheMonitor:
    """
    📊 Cache Performance Monitor
    
    Provides comprehensive monitoring for multi-level caching system:
    - Real-time performance metrics
    - Health status monitoring
    - Optimization recommendations
    - Alerting for cache issues
    """
    
    def __init__(self):
        self.settings = get_settings()
        
        # Health thresholds
        self.health_thresholds = {
            "hit_ratio": {"warning": 70.0, "critical": 50.0},  # Percentage
            "avg_access_time": {"warning": 50.0, "critical": 100.0},  # Milliseconds
            "memory_utilization": {"warning": 80.0, "critical": 95.0},  # Percentage
            "eviction_rate": {"warning": 10.0, "critical": 25.0},  # Per minute
            "error_rate": {"warning": 1.0, "critical": 5.0}  # Percentage
        }
        
        # Historical metrics for trend analysis
        self.historical_metrics: List[Dict[str, Any]] = []
        self.max_history_size = 1440  # 24 hours of minute-by-minute data
        
        # Alert tracking
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        self.alert_cooldown = 300  # 5 minutes between same alerts
        
        logger.info("Cache monitor initialized")
    
    async def collect_metrics(self, cache_manager) -> Dict[str, Any]:
        """
        📊 Collect comprehensive cache metrics
        
        Args:
            cache_manager: Advanced cache manager instance
            
        Returns:
            Comprehensive metrics dictionary
        """
        try:
            # Get cache analytics
            analytics = await cache_manager.get_cache_analytics()
            
            # Calculate derived metrics
            derived_metrics = self._calculate_derived_metrics(analytics)
            
            # Combine all metrics
            metrics = {
                **analytics,
                "derived_metrics": derived_metrics,
                "collection_timestamp": datetime.utcnow().isoformat()
            }
            
            # Store in history
            self._store_historical_metrics(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error collecting cache metrics: {str(e)}")
            return {"error": str(e)}
    
    async def assess_cache_health(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        🏥 Assess overall cache health
        
        Args:
            metrics: Cache metrics dictionary
            
        Returns:
            Health assessment with status and recommendations
        """
        try:
            health_metrics = []
            overall_status = CacheHealthStatus.HEALTHY
            
            # Assess hit ratio
            hit_ratio_metric = self._assess_hit_ratio(metrics)
            health_metrics.append(hit_ratio_metric)
            if hit_ratio_metric.status.value > overall_status.value:
                overall_status = hit_ratio_metric.status
            
            # Assess access time
            access_time_metric = self._assess_access_time(metrics)
            health_metrics.append(access_time_metric)
            if access_time_metric.status.value > overall_status.value:
                overall_status = access_time_metric.status
            
            # Assess memory utilization
            memory_metric = self._assess_memory_utilization(metrics)
            health_metrics.append(memory_metric)
            if memory_metric.status.value > overall_status.value:
                overall_status = memory_metric.status
            
            # Assess eviction rate
            eviction_metric = self._assess_eviction_rate(metrics)
            health_metrics.append(eviction_metric)
            if eviction_metric.status.value > overall_status.value:
                overall_status = eviction_metric.status
            
            # Generate recommendations
            recommendations = self._generate_recommendations(health_metrics, metrics)
            
            health_assessment = {
                "overall_status": overall_status.value,
                "health_metrics": [asdict(metric) for metric in health_metrics],
                "recommendations": [asdict(rec) for rec in recommendations],
                "assessment_timestamp": datetime.utcnow().isoformat()
            }
            
            # Check for alerts
            await self._check_and_send_alerts(health_assessment)
            
            return health_assessment
            
        except Exception as e:
            logger.error(f"Error assessing cache health: {str(e)}")
            return {
                "overall_status": CacheHealthStatus.UNKNOWN.value,
                "error": str(e)
            }
    
    async def get_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """
        📈 Get cache performance trends
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Performance trends and analysis
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # Filter historical metrics
            relevant_metrics = [
                metric for metric in self.historical_metrics
                if datetime.fromisoformat(metric["collection_timestamp"]) > cutoff_time
            ]
            
            if not relevant_metrics:
                return {"message": "No historical data available"}
            
            # Calculate trends
            trends = {
                "hit_ratio_trend": self._calculate_trend(relevant_metrics, "hit_ratio"),
                "access_time_trend": self._calculate_trend(relevant_metrics, "avg_access_time"),
                "memory_usage_trend": self._calculate_trend(relevant_metrics, "memory_utilization"),
                "query_volume_trend": self._calculate_trend(relevant_metrics, "total_queries"),
                "analysis_period_hours": hours,
                "data_points": len(relevant_metrics),
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
            
            return trends
            
        except Exception as e:
            logger.error(f"Error getting performance trends: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_derived_metrics(self, analytics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate derived metrics from raw analytics"""
        try:
            derived = {}
            
            # Overall hit ratio across all cache levels
            total_hits = sum(
                level_stats.get("hits", 0)
                for level_stats in analytics.get("cache_levels", {}).values()
            )
            total_misses = sum(
                level_stats.get("misses", 0)
                for level_stats in analytics.get("cache_levels", {}).values()
            )
            total_requests = total_hits + total_misses
            
            derived["overall_hit_ratio"] = (
                (total_hits / total_requests * 100) if total_requests > 0 else 0
            )
            
            # Cache efficiency score (0-100)
            memory_cache = analytics.get("memory_cache", {})
            performance = analytics.get("performance", {})
            
            hit_ratio_score = min(derived["overall_hit_ratio"], 100)
            access_time_score = max(0, 100 - performance.get("avg_access_time_ms", 0))
            memory_efficiency_score = max(0, 100 - memory_cache.get("memory_utilization_percent", 0))
            
            derived["cache_efficiency_score"] = (
                hit_ratio_score * 0.5 + access_time_score * 0.3 + memory_efficiency_score * 0.2
            )
            
            # Eviction rate (evictions per minute)
            total_evictions = sum(
                level_stats.get("evictions", 0)
                for level_stats in analytics.get("cache_levels", {}).values()
            )
            derived["eviction_rate_per_minute"] = total_evictions  # Simplified for now
            
            return derived
            
        except Exception as e:
            logger.error(f"Error calculating derived metrics: {str(e)}")
            return {}
    
    def _store_historical_metrics(self, metrics: Dict[str, Any]) -> None:
        """Store metrics in historical data"""
        try:
            # Extract key metrics for historical tracking
            historical_entry = {
                "timestamp": metrics["collection_timestamp"],
                "hit_ratio": metrics.get("derived_metrics", {}).get("overall_hit_ratio", 0),
                "avg_access_time": metrics.get("performance", {}).get("avg_access_time_ms", 0),
                "memory_utilization": metrics.get("memory_cache", {}).get("memory_utilization_percent", 0),
                "total_queries": metrics.get("performance", {}).get("total_queries", 0),
                "cache_efficiency_score": metrics.get("derived_metrics", {}).get("cache_efficiency_score", 0)
            }
            
            self.historical_metrics.append(historical_entry)
            
            # Trim history to max size
            if len(self.historical_metrics) > self.max_history_size:
                self.historical_metrics = self.historical_metrics[-self.max_history_size:]
                
        except Exception as e:
            logger.error(f"Error storing historical metrics: {str(e)}")
    
    def _assess_hit_ratio(self, metrics: Dict[str, Any]) -> CacheHealthMetric:
        """Assess cache hit ratio health"""
        hit_ratio = metrics.get("derived_metrics", {}).get("overall_hit_ratio", 0)
        thresholds = self.health_thresholds["hit_ratio"]
        
        if hit_ratio >= thresholds["warning"]:
            status = CacheHealthStatus.HEALTHY
            message = "Cache hit ratio is optimal"
        elif hit_ratio >= thresholds["critical"]:
            status = CacheHealthStatus.WARNING
            message = "Cache hit ratio is below optimal levels"
        else:
            status = CacheHealthStatus.CRITICAL
            message = "Cache hit ratio is critically low"
        
        return CacheHealthMetric(
            name="hit_ratio",
            value=hit_ratio,
            threshold_warning=thresholds["warning"],
            threshold_critical=thresholds["critical"],
            unit="percentage",
            status=status,
            message=message,
            timestamp=datetime.utcnow()
        )

    def _assess_access_time(self, metrics: Dict[str, Any]) -> CacheHealthMetric:
        """Assess cache access time health"""
        access_time = metrics.get("performance", {}).get("avg_access_time_ms", 0)
        thresholds = self.health_thresholds["avg_access_time"]

        if access_time <= thresholds["warning"]:
            status = CacheHealthStatus.HEALTHY
            message = "Cache access time is optimal"
        elif access_time <= thresholds["critical"]:
            status = CacheHealthStatus.WARNING
            message = "Cache access time is elevated"
        else:
            status = CacheHealthStatus.CRITICAL
            message = "Cache access time is critically high"

        return CacheHealthMetric(
            name="avg_access_time",
            value=access_time,
            threshold_warning=thresholds["warning"],
            threshold_critical=thresholds["critical"],
            unit="milliseconds",
            status=status,
            message=message,
            timestamp=datetime.utcnow()
        )

    def _assess_memory_utilization(self, metrics: Dict[str, Any]) -> CacheHealthMetric:
        """Assess memory utilization health"""
        memory_util = metrics.get("memory_cache", {}).get("memory_utilization_percent", 0)
        thresholds = self.health_thresholds["memory_utilization"]

        if memory_util <= thresholds["warning"]:
            status = CacheHealthStatus.HEALTHY
            message = "Memory utilization is normal"
        elif memory_util <= thresholds["critical"]:
            status = CacheHealthStatus.WARNING
            message = "Memory utilization is high"
        else:
            status = CacheHealthStatus.CRITICAL
            message = "Memory utilization is critically high"

        return CacheHealthMetric(
            name="memory_utilization",
            value=memory_util,
            threshold_warning=thresholds["warning"],
            threshold_critical=thresholds["critical"],
            unit="percentage",
            status=status,
            message=message,
            timestamp=datetime.utcnow()
        )

    def _assess_eviction_rate(self, metrics: Dict[str, Any]) -> CacheHealthMetric:
        """Assess cache eviction rate health"""
        eviction_rate = metrics.get("derived_metrics", {}).get("eviction_rate_per_minute", 0)
        thresholds = self.health_thresholds["eviction_rate"]

        if eviction_rate <= thresholds["warning"]:
            status = CacheHealthStatus.HEALTHY
            message = "Cache eviction rate is normal"
        elif eviction_rate <= thresholds["critical"]:
            status = CacheHealthStatus.WARNING
            message = "Cache eviction rate is elevated"
        else:
            status = CacheHealthStatus.CRITICAL
            message = "Cache eviction rate is critically high"

        return CacheHealthMetric(
            name="eviction_rate",
            value=eviction_rate,
            threshold_warning=thresholds["warning"],
            threshold_critical=thresholds["critical"],
            unit="per_minute",
            status=status,
            message=message,
            timestamp=datetime.utcnow()
        )

    def _generate_recommendations(
        self,
        health_metrics: List[CacheHealthMetric],
        metrics: Dict[str, Any]
    ) -> List[CacheOptimizationRecommendation]:
        """Generate optimization recommendations based on health metrics"""
        recommendations = []

        # Check hit ratio
        hit_ratio_metric = next((m for m in health_metrics if m.name == "hit_ratio"), None)
        if hit_ratio_metric and hit_ratio_metric.status != CacheHealthStatus.HEALTHY:
            recommendations.append(CacheOptimizationRecommendation(
                type="hit_ratio_optimization",
                priority="high" if hit_ratio_metric.status == CacheHealthStatus.CRITICAL else "medium",
                description="Improve cache hit ratio by optimizing cache keys and TTL values",
                impact="Reduced database load and improved response times",
                implementation_effort="medium",
                estimated_improvement="10-30% performance improvement"
            ))

        # Check memory utilization
        memory_metric = next((m for m in health_metrics if m.name == "memory_utilization"), None)
        if memory_metric and memory_metric.status != CacheHealthStatus.HEALTHY:
            recommendations.append(CacheOptimizationRecommendation(
                type="memory_optimization",
                priority="high" if memory_metric.status == CacheHealthStatus.CRITICAL else "medium",
                description="Increase memory cache size or optimize eviction policies",
                impact="Reduced memory pressure and improved cache performance",
                implementation_effort="low",
                estimated_improvement="5-15% performance improvement"
            ))

        # Check access time
        access_time_metric = next((m for m in health_metrics if m.name == "avg_access_time"), None)
        if access_time_metric and access_time_metric.status != CacheHealthStatus.HEALTHY:
            recommendations.append(CacheOptimizationRecommendation(
                type="access_time_optimization",
                priority="medium",
                description="Optimize cache data structures and serialization",
                impact="Faster cache access and improved user experience",
                implementation_effort="medium",
                estimated_improvement="20-40% access time reduction"
            ))

        # Check eviction rate
        eviction_metric = next((m for m in health_metrics if m.name == "eviction_rate"), None)
        if eviction_metric and eviction_metric.status != CacheHealthStatus.HEALTHY:
            recommendations.append(CacheOptimizationRecommendation(
                type="eviction_optimization",
                priority="medium",
                description="Adjust cache size or implement smarter eviction policies",
                impact="Better cache retention and improved hit ratios",
                implementation_effort="medium",
                estimated_improvement="10-25% hit ratio improvement"
            ))

        return recommendations

    def _calculate_trend(self, metrics_list: List[Dict[str, Any]], metric_name: str) -> Dict[str, Any]:
        """Calculate trend for a specific metric"""
        try:
            if len(metrics_list) < 2:
                return {"trend": "insufficient_data"}

            values = [m.get(metric_name, 0) for m in metrics_list]

            # Calculate simple trend
            first_half = values[:len(values)//2]
            second_half = values[len(values)//2:]

            first_avg = sum(first_half) / len(first_half) if first_half else 0
            second_avg = sum(second_half) / len(second_half) if second_half else 0

            if second_avg > first_avg * 1.1:
                trend = "increasing"
            elif second_avg < first_avg * 0.9:
                trend = "decreasing"
            else:
                trend = "stable"

            change_percent = ((second_avg - first_avg) / first_avg * 100) if first_avg > 0 else 0

            return {
                "trend": trend,
                "change_percent": round(change_percent, 2),
                "first_period_avg": round(first_avg, 2),
                "second_period_avg": round(second_avg, 2),
                "data_points": len(values)
            }

        except Exception as e:
            logger.error(f"Error calculating trend for {metric_name}: {str(e)}")
            return {"trend": "error", "error": str(e)}

    async def _check_and_send_alerts(self, health_assessment: Dict[str, Any]) -> None:
        """Check for alert conditions and send alerts if needed"""
        try:
            current_time = datetime.utcnow()

            for metric in health_assessment.get("health_metrics", []):
                metric_name = metric["name"]
                status = metric["status"]

                # Check if we should alert
                if status in ["warning", "critical"]:
                    alert_key = f"{metric_name}_{status}"

                    # Check cooldown
                    if alert_key in self.active_alerts:
                        last_alert_time = self.active_alerts[alert_key]["timestamp"]
                        if (current_time - last_alert_time).total_seconds() < self.alert_cooldown:
                            continue  # Skip due to cooldown

                    # Send alert (would integrate with notification system)
                    logger.warning(
                        f"Cache health alert: {metric['message']}",
                        metric_name=metric_name,
                        status=status,
                        value=metric["value"],
                        threshold=metric["threshold_warning"] if status == "warning" else metric["threshold_critical"]
                    )

                    # Track alert
                    self.active_alerts[alert_key] = {
                        "timestamp": current_time,
                        "metric": metric
                    }

                else:
                    # Clear resolved alerts
                    alert_keys_to_remove = [
                        key for key in self.active_alerts.keys()
                        if key.startswith(f"{metric_name}_")
                    ]
                    for key in alert_keys_to_remove:
                        del self.active_alerts[key]

        except Exception as e:
            logger.error(f"Error checking alerts: {str(e)}")
