"""
🪐 Jupiter API Client

Jupiter aggregator client for price data fetching, token information retrieval,
rate limiting, and WebSocket integration following V2 architecture patterns.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from decimal import Decimal
import httpx
import websockets
import json
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS, ERROR_CONSTANTS
from ...shared.types import APIResponse, TokenData, TokenMetrics

logger = get_logger(__name__)


class JupiterClient:
    """
    🪐 Jupiter API Client for price data and token information
    
    Provides comprehensive Jupiter aggregator integration with:
    - Price data fetching
    - Token information retrieval
    - Rate limiting and error handling
    - WebSocket integration for real-time data
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        self.base_url = self.settings.jupiter_api_url
        
        # Rate limiting
        self.rate_limit_requests = 100  # requests per minute
        self.rate_limit_window = 60  # seconds
        self.request_timestamps = []
        
        # HTTP client with proper configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(
                connect=10.0,
                read=30.0,
                write=10.0,
                pool=30.0
            ),
            limits=httpx.Limits(
                max_keepalive_connections=10,
                max_connections=20
            ),
            headers={
                "Content-Type": "application/json",
                "User-Agent": f"TokenTracker-V2/{self.project_id}",
                "Accept": "application/json"
            }
        )
        
        # WebSocket connection
        self.ws_connection = None
        self.ws_subscriptions = set()
        
        logger.info(
            "Jupiter client initialized",
            project_id=self.project_id,
            base_url=self.base_url
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def close(self):
        """Close HTTP client and WebSocket connections"""
        try:
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
            
            await self.client.aclose()
            
            logger.info("Jupiter client closed successfully")
        except Exception as e:
            logger.error(
                "Error closing Jupiter client",
                error=str(e),
                error_type=type(e).__name__
            )
    
    def _check_rate_limit(self) -> bool:
        """Check if request is within rate limits"""
        now = time.time()
        
        # Remove old timestamps outside the window
        self.request_timestamps = [
            ts for ts in self.request_timestamps 
            if now - ts < self.rate_limit_window
        ]
        
        # Check if we're within limits
        if len(self.request_timestamps) >= self.rate_limit_requests:
            logger.warning(
                "Rate limit reached",
                current_requests=len(self.request_timestamps),
                limit=self.rate_limit_requests,
                window_seconds=self.rate_limit_window
            )
            return False
        
        # Add current timestamp
        self.request_timestamps.append(now)
        return True
    
    async def _wait_for_rate_limit(self):
        """Wait if rate limit is exceeded"""
        if not self._check_rate_limit():
            # Calculate wait time
            oldest_timestamp = min(self.request_timestamps)
            wait_time = self.rate_limit_window - (time.time() - oldest_timestamp)
            
            if wait_time > 0:
                logger.info(
                    "Waiting for rate limit reset",
                    wait_seconds=wait_time
                )
                await asyncio.sleep(wait_time)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=8),
        retry=retry_if_exception_type((httpx.RequestError, httpx.HTTPStatusError))
    )
    async def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make HTTP request with retry logic and rate limiting"""
        await self._wait_for_rate_limit()
        
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = await self.client.request(method, url, **kwargs)
            response.raise_for_status()
            
            logger.debug(
                "Jupiter API request successful",
                method=method,
                endpoint=endpoint,
                status_code=response.status_code
            )
            
            return response.json()
            
        except httpx.HTTPStatusError as e:
            logger.error(
                "HTTP error in Jupiter API request",
                status_code=e.response.status_code,
                url=url,
                method=method,
                response_text=e.response.text[:500] if e.response else None
            )
            raise
        except httpx.RequestError as e:
            logger.error(
                "Request error in Jupiter API",
                error=str(e),
                url=url,
                method=method
            )
            raise
    
    async def get_token_price(self, token_address: str, vs_token: str = "So11111111111111111111111111111111111111112") -> Optional[TokenData]:
        """
        💰 Get current token price from Jupiter
        
        Args:
            token_address: Token mint address
            vs_token: Base token for price (default: SOL)
            
        Returns:
            TokenData with current price information
        """
        try:
            logger.info(
                "Fetching token price from Jupiter",
                token_address=token_address,
                vs_token=vs_token
            )
            
            response = await self._make_request(
                "GET",
                f"price?ids={token_address}&vsToken={vs_token}"
            )
            
            if not response or "data" not in response:
                logger.warning(
                    "No price data returned from Jupiter",
                    token_address=token_address
                )
                return None
            
            price_data = response["data"].get(token_address)
            if not price_data:
                logger.warning(
                    "Token not found in Jupiter response",
                    token_address=token_address
                )
                return None
            
            # Convert to TokenData
            token_data = TokenData(
                address=token_address,
                symbol=price_data.get("symbol", "UNKNOWN"),
                name=price_data.get("name", "Unknown Token"),
                decimals=price_data.get("decimals", 9),  # Default to 9 for Solana tokens
                price=Decimal(str(price_data.get("price", 0))),
                market_cap=Decimal(str(price_data.get("marketCap", 0))) if price_data.get("marketCap") else None,
                volume_24h=Decimal(str(price_data.get("volume24h", 0))) if price_data.get("volume24h") else None,
                timestamp=datetime.utcnow()
            )
            
            logger.info(
                "Token price fetched successfully",
                token_address=token_address,
                symbol=token_data.symbol,
                price=str(token_data.price)
            )
            
            return token_data
            
        except Exception as e:
            logger.error(
                "Error fetching token price from Jupiter",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_token_info(self, token_address: str) -> Optional[Dict[str, Any]]:
        """
        📊 Get comprehensive token information from Jupiter
        
        Args:
            token_address: Token mint address
            
        Returns:
            Dictionary with token information
        """
        try:
            logger.info(
                "Fetching token info from Jupiter",
                token_address=token_address
            )
            
            response = await self._make_request(
                "GET",
                f"tokens/{token_address}"
            )
            
            if not response:
                logger.warning(
                    "No token info returned from Jupiter",
                    token_address=token_address
                )
                return None
            
            logger.info(
                "Token info fetched successfully",
                token_address=token_address,
                symbol=response.get("symbol", "UNKNOWN")
            )
            
            return response
            
        except Exception as e:
            logger.error(
                "Error fetching token info from Jupiter",
                token_address=token_address,
                error=str(e),
                error_type=type(e).__name__
            )
            return None
    
    async def get_quote(self, input_mint: str, output_mint: str, amount: int, slippage_bps: int = 50) -> Optional[Dict[str, Any]]:
        """
        💱 Get swap quote from Jupiter
        
        Args:
            input_mint: Input token mint address
            output_mint: Output token mint address
            amount: Amount in smallest unit
            slippage_bps: Slippage in basis points (default: 0.5%)
            
        Returns:
            Quote information
        """
        try:
            logger.info(
                "Getting swap quote from Jupiter",
                input_mint=input_mint,
                output_mint=output_mint,
                amount=amount,
                slippage_bps=slippage_bps
            )
            
            params = {
                "inputMint": input_mint,
                "outputMint": output_mint,
                "amount": amount,
                "slippageBps": slippage_bps
            }
            
            response = await self._make_request(
                "GET",
                "quote",
                params=params
            )
            
            if not response:
                logger.warning(
                    "No quote returned from Jupiter",
                    input_mint=input_mint,
                    output_mint=output_mint
                )
                return None
            
            logger.info(
                "Quote fetched successfully",
                input_mint=input_mint,
                output_mint=output_mint,
                out_amount=response.get("outAmount")
            )
            
            return response
            
        except Exception as e:
            logger.error(
                "Error getting quote from Jupiter",
                input_mint=input_mint,
                output_mint=output_mint,
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def get_multiple_prices(self, token_addresses: List[str], vs_token: str = "So11111111111111111111111111111111111111112") -> Dict[str, Optional[TokenData]]:
        """
        💰 Get prices for multiple tokens from Jupiter

        Args:
            token_addresses: List of token mint addresses
            vs_token: Base token for price (default: SOL)

        Returns:
            Dictionary mapping token addresses to TokenData
        """
        try:
            if not token_addresses:
                return {}

            logger.info(
                "Fetching multiple token prices from Jupiter",
                token_count=len(token_addresses),
                vs_token=vs_token
            )

            # Jupiter supports multiple IDs in a single request
            ids_param = ",".join(token_addresses)

            response = await self._make_request(
                "GET",
                f"price?ids={ids_param}&vsToken={vs_token}"
            )

            if not response or "data" not in response:
                logger.warning("No price data returned from Jupiter for multiple tokens")
                return {addr: None for addr in token_addresses}

            results = {}
            for token_address in token_addresses:
                price_data = response["data"].get(token_address)

                if price_data:
                    token_data = TokenData(
                        address=token_address,
                        symbol=price_data.get("symbol", "UNKNOWN"),
                        name=price_data.get("name", "Unknown Token"),
                        price=Decimal(str(price_data.get("price", 0))),
                        market_cap=Decimal(str(price_data.get("marketCap", 0))) if price_data.get("marketCap") else None,
                        volume_24h=Decimal(str(price_data.get("volume24h", 0))) if price_data.get("volume24h") else None,
                        timestamp=datetime.utcnow()
                    )
                    results[token_address] = token_data
                else:
                    results[token_address] = None

            logger.info(
                "Multiple token prices fetched successfully",
                requested_count=len(token_addresses),
                successful_count=len([v for v in results.values() if v is not None])
            )

            return results

        except Exception as e:
            logger.error(
                "Error fetching multiple token prices from Jupiter",
                token_count=len(token_addresses),
                error=str(e),
                error_type=type(e).__name__
            )
            return {addr: None for addr in token_addresses}

    async def get_token_list(self) -> Optional[List[Dict[str, Any]]]:
        """
        📋 Get list of all tokens available on Jupiter

        Returns:
            List of token information dictionaries
        """
        try:
            logger.info("Fetching token list from Jupiter")

            response = await self._make_request("GET", "tokens")

            if not response:
                logger.warning("No token list returned from Jupiter")
                return None

            # Response should be a list of tokens
            if isinstance(response, list):
                logger.info(
                    "Token list fetched successfully",
                    token_count=len(response)
                )
                return response
            else:
                logger.warning(
                    "Unexpected token list format from Jupiter",
                    response_type=type(response).__name__
                )
                return None

        except Exception as e:
            logger.error(
                "Error fetching token list from Jupiter",
                error=str(e),
                error_type=type(e).__name__
            )
            return None

    async def start_price_stream(self, token_addresses: List[str], callback=None) -> bool:
        """
        🔄 Start real-time price streaming via WebSocket

        Args:
            token_addresses: List of token addresses to monitor
            callback: Optional callback function for price updates

        Returns:
            True if stream started successfully
        """
        try:
            logger.info(
                "Starting Jupiter price stream",
                token_count=len(token_addresses)
            )

            # Note: Jupiter doesn't have a public WebSocket API yet
            # This is a placeholder for when they implement it
            # For now, we'll use polling as fallback

            logger.warning(
                "Jupiter WebSocket not available, using polling fallback",
                token_addresses=token_addresses
            )

            # Start polling task
            asyncio.create_task(
                self._poll_prices(token_addresses, callback)
            )

            return True

        except Exception as e:
            logger.error(
                "Error starting Jupiter price stream",
                error=str(e),
                error_type=type(e).__name__
            )
            return False

    async def _poll_prices(self, token_addresses: List[str], callback=None, interval: int = 10):
        """
        🔄 Poll prices at regular intervals (fallback for WebSocket)

        Args:
            token_addresses: List of token addresses to monitor
            callback: Optional callback function for price updates
            interval: Polling interval in seconds
        """
        logger.info(
            "Starting price polling",
            token_count=len(token_addresses),
            interval_seconds=interval
        )

        while True:
            try:
                prices = await self.get_multiple_prices(token_addresses)

                if callback and prices:
                    for token_address, token_data in prices.items():
                        if token_data:
                            await callback(token_address, token_data)

                await asyncio.sleep(interval)

            except asyncio.CancelledError:
                logger.info("Price polling cancelled")
                break
            except Exception as e:
                logger.error(
                    "Error in price polling",
                    error=str(e),
                    error_type=type(e).__name__
                )
                await asyncio.sleep(interval)

    async def stop_price_stream(self):
        """
        ⏹️ Stop real-time price streaming
        """
        try:
            if self.ws_connection:
                await self.ws_connection.close()
                self.ws_connection = None
                self.ws_subscriptions.clear()

                logger.info("Jupiter price stream stopped")

        except Exception as e:
            logger.error(
                "Error stopping Jupiter price stream",
                error=str(e),
                error_type=type(e).__name__
            )

    async def health_check(self) -> bool:
        """
        🏥 Check Jupiter API health

        Returns:
            True if API is healthy
        """
        try:
            # Try to fetch a simple endpoint
            response = await self._make_request("GET", "tokens", timeout=5)

            if response:
                logger.info("Jupiter API health check passed")
                return True
            else:
                logger.warning("Jupiter API health check failed - no response")
                return False

        except Exception as e:
            logger.error(
                "Jupiter API health check failed",
                error=str(e),
                error_type=type(e).__name__
            )
            return False
