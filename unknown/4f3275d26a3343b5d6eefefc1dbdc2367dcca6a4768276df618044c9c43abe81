"""
🤖 Trading Automation Module

Live trading automation system with order management, risk controls,
DEX integration, and execution engine following V2 architecture patterns.
"""

from .order_manager import OrderManager
from .risk_manager import RiskManager
from .execution_engine import ExecutionEngine
from .trading_bot import TradingBot
from .dex_integrator import DEXIntegrator
from .routes import router

__all__ = [
    "OrderManager",
    "RiskManager",
    "ExecutionEngine", 
    "TradingBot",
    "DEXIntegrator",
    "router"
]
