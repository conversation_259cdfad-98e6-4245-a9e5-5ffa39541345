"""
⚡ Execution Engine

Smart order execution engine with routing, gas optimization,
transaction batching, and MEV protection.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from uuid import uuid4

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, OrderStatus, TradeStatus
from ...database.models import Order, Trade, Portfolio
from ...database.models.order import OrderPriority
from .order_manager import OrderManager
from .risk_manager import RiskManager
from .dex_integrator import DEXIntegrator, SwapRoute

logger = get_logger(__name__)


class ExecutionStrategy(str, Enum):
    """Order execution strategies"""
    IMMEDIATE = "immediate"
    TWAP = "twap"  # Time-Weighted Average Price
    VWAP = "vwap"  # Volume-Weighted Average Price
    ICEBERG = "iceberg"
    SMART_ROUTING = "smart_routing"


class ExecutionStatus(str, Enum):
    """Execution status"""
    PENDING = "pending"
    EXECUTING = "executing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExecutionEngine:
    """
    ⚡ Execution Engine
    
    Handles smart order execution with:
    - Smart order routing across DEXs
    - Gas optimization and timing
    - Transaction batching
    - MEV protection strategies
    - Execution algorithms (TWAP, VWAP, etc.)
    - Real-time execution monitoring
    """
    
    def __init__(self):
        self.logger = logger
        self.order_manager = OrderManager()
        self.risk_manager = RiskManager()
        self.dex_integrator = DEXIntegrator()
        
        # Execution parameters
        self.max_gas_price = 50  # Maximum gas price in gwei
        self.batch_size = 5      # Maximum orders per batch
        self.execution_timeout = 300  # 5 minutes timeout
        self.mev_protection_delay = (1, 3)  # Random delay in seconds
        
        # Active executions tracking
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        self.execution_queue: List[str] = []
        self.batch_queue: List[List[str]] = []
        
        # Execution monitoring
        self.execution_monitor_task = None
        self.batch_processor_task = None
    
    async def start_execution_engine(self):
        """Start the execution engine background tasks"""
        try:
            self.logger.info("Starting execution engine")
            
            # Start execution monitor
            self.execution_monitor_task = asyncio.create_task(self._execution_monitor())
            
            # Start batch processor
            self.batch_processor_task = asyncio.create_task(self._batch_processor())
            
            self.logger.info("Execution engine started successfully")
            
        except Exception as e:
            self.logger.error(f"Error starting execution engine: {str(e)}")
    
    async def stop_execution_engine(self):
        """Stop the execution engine background tasks"""
        try:
            self.logger.info("Stopping execution engine")
            
            if self.execution_monitor_task:
                self.execution_monitor_task.cancel()
            
            if self.batch_processor_task:
                self.batch_processor_task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(
                self.execution_monitor_task,
                self.batch_processor_task,
                return_exceptions=True
            )
            
            self.logger.info("Execution engine stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping execution engine: {str(e)}")
    
    async def execute_order(
        self,
        order_id: str,
        strategy: ExecutionStrategy = ExecutionStrategy.SMART_ROUTING,
        max_slippage: Optional[Decimal] = None,
        time_limit: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Execute an order using the specified strategy
        
        Args:
            order_id: Order ID to execute
            strategy: Execution strategy
            max_slippage: Maximum allowed slippage
            time_limit: Time limit in seconds
            
        Returns:
            Execution result
        """
        try:
            self.logger.info(f"Executing order {order_id} with strategy {strategy}")
            
            # Get order
            order = await Order.get(order_id)
            if not order:
                return {"success": False, "error": "Order not found"}
            
            # Check if order can be executed
            if not order.is_active:
                return {"success": False, "error": f"Order not active: {order.status}"}
            
            # Perform risk assessment
            risk_assessment = await self.risk_manager.assess_trade_risk(
                order.portfolio_id,
                order.token_address,
                order.side,
                order.quantity,
                order.price
            )
            
            if not risk_assessment["allowed"]:
                return {
                    "success": False,
                    "error": f"Risk assessment failed: {risk_assessment['reason']}"
                }
            
            # Create execution context
            execution_id = str(uuid4())
            execution_context = {
                "execution_id": execution_id,
                "order_id": order_id,
                "order": order,
                "strategy": strategy,
                "max_slippage": max_slippage or Decimal("0.01"),
                "time_limit": time_limit or self.execution_timeout,
                "status": ExecutionStatus.PENDING,
                "created_at": datetime.utcnow(),
                "risk_assessment": risk_assessment
            }
            
            # Add to active executions
            self.active_executions[execution_id] = execution_context
            
            # Execute based on strategy
            if strategy == ExecutionStrategy.IMMEDIATE:
                result = await self._execute_immediate(execution_context)
            elif strategy == ExecutionStrategy.SMART_ROUTING:
                result = await self._execute_smart_routing(execution_context)
            elif strategy == ExecutionStrategy.TWAP:
                result = await self._execute_twap(execution_context)
            elif strategy == ExecutionStrategy.ICEBERG:
                result = await self._execute_iceberg(execution_context)
            else:
                result = {"success": False, "error": f"Unsupported strategy: {strategy}"}
            
            # Update execution status
            execution_context["status"] = ExecutionStatus.COMPLETED if result["success"] else ExecutionStatus.FAILED
            execution_context["completed_at"] = datetime.utcnow()
            execution_context["result"] = result
            
            # Remove from active executions
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing order {order_id}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def batch_execute_orders(
        self,
        order_ids: List[str],
        strategy: ExecutionStrategy = ExecutionStrategy.SMART_ROUTING
    ) -> Dict[str, Any]:
        """
        Execute multiple orders as a batch
        
        Args:
            order_ids: List of order IDs
            strategy: Execution strategy
            
        Returns:
            Batch execution results
        """
        try:
            self.logger.info(f"Batch executing {len(order_ids)} orders")
            
            # Validate batch size
            if len(order_ids) > self.batch_size:
                return {
                    "success": False,
                    "error": f"Batch size {len(order_ids)} exceeds maximum {self.batch_size}"
                }
            
            # Execute orders concurrently
            execution_tasks = [
                self.execute_order(order_id, strategy)
                for order_id in order_ids
            ]
            
            results = await asyncio.gather(*execution_tasks, return_exceptions=True)
            
            # Process results
            successful_executions = 0
            failed_executions = 0
            execution_results = {}
            
            for i, result in enumerate(results):
                order_id = order_ids[i]
                if isinstance(result, Exception):
                    execution_results[order_id] = {"success": False, "error": str(result)}
                    failed_executions += 1
                else:
                    execution_results[order_id] = result
                    if result.get("success"):
                        successful_executions += 1
                    else:
                        failed_executions += 1
            
            return {
                "success": successful_executions > 0,
                "total_orders": len(order_ids),
                "successful_executions": successful_executions,
                "failed_executions": failed_executions,
                "results": execution_results
            }
            
        except Exception as e:
            self.logger.error(f"Error batch executing orders: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def optimize_gas_timing(self, order: Order) -> Dict[str, Any]:
        """
        Optimize gas price and timing for order execution
        
        Args:
            order: Order to optimize
            
        Returns:
            Gas optimization recommendations
        """
        try:
            # Get current gas prices (simplified)
            current_gas_price = await self._get_current_gas_price()
            
            # Determine optimal gas price based on order priority
            if order.priority == OrderPriority.URGENT:
                optimal_gas_price = min(current_gas_price * Decimal("1.5"), self.max_gas_price)
                recommended_delay = 0
            elif order.priority == OrderPriority.HIGH:
                optimal_gas_price = min(current_gas_price * Decimal("1.2"), self.max_gas_price)
                recommended_delay = 5
            elif order.priority == OrderPriority.NORMAL:
                optimal_gas_price = current_gas_price
                recommended_delay = 15
            else:  # LOW priority
                optimal_gas_price = current_gas_price * Decimal("0.8")
                recommended_delay = 30
            
            # Check if we should wait for better gas prices
            should_wait = (
                current_gas_price > self.max_gas_price * 0.8 and
                order.priority in [OrderPriority.LOW, OrderPriority.NORMAL]
            )
            
            return {
                "current_gas_price": current_gas_price,
                "optimal_gas_price": optimal_gas_price,
                "recommended_delay": recommended_delay,
                "should_wait": should_wait,
                "estimated_cost": optimal_gas_price * 100000  # Estimated gas units
            }
            
        except Exception as e:
            self.logger.error(f"Error optimizing gas timing: {str(e)}")
            return {
                "current_gas_price": 20,
                "optimal_gas_price": 25,
                "recommended_delay": 0,
                "should_wait": False,
                "estimated_cost": 2500000
            }
    
    async def _execute_immediate(self, execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute order immediately at market price"""
        try:
            order = execution_context["order"]
            
            # Find best route
            route = await self.dex_integrator.find_best_route(
                "USDC",  # Assuming USDC as base currency
                order.token_address,
                order.quantity,
                order.side
            )
            
            if not route:
                return {"success": False, "error": "No suitable route found"}
            
            # Apply MEV protection delay
            await self._apply_mev_protection()
            
            # Execute swap
            trade = await self.dex_integrator.execute_swap(
                route,
                order,
                execution_context["max_slippage"]
            )
            
            if trade:
                # Update order status
                await order.update_fill(
                    trade.quantity,
                    trade.price,
                    trade.fees_usd
                )
                
                return {
                    "success": True,
                    "trade_id": str(trade.id),
                    "executed_quantity": trade.quantity,
                    "executed_price": trade.price,
                    "fees": trade.fees_usd,
                    "slippage": trade.slippage,
                    "execution_venue": trade.execution_venue
                }
            else:
                return {"success": False, "error": "Trade execution failed"}
            
        except Exception as e:
            self.logger.error(f"Error in immediate execution: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _execute_smart_routing(self, execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute order using smart routing across multiple DEXs"""
        try:
            order = execution_context["order"]

            # Find best route across all DEXs
            route = await self.dex_integrator.find_best_route(
                "USDC",
                order.token_address,
                order.quantity,
                order.side
            )

            if not route:
                return {"success": False, "error": "No suitable route found"}

            # Optimize gas timing
            gas_optimization = await self.optimize_gas_timing(order)

            # Wait for optimal timing if recommended
            if gas_optimization["should_wait"] and gas_optimization["recommended_delay"] > 0:
                await asyncio.sleep(gas_optimization["recommended_delay"])

            # Apply MEV protection
            await self._apply_mev_protection()

            # Execute with optimized parameters
            trade = await self.dex_integrator.execute_swap(
                route,
                order,
                execution_context["max_slippage"]
            )

            if trade:
                await order.update_fill(trade.quantity, trade.price, trade.fees_usd)
                return {
                    "success": True,
                    "trade_id": str(trade.id),
                    "executed_quantity": trade.quantity,
                    "executed_price": trade.price,
                    "fees": trade.fees_usd,
                    "slippage": trade.slippage,
                    "execution_venue": trade.execution_venue,
                    "gas_optimization": gas_optimization
                }
            else:
                return {"success": False, "error": "Trade execution failed"}

        except Exception as e:
            self.logger.error(f"Error in smart routing execution: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _execute_twap(self, execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute order using Time-Weighted Average Price strategy"""
        try:
            order = execution_context["order"]
            time_limit = execution_context["time_limit"]

            # Split order into smaller chunks
            num_chunks = min(10, int(time_limit / 30))  # Execute every 30 seconds
            chunk_size = order.quantity / num_chunks
            interval = time_limit / num_chunks

            executed_trades = []
            total_executed = Decimal("0")

            for i in range(num_chunks):
                # Check if order is still active
                order = await Order.get(str(order.id))
                if not order or not order.is_active:
                    break

                # Execute chunk
                chunk_result = await self._execute_chunk(
                    order,
                    chunk_size,
                    execution_context["max_slippage"]
                )

                if chunk_result["success"]:
                    executed_trades.append(chunk_result)
                    total_executed += chunk_result["executed_quantity"]

                # Wait for next interval (except last iteration)
                if i < num_chunks - 1:
                    await asyncio.sleep(interval)

            return {
                "success": len(executed_trades) > 0,
                "strategy": "TWAP",
                "total_executed": total_executed,
                "num_executions": len(executed_trades),
                "executions": executed_trades
            }

        except Exception as e:
            self.logger.error(f"Error in TWAP execution: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _execute_iceberg(self, execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute order using Iceberg strategy (hide order size)"""
        try:
            order = execution_context["order"]

            # Split into smaller visible chunks
            visible_size = order.quantity * Decimal("0.1")  # Show only 10% at a time
            num_chunks = int(order.quantity / visible_size)

            executed_trades = []
            total_executed = Decimal("0")

            for i in range(num_chunks):
                # Check if order is still active
                order = await Order.get(str(order.id))
                if not order or not order.is_active:
                    break

                # Execute visible chunk
                chunk_result = await self._execute_chunk(
                    order,
                    visible_size,
                    execution_context["max_slippage"]
                )

                if chunk_result["success"]:
                    executed_trades.append(chunk_result)
                    total_executed += chunk_result["executed_quantity"]

                # Random delay between chunks for stealth
                await asyncio.sleep(random.uniform(5, 15))

            return {
                "success": len(executed_trades) > 0,
                "strategy": "ICEBERG",
                "total_executed": total_executed,
                "num_executions": len(executed_trades),
                "executions": executed_trades
            }

        except Exception as e:
            self.logger.error(f"Error in Iceberg execution: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _execute_chunk(
        self,
        order: Order,
        chunk_size: Decimal,
        max_slippage: Decimal
    ) -> Dict[str, Any]:
        """Execute a chunk of an order"""
        try:
            # Find best route for chunk
            route = await self.dex_integrator.find_best_route(
                "USDC",
                order.token_address,
                chunk_size,
                order.side
            )

            if not route:
                return {"success": False, "error": "No route found for chunk"}

            # Apply MEV protection
            await self._apply_mev_protection()

            # Execute chunk
            trade = await self.dex_integrator.execute_swap(route, order, max_slippage)

            if trade:
                return {
                    "success": True,
                    "trade_id": str(trade.id),
                    "executed_quantity": trade.quantity,
                    "executed_price": trade.price,
                    "fees": trade.fees_usd,
                    "execution_venue": trade.execution_venue
                }
            else:
                return {"success": False, "error": "Chunk execution failed"}

        except Exception as e:
            self.logger.error(f"Error executing chunk: {str(e)}")
            return {"success": False, "error": str(e)}

    async def _apply_mev_protection(self):
        """Apply MEV protection delay"""
        try:
            import random
            delay = random.uniform(*self.mev_protection_delay)
            await asyncio.sleep(delay)
        except Exception as e:
            self.logger.error(f"Error applying MEV protection: {str(e)}")

    async def _get_current_gas_price(self) -> Decimal:
        """Get current gas price from network"""
        try:
            # This would integrate with Solana RPC to get current fees
            # For now, return simulated gas price
            import random
            return Decimal(str(random.uniform(15, 35)))
        except Exception as e:
            self.logger.error(f"Error getting gas price: {str(e)}")
            return Decimal("25")

    async def _execution_monitor(self):
        """Monitor active executions for timeouts and issues"""
        try:
            while True:
                current_time = datetime.utcnow()
                expired_executions = []

                for execution_id, context in self.active_executions.items():
                    # Check for timeout
                    elapsed = (current_time - context["created_at"]).total_seconds()
                    if elapsed > context["time_limit"]:
                        expired_executions.append(execution_id)

                # Handle expired executions
                for execution_id in expired_executions:
                    context = self.active_executions[execution_id]
                    self.logger.warning(f"Execution {execution_id} timed out")
                    context["status"] = ExecutionStatus.FAILED
                    context["result"] = {"success": False, "error": "Execution timeout"}
                    del self.active_executions[execution_id]

                await asyncio.sleep(10)  # Check every 10 seconds

        except asyncio.CancelledError:
            self.logger.info("Execution monitor cancelled")
        except Exception as e:
            self.logger.error(f"Error in execution monitor: {str(e)}")

    async def _batch_processor(self):
        """Process batched orders for efficiency"""
        try:
            while True:
                if self.batch_queue:
                    batch = self.batch_queue.pop(0)
                    await self.batch_execute_orders(batch)

                await asyncio.sleep(5)  # Process batches every 5 seconds

        except asyncio.CancelledError:
            self.logger.info("Batch processor cancelled")
        except Exception as e:
            self.logger.error(f"Error in batch processor: {str(e)}")
