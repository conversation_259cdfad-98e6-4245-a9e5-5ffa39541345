"""
🚀 API Optimization Module - Phase 3 Production Optimization

Advanced API optimization features for production-grade performance including
request batching, load balancing, response compression, and intelligent routing.
"""

from .request_batcher import RequestBatcher, BatchStrategy, BatchRequest, BatchGroup
from .load_balancer import LoadBalancer, LoadBalancingStrategy, BackendServer, BackendStatus
from .optimization_middleware import APIOptimizationMiddleware, get_optimization_middleware

__all__ = [
    # Request Batching
    "RequestBatcher",
    "BatchStrategy", 
    "BatchRequest",
    "BatchGroup",
    
    # Load Balancing
    "LoadBalancer",
    "LoadBalancingStrategy",
    "BackendServer", 
    "BackendStatus",
    
    # Optimization Middleware
    "APIOptimizationMiddleware",
    "get_optimization_middleware"
]
