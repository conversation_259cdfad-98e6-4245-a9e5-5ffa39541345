apiVersion: v1
kind: Namespace
metadata:
  name: tokentracker-production
  labels:
    name: tokentracker-production
    environment: production
    app: tokentracker-v2
  annotations:
    description: "TokenTracker V2 Production Environment"
    contact: "<EMAIL>"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: tokentracker-production-quota
  namespace: tokentracker-production
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: tokentracker-production-limits
  namespace: tokentracker-production
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
