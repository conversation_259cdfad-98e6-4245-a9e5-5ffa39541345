"""
🚀 API Optimization Middleware - Phase 3 Production Optimization

Comprehensive API optimization middleware that integrates request batching,
load balancing, response compression, and performance monitoring.
"""

import asyncio
import time
import gzip
import json
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from fastapi import Request, Response
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from .request_batcher import RequestBatcher, BatchStrategy
from .load_balancer import Load<PERSON>alancer, LoadBalancingStrategy

logger = get_logger(__name__)


class APIOptimizationMiddleware(BaseHTTPMiddleware):
    """
    🚀 API Optimization Middleware
    
    Features:
    - Request batching for similar requests
    - Load balancing across backend services
    - Response compression
    - Request deduplication
    - Performance monitoring
    - Adaptive optimization
    """
    
    def __init__(self, app, enable_batching: bool = True, enable_load_balancing: bool = True):
        super().__init__(app)
        self.settings = get_settings()
        
        # Feature flags
        self.enable_batching = enable_batching
        self.enable_load_balancing = enable_load_balancing
        self.enable_compression = True
        self.enable_deduplication = True
        
        # Initialize components
        self.request_batcher: Optional[RequestBatcher] = None
        self.load_balancer: Optional[LoadBalancer] = None
        
        # Request deduplication cache
        self.dedup_cache: Dict[str, Dict[str, Any]] = {}
        self.dedup_ttl = 60.0  # seconds
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "batched_requests": 0,
            "load_balanced_requests": 0,
            "compressed_responses": 0,
            "deduplicated_requests": 0,
            "avg_response_time": 0.0,
            "optimization_savings_ms": 0.0
        }
        
        # Compression settings
        self.compression_threshold = 1024  # Compress responses > 1KB
        self.compression_level = 6  # Balance between speed and compression ratio
        
        logger.info(
            "API optimization middleware initialized",
            batching_enabled=enable_batching,
            load_balancing_enabled=enable_load_balancing
        )
    
    async def startup(self) -> None:
        """Initialize optimization components"""
        try:
            # Initialize request batcher
            if self.enable_batching:
                self.request_batcher = RequestBatcher()
                await self.request_batcher.start()
                logger.info("Request batcher started")
            
            # Initialize load balancer
            if self.enable_load_balancing:
                self.load_balancer = LoadBalancer(LoadBalancingStrategy.ADAPTIVE)
                
                # Add backend servers (would be configured from settings)
                self.load_balancer.add_backend("backend1", "localhost", 8001, weight=1.0)
                self.load_balancer.add_backend("backend2", "localhost", 8002, weight=1.0)
                
                await self.load_balancer.start()
                logger.info("Load balancer started")
            
        except Exception as e:
            logger.error(f"Error starting optimization middleware: {str(e)}")
    
    async def shutdown(self) -> None:
        """Cleanup optimization components"""
        try:
            if self.request_batcher:
                await self.request_batcher.stop()
                logger.info("Request batcher stopped")
            
            if self.load_balancer:
                await self.load_balancer.stop()
                logger.info("Load balancer stopped")
                
        except Exception as e:
            logger.error(f"Error stopping optimization middleware: {str(e)}")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Main middleware dispatch logic"""
        start_time = time.time()
        
        try:
            # Update request count
            self.stats["total_requests"] += 1
            
            # Check for request deduplication
            if self.enable_deduplication:
                dedup_result = await self._check_deduplication(request)
                if dedup_result:
                    self.stats["deduplicated_requests"] += 1
                    response = JSONResponse(content=dedup_result)
                    return await self._optimize_response(response, start_time)
            
            # Check if request should be batched
            if self.enable_batching and await self._should_batch_request(request):
                response = await self._handle_batched_request(request)
                if response:
                    self.stats["batched_requests"] += 1
                    return await self._optimize_response(response, start_time)
            
            # Handle load balancing
            if self.enable_load_balancing and await self._should_load_balance(request):
                response = await self._handle_load_balanced_request(request, call_next)
                if response:
                    self.stats["load_balanced_requests"] += 1
                    return await self._optimize_response(response, start_time)
            
            # Default processing
            response = await call_next(request)
            return await self._optimize_response(response, start_time)
            
        except Exception as e:
            logger.error(f"Error in optimization middleware: {str(e)}")
            # Fallback to default processing
            response = await call_next(request)
            return await self._optimize_response(response, start_time)
    
    async def _check_deduplication(self, request: Request) -> Optional[Dict[str, Any]]:
        """Check if request can be deduplicated"""
        try:
            # Generate request signature
            request_signature = await self._generate_request_signature(request)
            
            # Check cache
            if request_signature in self.dedup_cache:
                cached_entry = self.dedup_cache[request_signature]
                
                # Check if cache entry is still valid
                cache_age = time.time() - cached_entry["timestamp"]
                if cache_age < self.dedup_ttl:
                    logger.debug(
                        "Request deduplicated",
                        signature=request_signature,
                        cache_age=f"{cache_age:.2f}s"
                    )
                    return cached_entry["response"]
                else:
                    # Remove expired entry
                    del self.dedup_cache[request_signature]
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking deduplication: {str(e)}")
            return None
    
    async def _should_batch_request(self, request: Request) -> bool:
        """Determine if request should be batched"""
        try:
            # Check if request batcher is available
            if not self.request_batcher:
                return False
            
            # Check request path for batchable endpoints
            batchable_paths = [
                "/api/tokens/price",
                "/api/tokens/info",
                "/api/signals/generate",
                "/api/portfolio/update"
            ]
            
            return any(request.url.path.startswith(path) for path in batchable_paths)
            
        except Exception as e:
            logger.error(f"Error checking batch eligibility: {str(e)}")
            return False
    
    async def _should_load_balance(self, request: Request) -> bool:
        """Determine if request should be load balanced"""
        try:
            # Check if load balancer is available
            if not self.load_balancer:
                return False
            
            # Check for load balanceable endpoints
            load_balanceable_paths = [
                "/api/analytics/",
                "/api/ml/",
                "/api/compute/"
            ]
            
            return any(request.url.path.startswith(path) for path in load_balanceable_paths)
            
        except Exception as e:
            logger.error(f"Error checking load balance eligibility: {str(e)}")
            return False
    
    async def _handle_batched_request(self, request: Request) -> Optional[Response]:
        """Handle request through batching system"""
        try:
            # Extract request parameters
            request_params = await self._extract_request_params(request)
            
            # Create response future
            response_future = asyncio.Future()
            
            async def batch_callback(result: Any, error: Optional[str] = None):
                """Callback for batch processing result"""
                if error:
                    response_future.set_exception(Exception(error))
                else:
                    response_future.set_result(result)
            
            # Add request to batch
            request_id = await self.request_batcher.add_request(
                endpoint=str(request.url.path),
                params=request_params,
                callback=batch_callback,
                priority=self._get_request_priority(request)
            )
            
            # Wait for batch processing
            try:
                result = await asyncio.wait_for(response_future, timeout=30.0)
                return JSONResponse(content=result)
            except asyncio.TimeoutError:
                logger.warning(f"Batch request timeout for {request_id}")
                return None
                
        except Exception as e:
            logger.error(f"Error handling batched request: {str(e)}")
            return None
    
    async def _handle_load_balanced_request(
        self,
        request: Request,
        call_next: Callable
    ) -> Optional[Response]:
        """Handle request through load balancing"""
        try:
            # Get request data for routing
            request_data = {
                "path": str(request.url.path),
                "method": request.method,
                "headers": dict(request.headers),
                "client_ip": request.client.host if request.client else None
            }
            
            # Route request to backend
            backend = await self.load_balancer.route_request(request_data)
            if not backend:
                logger.warning("No available backend for load balancing")
                return None
            
            # Process request (in real implementation, this would forward to backend)
            request_start = time.time()
            response = await call_next(request)
            request_time = time.time() - request_start
            
            # Update backend metrics
            success = 200 <= response.status_code < 400
            await self.load_balancer.complete_request(backend, request_time, success)
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling load balanced request: {str(e)}")
            return None
    
    async def _optimize_response(self, response: Response, start_time: float) -> Response:
        """Optimize response with compression and caching"""
        try:
            # Calculate response time
            response_time = time.time() - start_time
            
            # Update average response time
            total_requests = self.stats["total_requests"]
            self.stats["avg_response_time"] = (
                (self.stats["avg_response_time"] * (total_requests - 1) + response_time) / total_requests
            )
            
            # Apply response compression
            if self.enable_compression:
                response = await self._compress_response(response)
            
            # Add performance headers
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"
            response.headers["X-Optimization-Stats"] = json.dumps({
                "batched": self.stats["batched_requests"],
                "load_balanced": self.stats["load_balanced_requests"],
                "compressed": self.stats["compressed_responses"],
                "deduplicated": self.stats["deduplicated_requests"]
            })
            
            return response
            
        except Exception as e:
            logger.error(f"Error optimizing response: {str(e)}")
            return response
    
    async def _compress_response(self, response: Response) -> Response:
        """Compress response if beneficial"""
        try:
            # Check if response should be compressed
            if not hasattr(response, 'body') or not response.body:
                return response
            
            # Check content type
            content_type = response.headers.get("content-type", "")
            compressible_types = [
                "application/json",
                "text/html",
                "text/plain",
                "text/css",
                "application/javascript"
            ]
            
            if not any(ct in content_type for ct in compressible_types):
                return response
            
            # Check size threshold
            if len(response.body) < self.compression_threshold:
                return response
            
            # Compress response body
            compressed_body = gzip.compress(response.body, compresslevel=self.compression_level)
            
            # Check if compression is beneficial
            compression_ratio = len(compressed_body) / len(response.body)
            if compression_ratio > 0.9:  # Less than 10% savings
                return response
            
            # Update response
            response.body = compressed_body
            response.headers["Content-Encoding"] = "gzip"
            response.headers["Content-Length"] = str(len(compressed_body))
            
            self.stats["compressed_responses"] += 1
            
            logger.debug(
                "Response compressed",
                original_size=len(response.body),
                compressed_size=len(compressed_body),
                compression_ratio=f"{compression_ratio:.2f}"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Error compressing response: {str(e)}")
            return response

    async def _generate_request_signature(self, request: Request) -> str:
        """Generate unique signature for request deduplication"""
        try:
            # Include path, method, and relevant parameters
            signature_parts = [
                request.method,
                str(request.url.path),
                str(request.url.query)
            ]

            # Include relevant headers
            relevant_headers = ["authorization", "user-agent", "accept"]
            for header in relevant_headers:
                if header in request.headers:
                    signature_parts.append(f"{header}:{request.headers[header]}")

            # Create hash
            import hashlib
            signature_content = "|".join(signature_parts)
            return hashlib.md5(signature_content.encode()).hexdigest()

        except Exception as e:
            logger.error(f"Error generating request signature: {str(e)}")
            return f"fallback_{time.time()}"

    async def _extract_request_params(self, request: Request) -> Dict[str, Any]:
        """Extract parameters from request for batching"""
        try:
            params = {}

            # Query parameters
            params.update(dict(request.query_params))

            # Path parameters
            if hasattr(request, 'path_params'):
                params.update(request.path_params)

            # Body parameters (for POST/PUT requests)
            if request.method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        content_type = request.headers.get("content-type", "")
                        if "application/json" in content_type:
                            body_params = json.loads(body.decode())
                            if isinstance(body_params, dict):
                                params.update(body_params)
                except Exception as e:
                    logger.debug(f"Could not parse request body: {str(e)}")

            return params

        except Exception as e:
            logger.error(f"Error extracting request params: {str(e)}")
            return {}

    def _get_request_priority(self, request: Request) -> int:
        """Determine request priority for batching"""
        try:
            # Check for priority header
            priority_header = request.headers.get("x-priority", "").lower()
            if priority_header == "high":
                return 3
            elif priority_header == "medium":
                return 2
            elif priority_header == "low":
                return 1

            # Determine priority based on endpoint
            high_priority_paths = ["/api/signals/", "/api/alerts/"]
            medium_priority_paths = ["/api/portfolio/", "/api/trades/"]

            path = str(request.url.path)

            if any(path.startswith(p) for p in high_priority_paths):
                return 3
            elif any(path.startswith(p) for p in medium_priority_paths):
                return 2
            else:
                return 1

        except Exception as e:
            logger.error(f"Error determining request priority: {str(e)}")
            return 1  # Default to low priority

    async def get_optimization_stats(self) -> Dict[str, Any]:
        """Get comprehensive optimization statistics"""
        try:
            stats = {
                "middleware_stats": self.stats.copy(),
                "timestamp": datetime.utcnow().isoformat()
            }

            # Add batcher stats if available
            if self.request_batcher:
                stats["batcher_stats"] = await self.request_batcher.get_batch_stats()

            # Add load balancer stats if available
            if self.load_balancer:
                stats["load_balancer_stats"] = await self.load_balancer.get_load_balancer_stats()

            # Calculate optimization efficiency
            total_requests = self.stats["total_requests"]
            if total_requests > 0:
                stats["optimization_efficiency"] = {
                    "batch_rate": (self.stats["batched_requests"] / total_requests) * 100,
                    "load_balance_rate": (self.stats["load_balanced_requests"] / total_requests) * 100,
                    "compression_rate": (self.stats["compressed_responses"] / total_requests) * 100,
                    "deduplication_rate": (self.stats["deduplicated_requests"] / total_requests) * 100
                }

            return stats

        except Exception as e:
            logger.error(f"Error getting optimization stats: {str(e)}")
            return {"error": str(e)}

    async def cleanup_dedup_cache(self) -> None:
        """Clean up expired deduplication cache entries"""
        try:
            current_time = time.time()
            expired_keys = []

            for key, entry in self.dedup_cache.items():
                if current_time - entry["timestamp"] > self.dedup_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.dedup_cache[key]

            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired deduplication cache entries")

        except Exception as e:
            logger.error(f"Error cleaning up deduplication cache: {str(e)}")


# Global optimization middleware instance
optimization_middleware = None


def get_optimization_middleware() -> APIOptimizationMiddleware:
    """Get global optimization middleware instance"""
    global optimization_middleware
    if optimization_middleware is None:
        optimization_middleware = APIOptimizationMiddleware(
            app=None,  # Will be set when added to FastAPI app
            enable_batching=True,
            enable_load_balancing=True
        )
    return optimization_middleware
