"""
🤖 Trading Bot

Comprehensive trading bot that orchestrates all automation components
for live trading with signal processing, risk management, and execution.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, SignalStrength
from ...database.models import Portfolio, Signal, Order
from ...features.signal_processing import SignalGenerator, SignalValidator
from ...features.notifications import NotificationManager
from .order_manager import OrderManager
from .risk_manager import RiskManager
from .execution_engine import ExecutionEngine, ExecutionStrategy
from .dex_integrator import DEXIntegrator

logger = get_logger(__name__)


class BotStatus(str, Enum):
    """Trading bot status"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    ERROR = "error"
    EMERGENCY_STOP = "emergency_stop"


class TradingMode(str, Enum):
    """Trading modes"""
    PAPER_TRADING = "paper_trading"
    LIVE_TRADING = "live_trading"
    SIGNAL_ONLY = "signal_only"


class TradingBot:
    """
    🤖 Trading Bot
    
    Orchestrates automated trading with:
    - Signal processing and validation
    - Risk assessment and management
    - Order creation and execution
    - Portfolio monitoring
    - Performance tracking
    - Emergency controls
    """
    
    def __init__(self, portfolio_id: str, config: Dict[str, Any] = None):
        self.logger = logger
        self.portfolio_id = portfolio_id
        self.config = config or {}
        
        # Initialize components
        self.signal_generator = SignalGenerator()
        self.signal_validator = SignalValidator()
        self.order_manager = OrderManager()
        self.risk_manager = RiskManager()
        self.execution_engine = ExecutionEngine()
        self.dex_integrator = DEXIntegrator()
        self.notification_manager = NotificationManager()
        
        # Bot state
        self.status = BotStatus.STOPPED
        self.trading_mode = TradingMode(self.config.get("trading_mode", "paper_trading"))
        self.start_time = None
        self.last_signal_check = None
        
        # Configuration
        self.signal_check_interval = self.config.get("signal_check_interval", 60)  # seconds
        self.max_concurrent_orders = self.config.get("max_concurrent_orders", 5)
        self.min_signal_strength = SignalStrength(self.config.get("min_signal_strength", 2))
        self.auto_execute = self.config.get("auto_execute", True)
        self.risk_limits = self.config.get("risk_limits", {})
        
        # Monitoring tasks
        self.signal_monitor_task = None
        self.portfolio_monitor_task = None
        self.risk_monitor_task = None
        
        # Statistics
        self.stats = {
            "signals_processed": 0,
            "orders_created": 0,
            "trades_executed": 0,
            "total_pnl": Decimal("0"),
            "uptime_seconds": 0
        }
    
    async def start(self):
        """Start the trading bot"""
        try:
            self.logger.info(f"Starting trading bot for portfolio {self.portfolio_id}")
            self.status = BotStatus.STARTING
            
            # Validate portfolio
            portfolio = await Portfolio.get(self.portfolio_id)
            if not portfolio:
                raise ValueError(f"Portfolio {self.portfolio_id} not found")
            
            if portfolio.status != "active":
                raise ValueError(f"Portfolio {self.portfolio_id} is not active")
            
            # Start execution engine
            await self.execution_engine.start_execution_engine()
            
            # Start monitoring tasks
            self.signal_monitor_task = asyncio.create_task(self._signal_monitor())
            self.portfolio_monitor_task = asyncio.create_task(self._portfolio_monitor())
            self.risk_monitor_task = asyncio.create_task(self._risk_monitor())
            
            # Update status
            self.status = BotStatus.RUNNING
            self.start_time = datetime.utcnow()
            
            # Send notification
            await self.notification_manager.send_notification(
                "trading_bot_started",
                {
                    "portfolio_id": self.portfolio_id,
                    "trading_mode": self.trading_mode,
                    "start_time": self.start_time
                }
            )
            
            self.logger.info(f"Trading bot started successfully in {self.trading_mode} mode")
            
        except Exception as e:
            self.status = BotStatus.ERROR
            self.logger.error(f"Error starting trading bot: {str(e)}")
            raise
    
    async def stop(self):
        """Stop the trading bot"""
        try:
            self.logger.info("Stopping trading bot")
            self.status = BotStatus.STOPPED
            
            # Cancel monitoring tasks
            if self.signal_monitor_task:
                self.signal_monitor_task.cancel()
            if self.portfolio_monitor_task:
                self.portfolio_monitor_task.cancel()
            if self.risk_monitor_task:
                self.risk_monitor_task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(
                self.signal_monitor_task,
                self.portfolio_monitor_task,
                self.risk_monitor_task,
                return_exceptions=True
            )
            
            # Stop execution engine
            await self.execution_engine.stop_execution_engine()
            
            # Calculate uptime
            if self.start_time:
                uptime = (datetime.utcnow() - self.start_time).total_seconds()
                self.stats["uptime_seconds"] += uptime
            
            # Send notification
            await self.notification_manager.send_notification(
                "trading_bot_stopped",
                {
                    "portfolio_id": self.portfolio_id,
                    "stats": self.stats,
                    "uptime_seconds": self.stats["uptime_seconds"]
                }
            )
            
            self.logger.info("Trading bot stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping trading bot: {str(e)}")
    
    async def pause(self):
        """Pause the trading bot"""
        try:
            self.logger.info("Pausing trading bot")
            self.status = BotStatus.PAUSED
            
            # Cancel active orders if configured
            if self.config.get("cancel_orders_on_pause", False):
                await self._cancel_active_orders("Bot paused")
            
            await self.notification_manager.send_notification(
                "trading_bot_paused",
                {"portfolio_id": self.portfolio_id}
            )
            
        except Exception as e:
            self.logger.error(f"Error pausing trading bot: {str(e)}")
    
    async def resume(self):
        """Resume the trading bot"""
        try:
            self.logger.info("Resuming trading bot")
            self.status = BotStatus.RUNNING
            
            await self.notification_manager.send_notification(
                "trading_bot_resumed",
                {"portfolio_id": self.portfolio_id}
            )
            
        except Exception as e:
            self.logger.error(f"Error resuming trading bot: {str(e)}")
    
    async def emergency_stop(self, reason: str):
        """Emergency stop the trading bot"""
        try:
            self.logger.critical(f"EMERGENCY STOP: {reason}")
            self.status = BotStatus.EMERGENCY_STOP
            
            # Trigger risk manager emergency stop
            await self.risk_manager.trigger_emergency_stop(reason, self.portfolio_id)
            
            # Cancel all active orders
            await self._cancel_active_orders(f"Emergency stop: {reason}")
            
            # Send critical notification
            await self.notification_manager.send_notification(
                "trading_bot_emergency_stop",
                {
                    "portfolio_id": self.portfolio_id,
                    "reason": reason,
                    "timestamp": datetime.utcnow()
                },
                priority="critical"
            )
            
        except Exception as e:
            self.logger.error(f"Error in emergency stop: {str(e)}")
    
    async def process_signal(self, signal: Signal) -> Dict[str, Any]:
        """
        Process a trading signal
        
        Args:
            signal: Signal to process
            
        Returns:
            Processing result
        """
        try:
            self.logger.info(f"Processing signal {signal.id} for {signal.token_address}")
            self.stats["signals_processed"] += 1
            
            # Validate signal
            validation_result = await self.signal_validator.validate_signal(signal)
            if not validation_result["is_valid"]:
                return {
                    "success": False,
                    "reason": f"Signal validation failed: {validation_result['reason']}"
                }
            
            # Check signal strength
            if signal.strength < self.min_signal_strength:
                return {
                    "success": False,
                    "reason": f"Signal strength {signal.strength} below minimum {self.min_signal_strength}"
                }
            
            # Assess risk
            risk_assessment = await self.risk_manager.assess_trade_risk(
                self.portfolio_id,
                signal.token_address,
                signal.signal_type,
                signal.position_size,
                signal.entry_price,
                signal
            )
            
            if not risk_assessment["allowed"]:
                return {
                    "success": False,
                    "reason": f"Risk assessment failed: {risk_assessment['reason']}"
                }
            
            # Create order if auto-execution is enabled
            if self.auto_execute and self.trading_mode == TradingMode.LIVE_TRADING:
                order_result = await self._create_order_from_signal(signal, risk_assessment)
                return order_result
            else:
                # Send signal notification
                await self.notification_manager.send_notification(
                    "signal_detected",
                    {
                        "signal": signal.to_dict(),
                        "risk_assessment": risk_assessment,
                        "portfolio_id": self.portfolio_id
                    }
                )
                
                return {
                    "success": True,
                    "action": "signal_notified",
                    "signal_id": str(signal.id),
                    "risk_assessment": risk_assessment
                }
            
        except Exception as e:
            self.logger.error(f"Error processing signal: {str(e)}")
            return {"success": False, "reason": str(e)}
    
    async def get_status(self) -> Dict[str, Any]:
        """Get bot status and statistics"""
        try:
            # Calculate current uptime
            current_uptime = 0
            if self.start_time and self.status == BotStatus.RUNNING:
                current_uptime = (datetime.utcnow() - self.start_time).total_seconds()
            
            # Get active orders count
            active_orders = await Order.find({
                "portfolio_id": self.portfolio_id,
                "status": {"$in": ["pending", "partially_filled"]}
            }).count()
            
            return {
                "status": self.status,
                "trading_mode": self.trading_mode,
                "portfolio_id": self.portfolio_id,
                "start_time": self.start_time,
                "current_uptime": current_uptime,
                "total_uptime": self.stats["uptime_seconds"] + current_uptime,
                "active_orders": active_orders,
                "last_signal_check": self.last_signal_check,
                "stats": self.stats,
                "config": self.config
            }
            
        except Exception as e:
            self.logger.error(f"Error getting bot status: {str(e)}")
            return {"status": "error", "error": str(e)}

    async def _signal_monitor(self):
        """Monitor for new trading signals"""
        try:
            while self.status in [BotStatus.RUNNING, BotStatus.PAUSED]:
                if self.status == BotStatus.RUNNING:
                    # Check for new signals
                    await self._check_for_signals()
                    self.last_signal_check = datetime.utcnow()

                await asyncio.sleep(self.signal_check_interval)

        except asyncio.CancelledError:
            self.logger.info("Signal monitor cancelled")
        except Exception as e:
            self.logger.error(f"Error in signal monitor: {str(e)}")
            await self.emergency_stop(f"Signal monitor error: {str(e)}")

    async def _portfolio_monitor(self):
        """Monitor portfolio performance and status"""
        try:
            while self.status in [BotStatus.RUNNING, BotStatus.PAUSED]:
                if self.status == BotStatus.RUNNING:
                    await self._check_portfolio_health()

                await asyncio.sleep(300)  # Check every 5 minutes

        except asyncio.CancelledError:
            self.logger.info("Portfolio monitor cancelled")
        except Exception as e:
            self.logger.error(f"Error in portfolio monitor: {str(e)}")

    async def _risk_monitor(self):
        """Monitor risk limits and conditions"""
        try:
            while self.status in [BotStatus.RUNNING, BotStatus.PAUSED]:
                if self.status == BotStatus.RUNNING:
                    # Check risk limits
                    risk_check = await self.risk_manager.check_risk_limits(self.portfolio_id)

                    if not risk_check["within_limits"]:
                        await self.emergency_stop(f"Risk limits exceeded: {risk_check['reason']}")
                        break

                await asyncio.sleep(60)  # Check every minute

        except asyncio.CancelledError:
            self.logger.info("Risk monitor cancelled")
        except Exception as e:
            self.logger.error(f"Error in risk monitor: {str(e)}")

    async def _check_for_signals(self):
        """Check for new trading signals"""
        try:
            # Get recent signals that haven't been processed
            cutoff_time = datetime.utcnow() - timedelta(minutes=10)
            signals = await Signal.find({
                "created_at": {"$gte": cutoff_time},
                "processed": {"$ne": True}
            }).to_list()

            for signal in signals:
                if self.status != BotStatus.RUNNING:
                    break

                # Process signal
                result = await self.process_signal(signal)

                # Mark signal as processed
                signal.processed = True
                signal.processing_result = result
                await signal.save()

        except Exception as e:
            self.logger.error(f"Error checking for signals: {str(e)}")

    async def _check_portfolio_health(self):
        """Check portfolio health and performance"""
        try:
            portfolio = await Portfolio.get(self.portfolio_id)
            if not portfolio:
                await self.emergency_stop("Portfolio not found")
                return

            # Check if portfolio is still active
            if portfolio.status != "active":
                await self.emergency_stop(f"Portfolio status changed to {portfolio.status}")
                return

            # Update statistics
            self.stats["total_pnl"] = portfolio.total_pnl

            # Check for significant losses
            if portfolio.total_pnl < -portfolio.initial_balance * Decimal("0.2"):  # 20% loss
                await self.emergency_stop("Portfolio loss exceeds 20%")
                return

        except Exception as e:
            self.logger.error(f"Error checking portfolio health: {str(e)}")

    async def _create_order_from_signal(self, signal: Signal, risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Create an order from a validated signal"""
        try:
            # Use recommended position size from risk assessment
            position_size = risk_assessment.get("recommended_size", signal.position_size)

            # Create order
            order = await self.order_manager.create_order(
                portfolio_id=self.portfolio_id,
                token_address=signal.token_address,
                side=signal.signal_type,
                order_type=OrderType.MARKET,
                quantity=position_size,
                price=signal.entry_price,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                signal_id=str(signal.id),
                metadata={
                    "signal_strength": signal.strength,
                    "signal_confidence": signal.confidence,
                    "risk_score": risk_assessment["overall_risk_score"]
                }
            )

            if order:
                self.stats["orders_created"] += 1

                # Execute order if auto-execution is enabled
                if self.auto_execute:
                    execution_result = await self.execution_engine.execute_order(
                        str(order.id),
                        ExecutionStrategy.SMART_ROUTING
                    )

                    if execution_result["success"]:
                        self.stats["trades_executed"] += 1

                    return {
                        "success": True,
                        "action": "order_created_and_executed",
                        "order_id": str(order.id),
                        "execution_result": execution_result
                    }
                else:
                    return {
                        "success": True,
                        "action": "order_created",
                        "order_id": str(order.id)
                    }
            else:
                return {"success": False, "reason": "Failed to create order"}

        except Exception as e:
            self.logger.error(f"Error creating order from signal: {str(e)}")
            return {"success": False, "reason": str(e)}

    async def _cancel_active_orders(self, reason: str):
        """Cancel all active orders for the portfolio"""
        try:
            active_orders = await Order.find({
                "portfolio_id": self.portfolio_id,
                "status": {"$in": ["pending", "partially_filled"]}
            }).to_list()

            for order in active_orders:
                await self.order_manager.cancel_order(str(order.id), reason)

            self.logger.info(f"Cancelled {len(active_orders)} active orders: {reason}")

        except Exception as e:
            self.logger.error(f"Error cancelling active orders: {str(e)}")
