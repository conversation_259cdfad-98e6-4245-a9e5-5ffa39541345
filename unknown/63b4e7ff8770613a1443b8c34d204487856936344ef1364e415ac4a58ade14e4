name: 🚀 TokenTracker V2 - Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: '3.10'
  NODE_ENV: test

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: 🔍 Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: 🔧 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r v2/requirements.txt
        pip install flake8 black isort bandit safety
        
    - name: 🎨 Code formatting check (Black)
      run: |
        cd v2
        black --check --diff .
        
    - name: 📏 Import sorting check (isort)
      run: |
        cd v2
        isort --check-only --diff .
        
    - name: 🔍 Linting (flake8)
      run: |
        cd v2
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: 🔒 Security check (Bandit)
      run: |
        cd v2
        bandit -r src/ -f json -o bandit-report.json || true
        bandit -r src/ --severity-level medium
        
    - name: 🛡️ Dependency security check (Safety)
      run: |
        cd v2
        safety check --json --output safety-report.json || true
        safety check
        
    - name: 📊 Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          v2/bandit-report.json
          v2/safety-report.json

  # Unit and Integration Tests
  test:
    name: 🧪 Tests
    runs-on: ubuntu-latest
    needs: code-quality
    
    strategy:
      matrix:
        test-type: [unit, integration]
        
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
      mongodb:
        image: mongo:7
        ports:
          - 27017:27017
        env:
          MONGO_INITDB_ROOT_USERNAME: test
          MONGO_INITDB_ROOT_PASSWORD: test
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 📦 Cache dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: 🔧 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r v2/requirements.txt
        pip install pytest pytest-asyncio pytest-cov pytest-mock
        
    - name: 🌍 Set up test environment
      run: |
        cd v2
        cp .env.example .env.test
        echo "MONGODB_URI=*****************************************************" >> .env.test
        echo "REDIS_URL=redis://localhost:6379/1" >> .env.test
        echo "NODE_ENV=test" >> .env.test
        
    - name: 🧪 Run unit tests
      if: matrix.test-type == 'unit'
      run: |
        cd v2
        python -m pytest tests/test_*.py -v --cov=src --cov-report=xml --cov-report=html --cov-report=term
        
    - name: 🔗 Run integration tests
      if: matrix.test-type == 'integration'
      run: |
        cd v2
        python -m pytest tests/test_*integration*.py -v --cov=src --cov-report=xml --cov-append
        
    - name: 📊 Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: v2/coverage.xml
        flags: ${{ matrix.test-type }}
        name: codecov-${{ matrix.test-type }}
        
    - name: 📈 Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-${{ matrix.test-type }}
        path: |
          v2/coverage.xml
          v2/htmlcov/

  # Performance Tests
  performance:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 🔧 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r v2/requirements.txt
        pip install locust pytest-benchmark
        
    - name: ⚡ Run performance benchmarks
      run: |
        cd v2
        python -m pytest tests/test_performance.py --benchmark-only --benchmark-json=benchmark.json
        
    - name: 📊 Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: performance-benchmarks
        path: v2/benchmark.json

  # Docker Build and Security Scan
  docker:
    name: 🐳 Docker Build & Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 🔧 Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./v2
        file: ./v2/Dockerfile
        push: false
        tags: tokentracker-v2:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: 🔍 Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: tokentracker-v2:test
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: 📊 Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Documentation Build
  docs:
    name: 📚 Documentation
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: 🔧 Install documentation dependencies
      run: |
        python -m pip install --upgrade pip
        pip install mkdocs mkdocs-material mkdocs-mermaid2-plugin
        
    - name: 📚 Build documentation
      run: |
        cd v2
        mkdocs build --strict
        
    - name: 📊 Upload documentation
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: v2/site/

  # Notification on Success/Failure
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [code-quality, test, performance, docker, docs]
    if: always()
    
    steps:
    - name: 📢 Notify on success
      if: ${{ needs.code-quality.result == 'success' && needs.test.result == 'success' && needs.docker.result == 'success' }}
      run: |
        echo "✅ All CI checks passed successfully!"
        
    - name: 📢 Notify on failure
      if: ${{ needs.code-quality.result == 'failure' || needs.test.result == 'failure' || needs.docker.result == 'failure' }}
      run: |
        echo "❌ CI checks failed. Please review the logs."
        exit 1
