name: 🚀 TokenTracker V2 - Deployment Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      force_deploy:
        description: 'Force deployment (skip some checks)'
        required: false
        default: false
        type: boolean

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: tokentracker-v2

jobs:
  # Build and Push Docker Image
  build:
    name: 🏗️ Build & Push Image
    runs-on: ubuntu-latest
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 🔐 Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 🏷️ Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
          
    - name: 🏗️ Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ./v2
        file: ./v2/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # Deploy to Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: https://staging.tokentracker.com
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: 🔐 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1
        
    - name: 🔧 Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name tokentracker-staging
        
    - name: 🚀 Deploy to staging
      run: |
        cd v2/k8s/staging
        
        # Update image tag in deployment
        sed -i "s|IMAGE_TAG|${{ needs.build.outputs.image-tag }}|g" deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f namespace.yaml
        kubectl apply -f configmap.yaml
        kubectl apply -f secrets.yaml
        kubectl apply -f deployment.yaml
        kubectl apply -f service.yaml
        kubectl apply -f ingress.yaml
        
        # Wait for rollout to complete
        kubectl rollout status deployment/tokentracker-v2 -n tokentracker-staging --timeout=600s
        
    - name: 🧪 Run smoke tests
      run: |
        cd v2
        python -m pytest tests/test_smoke.py --staging-url=https://staging.tokentracker.com
        
    - name: 📊 Update deployment status
      if: always()
      run: |
        if [ $? -eq 0 ]; then
          echo "✅ Staging deployment successful"
        else
          echo "❌ Staging deployment failed"
          exit 1
        fi

  # Deploy to Production
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [build, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: https://tokentracker.com
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: 🔐 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
        aws-region: us-east-1
        
    - name: 🔧 Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name tokentracker-production
        
    - name: 📋 Pre-deployment checks
      if: github.event.inputs.force_deploy != 'true'
      run: |
        cd v2
        
        # Check if staging tests passed
        echo "🔍 Verifying staging deployment health..."
        python scripts/check_staging_health.py
        
        # Check database migrations
        echo "🗄️ Checking database migration status..."
        python scripts/check_migrations.py --env=production
        
        # Check resource availability
        echo "📊 Checking production resource availability..."
        kubectl top nodes
        kubectl top pods -n tokentracker-production
        
    - name: 🚀 Blue-Green Deployment
      run: |
        cd v2/k8s/production
        
        # Update image tag in deployment
        sed -i "s|IMAGE_TAG|${{ needs.build.outputs.image-tag }}|g" deployment.yaml
        
        # Create green deployment
        sed 's/tokentracker-v2/tokentracker-v2-green/g' deployment.yaml > deployment-green.yaml
        
        # Deploy green version
        kubectl apply -f deployment-green.yaml
        kubectl rollout status deployment/tokentracker-v2-green -n tokentracker-production --timeout=600s
        
        # Run health checks on green deployment
        echo "🏥 Running health checks on green deployment..."
        python ../../scripts/health_check.py --target=green --namespace=tokentracker-production
        
        # Switch traffic to green (update service selector)
        kubectl patch service tokentracker-v2 -n tokentracker-production -p '{"spec":{"selector":{"version":"green"}}}'
        
        # Wait and verify
        sleep 30
        python ../../scripts/health_check.py --target=production --namespace=tokentracker-production
        
        # Clean up old blue deployment
        kubectl delete deployment tokentracker-v2-blue -n tokentracker-production --ignore-not-found=true
        
        # Rename green to blue for next deployment
        kubectl patch deployment tokentracker-v2-green -n tokentracker-production -p '{"metadata":{"name":"tokentracker-v2-blue"}}'
        
    - name: 🧪 Post-deployment tests
      run: |
        cd v2
        
        # Run comprehensive production tests
        python -m pytest tests/test_production.py --production-url=https://tokentracker.com
        
        # Run performance tests
        python scripts/performance_test.py --target=production
        
        # Verify monitoring and alerting
        python scripts/verify_monitoring.py --env=production
        
    - name: 📊 Update deployment metrics
      if: always()
      run: |
        cd v2
        python scripts/update_deployment_metrics.py \
          --environment=production \
          --version=${{ github.ref_name }} \
          --status=${{ job.status }} \
          --image-digest=${{ needs.build.outputs.image-digest }}

  # Rollback Capability
  rollback:
    name: 🔄 Rollback
    runs-on: ubuntu-latest
    if: failure() && (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v'))
    needs: [deploy-staging, deploy-production]
    environment:
      name: production
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
        
    - name: 🔐 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
        aws-region: us-east-1
        
    - name: 🔧 Update kubeconfig
      run: |
        aws eks update-kubeconfig --region us-east-1 --name tokentracker-production
        
    - name: 🔄 Perform rollback
      run: |
        cd v2
        
        echo "🚨 Deployment failed, initiating rollback..."
        
        # Get previous successful deployment
        PREVIOUS_REVISION=$(kubectl rollout history deployment/tokentracker-v2 -n tokentracker-production | tail -2 | head -1 | awk '{print $1}')
        
        # Rollback to previous revision
        kubectl rollout undo deployment/tokentracker-v2 -n tokentracker-production --to-revision=$PREVIOUS_REVISION
        
        # Wait for rollback to complete
        kubectl rollout status deployment/tokentracker-v2 -n tokentracker-production --timeout=300s
        
        # Verify rollback health
        python scripts/health_check.py --target=production --namespace=tokentracker-production
        
        echo "✅ Rollback completed successfully"
        
    - name: 📢 Notify rollback
      run: |
        echo "🔄 Production rollback completed due to deployment failure"
        # Here you would typically send notifications to Slack, email, etc.

  # Cleanup
  cleanup:
    name: 🧹 Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: 🧹 Clean up old images
      run: |
        echo "🧹 Cleaning up old container images..."
        # This would typically clean up old images from the registry
        # to save storage space
        
    - name: 📊 Generate deployment report
      run: |
        echo "📊 Generating deployment report..."
        echo "Deployment completed at: $(date)"
        echo "Git commit: ${{ github.sha }}"
        echo "Image tag: ${{ needs.build.outputs.image-tag }}"
