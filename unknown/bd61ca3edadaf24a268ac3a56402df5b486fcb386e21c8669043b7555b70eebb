"""
📋 Order Manager

Comprehensive order management system for live trading with order creation,
validation, tracking, modification, cancellation, and history reporting.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
from uuid import uuid4
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, OrderStatus, TradeStatus, SignalData
from ...database.models import Order, Trade, Portfolio, Signal
from ...database.models.order import OrderPriority
from ...features.data_pipeline import DataAggregator
from ...features.signal_processing import RiskAssessor

logger = get_logger(__name__)


class OrderManager:
    """
    📋 Order Manager
    
    Manages live trading orders with:
    - Order creation and validation
    - Order status tracking and updates
    - Order modification and cancellation
    - Order history and reporting
    - Risk-based order validation
    - Automated order execution monitoring
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        self.risk_assessor = RiskAssessor()
        
        # Order management parameters
        self.max_orders_per_portfolio = 50
        self.max_order_value_usd = Decimal("10000")
        self.min_order_value_usd = Decimal("10")
        self.default_order_ttl_hours = 24
        
        # Active order tracking
        self.active_orders: Dict[str, Order] = {}
        self.order_monitors: Dict[str, asyncio.Task] = {}
    
    async def create_order(
        self,
        portfolio_id: str,
        token_address: str,
        side: SignalType,
        order_type: OrderType,
        quantity: Decimal,
        price: Optional[Decimal] = None,
        stop_price: Optional[Decimal] = None,
        limit_price: Optional[Decimal] = None,
        stop_loss: Optional[Decimal] = None,
        take_profit: Optional[Decimal] = None,
        signal_id: Optional[str] = None,
        priority: OrderPriority = OrderPriority.NORMAL,
        expires_at: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Order]:
        """
        Create a new trading order
        
        Args:
            portfolio_id: Portfolio ID
            token_address: Token address
            side: BUY or SELL
            order_type: Order type (MARKET, LIMIT, STOP, STOP_LIMIT)
            quantity: Order quantity
            price: Order price (for market orders)
            stop_price: Stop trigger price
            limit_price: Limit price
            stop_loss: Stop loss price
            take_profit: Take profit price
            signal_id: Source signal ID
            priority: Order execution priority
            expires_at: Order expiration time
            metadata: Additional order metadata
            
        Returns:
            Created Order object or None if validation fails
        """
        try:
            self.logger.info(f"Creating order: {side} {quantity} {token_address}")
            
            # Validate order parameters
            validation_result = await self._validate_order_parameters(
                portfolio_id, token_address, side, order_type, quantity,
                price, stop_price, limit_price
            )
            
            if not validation_result['valid']:
                self.logger.error(f"Order validation failed: {validation_result['reason']}")
                return None
            
            # Get current market data
            market_data = await self.data_aggregator.get_token_data(token_address)
            if not market_data:
                self.logger.error(f"Could not get market data for {token_address}")
                return None
            
            # Calculate order value
            order_price = price or market_data.price
            order_value = quantity * order_price
            
            # Set default expiration if not provided
            if not expires_at:
                expires_at = datetime.utcnow() + timedelta(hours=self.default_order_ttl_hours)
            
            # Create order object
            order = Order(
                portfolio_id=portfolio_id,
                signal_id=signal_id,
                token_address=token_address,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=order_price,
                stop_price=stop_price,
                limit_price=limit_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                value_usd=order_value,
                status=OrderStatus.PENDING,
                priority=priority,
                expires_at=expires_at,
                metadata=metadata or {},
                created_at=datetime.utcnow()
            )
            
            # Save order to database
            await order.save()
            
            # Add to active orders tracking
            self.active_orders[str(order.id)] = order
            
            # Start order monitoring
            monitor_task = asyncio.create_task(self._monitor_order(order))
            self.order_monitors[str(order.id)] = monitor_task
            
            self.logger.info(f"Order created successfully: {order.id}")
            return order
            
        except Exception as e:
            self.logger.error(f"Error creating order: {str(e)}")
            return None
    
    async def modify_order(
        self,
        order_id: str,
        quantity: Optional[Decimal] = None,
        price: Optional[Decimal] = None,
        stop_price: Optional[Decimal] = None,
        limit_price: Optional[Decimal] = None,
        expires_at: Optional[datetime] = None
    ) -> bool:
        """
        Modify an existing order
        
        Args:
            order_id: Order ID to modify
            quantity: New quantity
            price: New price
            stop_price: New stop price
            limit_price: New limit price
            expires_at: New expiration time
            
        Returns:
            True if modification successful, False otherwise
        """
        try:
            self.logger.info(f"Modifying order {order_id}")
            
            # Get order from database
            order = await Order.get(order_id)
            if not order:
                self.logger.error(f"Order {order_id} not found")
                return False
            
            # Check if order can be modified
            if order.status not in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]:
                self.logger.error(f"Cannot modify order {order_id} with status {order.status}")
                return False
            
            # Update order fields
            modified = False
            if quantity is not None and quantity != order.quantity:
                order.quantity = quantity
                modified = True
            
            if price is not None and price != order.price:
                order.price = price
                modified = True
            
            if stop_price is not None and stop_price != order.stop_price:
                order.stop_price = stop_price
                modified = True
            
            if limit_price is not None and limit_price != order.limit_price:
                order.limit_price = limit_price
                modified = True
            
            if expires_at is not None and expires_at != order.expires_at:
                order.expires_at = expires_at
                modified = True
            
            if modified:
                # Recalculate order value
                order.value_usd = order.quantity * order.price
                order.updated_at = datetime.utcnow()
                
                # Save updated order
                await order.save()
                
                # Update active orders tracking
                self.active_orders[order_id] = order
                
                self.logger.info(f"Order {order_id} modified successfully")
                return True
            else:
                self.logger.info(f"No changes made to order {order_id}")
                return True
            
        except Exception as e:
            self.logger.error(f"Error modifying order {order_id}: {str(e)}")
            return False
    
    async def cancel_order(self, order_id: str, reason: str = "User requested") -> bool:
        """
        Cancel an existing order
        
        Args:
            order_id: Order ID to cancel
            reason: Cancellation reason
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            self.logger.info(f"Cancelling order {order_id}: {reason}")
            
            # Get order from database
            order = await Order.get(order_id)
            if not order:
                self.logger.error(f"Order {order_id} not found")
                return False
            
            # Check if order can be cancelled
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.EXPIRED]:
                self.logger.error(f"Cannot cancel order {order_id} with status {order.status}")
                return False
            
            # Update order status
            order.status = OrderStatus.CANCELLED
            order.cancelled_at = datetime.utcnow()
            order.cancellation_reason = reason
            order.updated_at = datetime.utcnow()
            
            # Save updated order
            await order.save()
            
            # Remove from active orders
            if order_id in self.active_orders:
                del self.active_orders[order_id]
            
            # Cancel monitoring task
            if order_id in self.order_monitors:
                self.order_monitors[order_id].cancel()
                del self.order_monitors[order_id]
            
            self.logger.info(f"Order {order_id} cancelled successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {str(e)}")
            return False

    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed order status and information

        Args:
            order_id: Order ID

        Returns:
            Order status dictionary or None if not found
        """
        try:
            order = await Order.get(order_id)
            if not order:
                return None

            # Get related trades
            trades = await Trade.find({"order_id": order_id}).to_list()

            # Calculate filled quantity and average price
            filled_quantity = sum(trade.quantity for trade in trades if trade.status == TradeStatus.COMPLETED)
            total_value = sum(trade.value_usd for trade in trades if trade.status == TradeStatus.COMPLETED)
            avg_fill_price = total_value / filled_quantity if filled_quantity > 0 else Decimal("0")

            return {
                "order_id": str(order.id),
                "status": order.status,
                "side": order.side,
                "order_type": order.order_type,
                "quantity": order.quantity,
                "filled_quantity": filled_quantity,
                "remaining_quantity": order.quantity - filled_quantity,
                "price": order.price,
                "avg_fill_price": avg_fill_price,
                "value_usd": order.value_usd,
                "created_at": order.created_at,
                "updated_at": order.updated_at,
                "expires_at": order.expires_at,
                "trades": [
                    {
                        "trade_id": str(trade.id),
                        "quantity": trade.quantity,
                        "price": trade.price,
                        "value_usd": trade.value_usd,
                        "executed_at": trade.executed_at,
                        "status": trade.status
                    }
                    for trade in trades
                ]
            }

        except Exception as e:
            self.logger.error(f"Error getting order status {order_id}: {str(e)}")
            return None

    async def get_portfolio_orders(
        self,
        portfolio_id: str,
        status_filter: Optional[List[OrderStatus]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Get orders for a specific portfolio

        Args:
            portfolio_id: Portfolio ID
            status_filter: Filter by order status
            limit: Maximum number of orders to return

        Returns:
            List of order dictionaries
        """
        try:
            # Build query
            query = {"portfolio_id": portfolio_id}
            if status_filter:
                query["status"] = {"$in": status_filter}

            # Get orders
            orders = await Order.find(query).limit(limit).sort("-created_at").to_list()

            # Convert to dictionaries
            order_list = []
            for order in orders:
                order_dict = {
                    "order_id": str(order.id),
                    "token_address": order.token_address,
                    "side": order.side,
                    "order_type": order.order_type,
                    "quantity": order.quantity,
                    "price": order.price,
                    "value_usd": order.value_usd,
                    "status": order.status,
                    "priority": order.priority,
                    "created_at": order.created_at,
                    "expires_at": order.expires_at
                }
                order_list.append(order_dict)

            return order_list

        except Exception as e:
            self.logger.error(f"Error getting portfolio orders {portfolio_id}: {str(e)}")
            return []

    async def _validate_order_parameters(
        self,
        portfolio_id: str,
        token_address: str,
        side: SignalType,
        order_type: OrderType,
        quantity: Decimal,
        price: Optional[Decimal],
        stop_price: Optional[Decimal],
        limit_price: Optional[Decimal]
    ) -> Dict[str, Any]:
        """Validate order parameters"""
        try:
            # Check portfolio exists
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio:
                return {"valid": False, "reason": "Portfolio not found"}

            # Check portfolio is active
            if portfolio.status != "active":
                return {"valid": False, "reason": "Portfolio is not active"}

            # Check quantity is positive
            if quantity <= 0:
                return {"valid": False, "reason": "Quantity must be positive"}

            # Check order type specific requirements
            if order_type == OrderType.LIMIT and not limit_price:
                return {"valid": False, "reason": "Limit price required for limit orders"}

            if order_type == OrderType.STOP and not stop_price:
                return {"valid": False, "reason": "Stop price required for stop orders"}

            if order_type == OrderType.STOP_LIMIT and (not stop_price or not limit_price):
                return {"valid": False, "reason": "Stop price and limit price required for stop-limit orders"}

            # Check order value limits
            order_price = price or limit_price or stop_price
            if order_price:
                order_value = quantity * order_price
                if order_value < self.min_order_value_usd:
                    return {"valid": False, "reason": f"Order value below minimum ${self.min_order_value_usd}"}

                if order_value > self.max_order_value_usd:
                    return {"valid": False, "reason": f"Order value exceeds maximum ${self.max_order_value_usd}"}

            # Check portfolio order limit
            active_orders_count = await Order.find({
                "portfolio_id": portfolio_id,
                "status": {"$in": [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]}
            }).count()

            if active_orders_count >= self.max_orders_per_portfolio:
                return {"valid": False, "reason": f"Portfolio has reached maximum orders limit ({self.max_orders_per_portfolio})"}

            return {"valid": True, "reason": "Order parameters valid"}

        except Exception as e:
            self.logger.error(f"Error validating order parameters: {str(e)}")
            return {"valid": False, "reason": "Validation error"}

    async def _monitor_order(self, order: Order):
        """Monitor order for expiration and execution opportunities"""
        try:
            self.logger.info(f"Starting order monitoring for {order.id}")

            while order.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]:
                # Check if order has expired
                if datetime.utcnow() >= order.expires_at:
                    await self._expire_order(order)
                    break

                # Check for execution opportunities (for limit orders)
                if order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]:
                    await self._check_execution_opportunity(order)

                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds

                # Refresh order from database
                order = await Order.get(str(order.id))
                if not order:
                    break

            self.logger.info(f"Order monitoring ended for {order.id}")

        except asyncio.CancelledError:
            self.logger.info(f"Order monitoring cancelled for {order.id}")
        except Exception as e:
            self.logger.error(f"Error monitoring order {order.id}: {str(e)}")

    async def _expire_order(self, order: Order):
        """Mark order as expired"""
        try:
            order.status = OrderStatus.EXPIRED
            order.expired_at = datetime.utcnow()
            order.updated_at = datetime.utcnow()
            await order.save()

            # Remove from active orders
            if str(order.id) in self.active_orders:
                del self.active_orders[str(order.id)]

            self.logger.info(f"Order {order.id} expired")

        except Exception as e:
            self.logger.error(f"Error expiring order {order.id}: {str(e)}")

    async def _check_execution_opportunity(self, order: Order):
        """Check if limit order can be executed"""
        try:
            # Get current market data
            market_data = await self.data_aggregator.get_token_data(order.token_address)
            if not market_data:
                return

            current_price = market_data.price
            can_execute = False

            # Check execution conditions based on order type
            if order.order_type == OrderType.LIMIT:
                if order.side == SignalType.BUY and current_price <= order.limit_price:
                    can_execute = True
                elif order.side == SignalType.SELL and current_price >= order.limit_price:
                    can_execute = True

            elif order.order_type == OrderType.STOP_LIMIT:
                # For stop-limit orders, check if stop price is triggered
                if order.side == SignalType.BUY and current_price >= order.stop_price:
                    can_execute = True
                elif order.side == SignalType.SELL and current_price <= order.stop_price:
                    can_execute = True

            if can_execute:
                self.logger.info(f"Execution opportunity detected for order {order.id}")
                # This would trigger the execution engine
                # For now, just log the opportunity

        except Exception as e:
            self.logger.error(f"Error checking execution opportunity for order {order.id}: {str(e)}")
