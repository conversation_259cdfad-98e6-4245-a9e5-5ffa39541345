#!/usr/bin/env python3
"""
🔍 Staging Health Verification Script

Verifies that staging deployment is healthy before promoting to production.
Used in CI/CD pipeline as a gate for production deployments.
"""

import asyncio
import sys
import time
from datetime import datetime, timedelta
import httpx
import json

STAGING_URL = "https://staging.tokentracker.com"
HEALTH_REQUIREMENTS = {
    "min_uptime_minutes": 30,  # Staging must be up for at least 30 minutes
    "max_error_rate_percent": 5.0,  # Error rate must be below 5%
    "max_avg_response_time_ms": 500,  # Average response time must be below 500ms
    "min_success_rate_percent": 95.0,  # Success rate must be above 95%
    "required_endpoints": [
        "/health",
        "/ready", 
        "/api/version",
        "/api/tokens/price",
        "/api/signals/generate"
    ]
}

class StagingHealthVerifier:
    """Verifies staging deployment health for production promotion"""
    
    def __init__(self):
        self.base_url = STAGING_URL
        self.client = httpx.AsyncClient(timeout=30)
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def verify_staging_health(self) -> bool:
        """
        Comprehensive staging health verification
        
        Returns:
            True if staging is healthy for production promotion
        """
        print("🔍 STAGING HEALTH VERIFICATION")
        print("=" * 50)
        print(f"Staging URL: {self.base_url}")
        print(f"Verification Time: {datetime.utcnow().isoformat()}")
        print()
        
        verification_results = {
            "uptime_check": False,
            "error_rate_check": False,
            "response_time_check": False,
            "endpoint_availability_check": False,
            "performance_metrics_check": False
        }
        
        try:
            # 1. Check uptime
            print("⏱️ Checking deployment uptime...")
            verification_results["uptime_check"] = await self._check_uptime()
            
            # 2. Check error rates
            print("📊 Checking error rates...")
            verification_results["error_rate_check"] = await self._check_error_rates()
            
            # 3. Check response times
            print("⚡ Checking response times...")
            verification_results["response_time_check"] = await self._check_response_times()
            
            # 4. Check endpoint availability
            print("🌐 Checking endpoint availability...")
            verification_results["endpoint_availability_check"] = await self._check_endpoint_availability()
            
            # 5. Check performance metrics
            print("📈 Checking performance metrics...")
            verification_results["performance_metrics_check"] = await self._check_performance_metrics()
            
        except Exception as e:
            print(f"❌ Verification failed with error: {str(e)}")
            return False
        
        # Evaluate results
        print("\n" + "=" * 50)
        print("📋 VERIFICATION RESULTS")
        print("=" * 50)
        
        all_passed = True
        for check_name, passed in verification_results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{check_name.replace('_', ' ').title()}: {status}")
            if not passed:
                all_passed = False
        
        print("\n" + "=" * 50)
        if all_passed:
            print("✅ STAGING VERIFICATION PASSED - Ready for production deployment!")
        else:
            print("❌ STAGING VERIFICATION FAILED - Production deployment blocked!")
        
        return all_passed
    
    async def _check_uptime(self) -> bool:
        """Check if staging has been up for minimum required time"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code != 200:
                print(f"  ❌ Health endpoint returned {response.status_code}")
                return False
            
            health_data = response.json()
            uptime_seconds = health_data.get("uptime_seconds", 0)
            uptime_minutes = uptime_seconds / 60
            
            required_minutes = HEALTH_REQUIREMENTS["min_uptime_minutes"]
            
            print(f"  Current uptime: {uptime_minutes:.1f} minutes")
            print(f"  Required uptime: {required_minutes} minutes")
            
            if uptime_minutes >= required_minutes:
                print(f"  ✅ Uptime requirement met")
                return True
            else:
                print(f"  ❌ Uptime requirement not met")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking uptime: {str(e)}")
            return False
    
    async def _check_error_rates(self) -> bool:
        """Check if error rates are within acceptable limits"""
        try:
            response = await self.client.get(f"{self.base_url}/metrics")
            if response.status_code != 200:
                print(f"  ❌ Metrics endpoint returned {response.status_code}")
                return False
            
            # Parse metrics (simplified - would parse Prometheus format in real implementation)
            metrics_text = response.text
            
            # Extract error rate (simplified parsing)
            # In real implementation, would use prometheus_client to parse
            error_rate = 0.0  # Placeholder
            
            # For demo, simulate getting error rate from health endpoint
            health_response = await self.client.get(f"{self.base_url}/health")
            if health_response.status_code == 200:
                health_data = health_response.json()
                error_rate = health_data.get("error_rate_percent", 0.0)
            
            max_error_rate = HEALTH_REQUIREMENTS["max_error_rate_percent"]
            
            print(f"  Current error rate: {error_rate:.2f}%")
            print(f"  Maximum allowed: {max_error_rate}%")
            
            if error_rate <= max_error_rate:
                print(f"  ✅ Error rate within limits")
                return True
            else:
                print(f"  ❌ Error rate exceeds limit")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking error rates: {str(e)}")
            return False
    
    async def _check_response_times(self) -> bool:
        """Check if response times are within acceptable limits"""
        try:
            # Test multiple endpoints and measure response times
            test_endpoints = ["/health", "/ready", "/api/version"]
            response_times = []
            
            for endpoint in test_endpoints:
                start_time = time.time()
                response = await self.client.get(f"{self.base_url}{endpoint}")
                response_time = (time.time() - start_time) * 1000  # Convert to ms
                
                if response.status_code == 200:
                    response_times.append(response_time)
                    print(f"  {endpoint}: {response_time:.1f}ms")
            
            if not response_times:
                print(f"  ❌ No successful responses to measure")
                return False
            
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = HEALTH_REQUIREMENTS["max_avg_response_time_ms"]
            
            print(f"  Average response time: {avg_response_time:.1f}ms")
            print(f"  Maximum allowed: {max_response_time}ms")
            
            if avg_response_time <= max_response_time:
                print(f"  ✅ Response times within limits")
                return True
            else:
                print(f"  ❌ Response times exceed limit")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking response times: {str(e)}")
            return False
    
    async def _check_endpoint_availability(self) -> bool:
        """Check if all required endpoints are available"""
        try:
            required_endpoints = HEALTH_REQUIREMENTS["required_endpoints"]
            available_count = 0
            
            for endpoint in required_endpoints:
                try:
                    response = await self.client.get(f"{self.base_url}{endpoint}")
                    if response.status_code == 200:
                        available_count += 1
                        print(f"  ✅ {endpoint}: Available")
                    else:
                        print(f"  ❌ {endpoint}: Status {response.status_code}")
                except Exception as e:
                    print(f"  ❌ {endpoint}: Error - {str(e)}")
            
            success_rate = (available_count / len(required_endpoints)) * 100
            min_success_rate = HEALTH_REQUIREMENTS["min_success_rate_percent"]
            
            print(f"  Endpoint availability: {success_rate:.1f}%")
            print(f"  Required availability: {min_success_rate}%")
            
            if success_rate >= min_success_rate:
                print(f"  ✅ Endpoint availability meets requirements")
                return True
            else:
                print(f"  ❌ Endpoint availability below requirements")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking endpoint availability: {str(e)}")
            return False
    
    async def _check_performance_metrics(self) -> bool:
        """Check overall performance metrics"""
        try:
            # Get comprehensive health data
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code != 200:
                print(f"  ❌ Health endpoint returned {response.status_code}")
                return False
            
            health_data = response.json()
            
            # Check various performance indicators
            checks = []
            
            # Database health
            db_status = health_data.get("database", {}).get("status", "unknown")
            checks.append(("Database", db_status == "healthy"))
            
            # Cache health
            cache_status = health_data.get("cache", {}).get("status", "unknown")
            checks.append(("Cache", cache_status == "healthy"))
            
            # Memory usage
            memory_usage = health_data.get("memory_usage_percent", 0)
            checks.append(("Memory Usage", memory_usage < 80))  # Less than 80%
            
            # CPU usage
            cpu_usage = health_data.get("cpu_usage_percent", 0)
            checks.append(("CPU Usage", cpu_usage < 70))  # Less than 70%
            
            # Print results
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"  {status} {check_name}: {'PASS' if passed else 'FAIL'}")
                if not passed:
                    all_passed = False
            
            return all_passed
            
        except Exception as e:
            print(f"  ❌ Error checking performance metrics: {str(e)}")
            return False

async def main():
    """Main verification function"""
    print("🚀 TokenTracker V2 - Staging Health Verification")
    print("Used to verify staging health before production deployment")
    print()
    
    async with StagingHealthVerifier() as verifier:
        is_healthy = await verifier.verify_staging_health()
    
    if is_healthy:
        print("\n🎉 Staging verification completed successfully!")
        print("Production deployment can proceed.")
        sys.exit(0)
    else:
        print("\n🚨 Staging verification failed!")
        print("Production deployment should be blocked.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
