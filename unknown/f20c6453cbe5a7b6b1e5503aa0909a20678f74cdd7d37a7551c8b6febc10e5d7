#!/bin/bash
# 🏥 Docker Health Check Script for TokenTracker V2
# Comprehensive health checking for containerized deployment

set -e

# Configuration
HOST=${HOST:-localhost}
PORT=${PORT:-8000}
TIMEOUT=${TIMEOUT:-10}
MAX_RETRIES=${MAX_RETRIES:-3}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] HEALTHCHECK:${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] HEALTHCHECK WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] HEALTHCHECK ERROR:${NC} $1"
}

# Function to check HTTP endpoint
check_endpoint() {
    local endpoint=$1
    local expected_status=${2:-200}
    local description=$3
    
    log "Checking $description ($endpoint)..."
    
    local response
    local status_code
    
    # Make HTTP request with timeout
    response=$(curl -s -w "%{http_code}" --max-time $TIMEOUT "http://${HOST}:${PORT}${endpoint}" 2>/dev/null || echo "000")
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        log "✅ $description: OK (HTTP $status_code)"
        return 0
    else
        error "❌ $description: FAILED (HTTP $status_code, expected $expected_status)"
        return 1
    fi
}

# Function to check JSON response content
check_json_endpoint() {
    local endpoint=$1
    local expected_field=$2
    local expected_value=$3
    local description=$4
    
    log "Checking $description ($endpoint)..."
    
    local response
    local status_code
    local body
    
    # Make HTTP request
    response=$(curl -s -w "%{http_code}" --max-time $TIMEOUT "http://${HOST}:${PORT}${endpoint}" 2>/dev/null || echo "000")
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" != "200" ]; then
        error "❌ $description: HTTP request failed (HTTP $status_code)"
        return 1
    fi
    
    # Check if response is valid JSON and contains expected field
    if command -v python3 >/dev/null 2>&1; then
        local field_value
        field_value=$(echo "$body" | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print(data.get('$expected_field', ''))
except:
    print('')
" 2>/dev/null)
        
        if [ "$field_value" = "$expected_value" ]; then
            log "✅ $description: OK ($expected_field=$field_value)"
            return 0
        else
            error "❌ $description: FAILED ($expected_field=$field_value, expected $expected_value)"
            return 1
        fi
    else
        # Fallback: simple string search
        if echo "$body" | grep -q "\"$expected_field\".*\"$expected_value\""; then
            log "✅ $description: OK (contains $expected_field=$expected_value)"
            return 0
        else
            error "❌ $description: FAILED (missing or incorrect $expected_field)"
            return 1
        fi
    fi
}

# Function to check process is running
check_process() {
    local process_name=$1
    local description=$2
    
    log "Checking $description..."
    
    if pgrep -f "$process_name" >/dev/null 2>&1; then
        log "✅ $description: Running"
        return 0
    else
        error "❌ $description: Not running"
        return 1
    fi
}

# Function to check memory usage
check_memory() {
    local max_memory_percent=${1:-90}
    
    log "Checking memory usage..."
    
    if command -v free >/dev/null 2>&1; then
        local memory_usage
        memory_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        
        if [ "$memory_usage" -lt "$max_memory_percent" ]; then
            log "✅ Memory usage: OK (${memory_usage}%)"
            return 0
        else
            warn "⚠️ Memory usage: HIGH (${memory_usage}%, threshold ${max_memory_percent}%)"
            return 1
        fi
    else
        warn "⚠️ Memory usage: Cannot check (free command not available)"
        return 0
    fi
}

# Function to check disk space
check_disk() {
    local max_disk_percent=${1:-85}
    local path=${2:-/app}
    
    log "Checking disk space for $path..."
    
    if command -v df >/dev/null 2>&1; then
        local disk_usage
        disk_usage=$(df "$path" | awk 'NR==2{print $5}' | sed 's/%//')
        
        if [ "$disk_usage" -lt "$max_disk_percent" ]; then
            log "✅ Disk usage: OK (${disk_usage}%)"
            return 0
        else
            warn "⚠️ Disk usage: HIGH (${disk_usage}%, threshold ${max_disk_percent}%)"
            return 1
        fi
    else
        warn "⚠️ Disk usage: Cannot check (df command not available)"
        return 0
    fi
}

# Main health check function
main() {
    log "🏥 Starting TokenTracker V2 health check..."
    log "Target: http://${HOST}:${PORT}"
    log "Timeout: ${TIMEOUT}s"
    
    local failed_checks=0
    local total_checks=0
    
    # Critical health checks (must pass)
    log "🔍 Running critical health checks..."
    
    # Basic health endpoint
    ((total_checks++))
    if ! check_json_endpoint "/health" "status" "healthy" "Basic Health Check"; then
        ((failed_checks++))
    fi
    
    # Readiness endpoint
    ((total_checks++))
    if ! check_json_endpoint "/ready" "status" "ready" "Readiness Check"; then
        ((failed_checks++))
    fi
    
    # API version endpoint
    ((total_checks++))
    if ! check_endpoint "/api/version" "200" "API Version"; then
        ((failed_checks++))
    fi
    
    # Process check
    ((total_checks++))
    if ! check_process "uvicorn" "Uvicorn Process"; then
        ((failed_checks++))
    fi
    
    # Non-critical health checks (warnings only)
    log "🔍 Running non-critical health checks..."
    
    # Database health (non-critical for container health)
    ((total_checks++))
    if ! check_endpoint "/health/database" "200" "Database Health"; then
        warn "Database health check failed (non-critical)"
    fi
    
    # Cache health (non-critical for container health)
    ((total_checks++))
    if ! check_endpoint "/health/cache" "200" "Cache Health"; then
        warn "Cache health check failed (non-critical)"
    fi
    
    # Metrics endpoint (non-critical)
    ((total_checks++))
    if ! check_endpoint "/metrics" "200" "Metrics Endpoint"; then
        warn "Metrics endpoint check failed (non-critical)"
    fi
    
    # Resource checks (warnings only)
    check_memory 90
    check_disk 85 "/app"
    
    # Summary
    log "📊 Health check summary:"
    log "Total checks: $total_checks"
    log "Failed critical checks: $failed_checks"
    
    if [ $failed_checks -eq 0 ]; then
        log "✅ All critical health checks passed!"
        exit 0
    else
        error "❌ $failed_checks critical health check(s) failed!"
        exit 1
    fi
}

# Handle signals
trap 'error "Health check interrupted"; exit 1' INT TERM

# Run main function
main "$@"
