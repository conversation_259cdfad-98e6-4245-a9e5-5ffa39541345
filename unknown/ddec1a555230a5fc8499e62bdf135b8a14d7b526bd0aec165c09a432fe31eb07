"""
⚠️ Risk Manager

Comprehensive risk management framework for live trading with position sizing,
risk limits enforcement, portfolio monitoring, and emergency stop mechanisms.
"""

import asyncio
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import SignalType, OrderType, OrderStatus, TradeStatus, SignalData
from ...database.models import Order, Trade, Portfolio, Signal
from ...features.data_pipeline import DataAggregator
from ...features.signal_processing import RiskAssessor

logger = get_logger(__name__)


class RiskLevel(str, Enum):
    """Risk level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskAction(str, Enum):
    """Risk management actions"""
    ALLOW = "allow"
    REDUCE_SIZE = "reduce_size"
    BLOCK = "block"
    EMERGENCY_STOP = "emergency_stop"


class RiskManager:
    """
    ⚠️ Risk Manager
    
    Manages trading risk with:
    - Position sizing algorithms
    - Risk limits enforcement
    - Portfolio risk monitoring
    - Emergency stop mechanisms
    - Real-time risk assessment
    - Automated risk controls
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        self.risk_assessor = RiskAssessor()
        
        # Risk parameters
        self.max_portfolio_risk = Decimal("0.02")  # 2% max portfolio risk per trade
        self.max_position_size = Decimal("0.10")   # 10% max position size
        self.max_daily_loss = Decimal("0.05")      # 5% max daily loss
        self.max_drawdown = Decimal("0.15")        # 15% max drawdown
        self.correlation_limit = Decimal("0.70")   # 70% max correlation
        
        # Emergency stop conditions
        self.emergency_stop_active = False
        self.emergency_stop_reason = None
        
        # Risk monitoring
        self.risk_monitors: Dict[str, asyncio.Task] = {}
    
    async def assess_trade_risk(
        self,
        portfolio_id: str,
        token_address: str,
        side: SignalType,
        quantity: Decimal,
        price: Decimal,
        signal_data: Optional[SignalData] = None
    ) -> Dict[str, Any]:
        """
        Assess risk for a proposed trade
        
        Args:
            portfolio_id: Portfolio ID
            token_address: Token address
            side: Trade side (BUY/SELL)
            quantity: Trade quantity
            price: Trade price
            signal_data: Signal data if available
            
        Returns:
            Risk assessment results
        """
        try:
            self.logger.info(f"Assessing trade risk: {side} {quantity} {token_address}")
            
            # Check emergency stop
            if self.emergency_stop_active:
                return {
                    "risk_level": RiskLevel.CRITICAL,
                    "action": RiskAction.EMERGENCY_STOP,
                    "reason": f"Emergency stop active: {self.emergency_stop_reason}",
                    "allowed": False
                }
            
            # Get portfolio data
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio:
                return {
                    "risk_level": RiskLevel.CRITICAL,
                    "action": RiskAction.BLOCK,
                    "reason": "Portfolio not found",
                    "allowed": False
                }
            
            # Calculate trade value
            trade_value = quantity * price
            
            # Assess individual risk components
            position_risk = await self._assess_position_risk(portfolio, token_address, trade_value, side)
            portfolio_risk = await self._assess_portfolio_risk(portfolio, trade_value)
            correlation_risk = await self._assess_correlation_risk(portfolio, token_address)
            liquidity_risk = await self._assess_liquidity_risk(token_address, trade_value)
            
            # Calculate overall risk score
            risk_scores = [
                position_risk["score"],
                portfolio_risk["score"],
                correlation_risk["score"],
                liquidity_risk["score"]
            ]
            overall_risk_score = sum(risk_scores) / len(risk_scores)
            
            # Determine risk level and action
            risk_level, action = self._determine_risk_action(overall_risk_score)
            
            # Calculate recommended position size
            recommended_size = await self._calculate_position_size(
                portfolio, token_address, trade_value, overall_risk_score
            )
            
            return {
                "risk_level": risk_level,
                "action": action,
                "overall_risk_score": overall_risk_score,
                "position_risk": position_risk,
                "portfolio_risk": portfolio_risk,
                "correlation_risk": correlation_risk,
                "liquidity_risk": liquidity_risk,
                "recommended_size": recommended_size,
                "max_position_value": recommended_size * price,
                "allowed": action in [RiskAction.ALLOW, RiskAction.REDUCE_SIZE],
                "reason": self._get_risk_reason(risk_level, action, risk_scores)
            }
            
        except Exception as e:
            self.logger.error(f"Error assessing trade risk: {str(e)}")
            return {
                "risk_level": RiskLevel.CRITICAL,
                "action": RiskAction.BLOCK,
                "reason": f"Risk assessment error: {str(e)}",
                "allowed": False
            }
    
    async def calculate_position_size(
        self,
        portfolio_id: str,
        token_address: str,
        signal_confidence: float,
        risk_score: float,
        max_risk_per_trade: Optional[Decimal] = None
    ) -> Decimal:
        """
        Calculate optimal position size based on risk parameters
        
        Args:
            portfolio_id: Portfolio ID
            token_address: Token address
            signal_confidence: Signal confidence (0-1)
            risk_score: Token risk score (0-1)
            max_risk_per_trade: Maximum risk per trade override
            
        Returns:
            Recommended position size in USD
        """
        try:
            # Get portfolio data
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio:
                return Decimal("0")
            
            # Use provided max risk or default
            max_risk = max_risk_per_trade or self.max_portfolio_risk
            
            # Base position size (percentage of portfolio)
            base_size = portfolio.total_value * max_risk
            
            # Adjust for signal confidence
            confidence_multiplier = Decimal(str(signal_confidence))
            
            # Adjust for risk score (inverse relationship)
            risk_multiplier = Decimal("1") - Decimal(str(risk_score))
            
            # Calculate final position size
            position_size = base_size * confidence_multiplier * risk_multiplier
            
            # Apply maximum position size limit
            max_position_value = portfolio.total_value * self.max_position_size
            position_size = min(position_size, max_position_value)
            
            # Ensure minimum viable position
            min_position = Decimal("10")  # $10 minimum
            position_size = max(position_size, min_position)
            
            self.logger.info(f"Calculated position size: ${position_size}")
            return position_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return Decimal("0")
    
    async def check_risk_limits(self, portfolio_id: str) -> Dict[str, Any]:
        """
        Check if portfolio is within risk limits
        
        Args:
            portfolio_id: Portfolio ID
            
        Returns:
            Risk limits check results
        """
        try:
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio:
                return {"within_limits": False, "reason": "Portfolio not found"}
            
            # Check daily loss limit
            daily_pnl = await self._calculate_daily_pnl(portfolio_id)
            daily_loss_pct = abs(daily_pnl) / portfolio.total_value if portfolio.total_value > 0 else Decimal("0")
            
            if daily_pnl < 0 and daily_loss_pct > self.max_daily_loss:
                return {
                    "within_limits": False,
                    "reason": f"Daily loss limit exceeded: {daily_loss_pct:.2%} > {self.max_daily_loss:.2%}",
                    "daily_loss_pct": daily_loss_pct,
                    "limit_exceeded": "daily_loss"
                }
            
            # Check maximum drawdown
            max_drawdown_pct = await self._calculate_max_drawdown(portfolio_id)
            if max_drawdown_pct > self.max_drawdown:
                return {
                    "within_limits": False,
                    "reason": f"Maximum drawdown exceeded: {max_drawdown_pct:.2%} > {self.max_drawdown:.2%}",
                    "max_drawdown_pct": max_drawdown_pct,
                    "limit_exceeded": "max_drawdown"
                }
            
            # Check position concentration
            concentration_risk = await self._check_position_concentration(portfolio_id)
            if concentration_risk["risk_level"] == RiskLevel.CRITICAL:
                return {
                    "within_limits": False,
                    "reason": f"Position concentration too high: {concentration_risk['reason']}",
                    "limit_exceeded": "concentration"
                }
            
            return {
                "within_limits": True,
                "daily_loss_pct": daily_loss_pct,
                "max_drawdown_pct": max_drawdown_pct,
                "concentration_risk": concentration_risk
            }
            
        except Exception as e:
            self.logger.error(f"Error checking risk limits: {str(e)}")
            return {"within_limits": False, "reason": f"Error: {str(e)}"}
    
    async def trigger_emergency_stop(self, reason: str, portfolio_id: Optional[str] = None):
        """
        Trigger emergency stop mechanism
        
        Args:
            reason: Reason for emergency stop
            portfolio_id: Specific portfolio ID or None for global stop
        """
        try:
            self.logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
            
            self.emergency_stop_active = True
            self.emergency_stop_reason = reason
            
            # Cancel all pending orders
            if portfolio_id:
                await self._cancel_portfolio_orders(portfolio_id, reason)
            else:
                await self._cancel_all_orders(reason)
            
            # Log emergency stop
            self.logger.critical(f"Emergency stop completed: {reason}")
            
        except Exception as e:
            self.logger.error(f"Error triggering emergency stop: {str(e)}")
    
    async def clear_emergency_stop(self):
        """Clear emergency stop condition"""
        try:
            self.logger.info("Clearing emergency stop")
            self.emergency_stop_active = False
            self.emergency_stop_reason = None
            
        except Exception as e:
            self.logger.error(f"Error clearing emergency stop: {str(e)}")

    async def _assess_position_risk(
        self,
        portfolio: Portfolio,
        token_address: str,
        trade_value: Decimal,
        side: SignalType
    ) -> Dict[str, Any]:
        """Assess position-specific risk"""
        try:
            # Get current position
            current_positions = portfolio.positions or {}
            current_position = current_positions.get(token_address, {})
            current_value = current_position.get("value_usd", Decimal("0"))

            # Calculate new position value
            if side == SignalType.BUY:
                new_position_value = current_value + trade_value
            else:  # SELL
                new_position_value = max(Decimal("0"), current_value - trade_value)

            # Calculate position percentage
            position_pct = new_position_value / portfolio.total_value if portfolio.total_value > 0 else Decimal("0")

            # Assess risk based on position size
            if position_pct > self.max_position_size:
                risk_score = 0.9
                risk_level = RiskLevel.HIGH
            elif position_pct > self.max_position_size * Decimal("0.8"):
                risk_score = 0.7
                risk_level = RiskLevel.MEDIUM
            else:
                risk_score = 0.3
                risk_level = RiskLevel.LOW

            return {
                "score": risk_score,
                "level": risk_level,
                "position_pct": position_pct,
                "new_position_value": new_position_value,
                "reason": f"Position size: {position_pct:.2%} of portfolio"
            }

        except Exception as e:
            self.logger.error(f"Error assessing position risk: {str(e)}")
            return {"score": 0.9, "level": RiskLevel.HIGH, "reason": "Assessment error"}

    async def _assess_portfolio_risk(self, portfolio: Portfolio, trade_value: Decimal) -> Dict[str, Any]:
        """Assess portfolio-level risk"""
        try:
            # Calculate trade risk as percentage of portfolio
            trade_risk_pct = trade_value / portfolio.total_value if portfolio.total_value > 0 else Decimal("1")

            # Assess based on trade size relative to portfolio
            if trade_risk_pct > self.max_portfolio_risk * 2:
                risk_score = 0.9
                risk_level = RiskLevel.HIGH
            elif trade_risk_pct > self.max_portfolio_risk:
                risk_score = 0.6
                risk_level = RiskLevel.MEDIUM
            else:
                risk_score = 0.2
                risk_level = RiskLevel.LOW

            return {
                "score": risk_score,
                "level": risk_level,
                "trade_risk_pct": trade_risk_pct,
                "reason": f"Trade risk: {trade_risk_pct:.2%} of portfolio"
            }

        except Exception as e:
            self.logger.error(f"Error assessing portfolio risk: {str(e)}")
            return {"score": 0.8, "level": RiskLevel.HIGH, "reason": "Assessment error"}

    async def _assess_correlation_risk(self, portfolio: Portfolio, token_address: str) -> Dict[str, Any]:
        """Assess correlation risk with existing positions"""
        try:
            # This is a simplified correlation assessment
            # In a full implementation, this would analyze price correlations
            current_positions = portfolio.positions or {}

            if len(current_positions) == 0:
                return {"score": 0.1, "level": RiskLevel.LOW, "reason": "No existing positions"}

            # For now, assume moderate correlation risk if multiple positions exist
            if len(current_positions) > 5:
                risk_score = 0.6
                risk_level = RiskLevel.MEDIUM
            else:
                risk_score = 0.3
                risk_level = RiskLevel.LOW

            return {
                "score": risk_score,
                "level": risk_level,
                "position_count": len(current_positions),
                "reason": f"Portfolio has {len(current_positions)} positions"
            }

        except Exception as e:
            self.logger.error(f"Error assessing correlation risk: {str(e)}")
            return {"score": 0.5, "level": RiskLevel.MEDIUM, "reason": "Assessment error"}

    async def _assess_liquidity_risk(self, token_address: str, trade_value: Decimal) -> Dict[str, Any]:
        """Assess liquidity risk for the token"""
        try:
            # Get token market data
            market_data = await self.data_aggregator.get_token_data(token_address)
            if not market_data:
                return {"score": 0.9, "level": RiskLevel.HIGH, "reason": "No market data available"}

            # Assess liquidity based on volume and market cap
            daily_volume = market_data.volume_24h or Decimal("0")
            market_cap = market_data.market_cap or Decimal("0")

            # Calculate trade impact
            volume_impact = trade_value / daily_volume if daily_volume > 0 else Decimal("1")

            if volume_impact > Decimal("0.1"):  # Trade > 10% of daily volume
                risk_score = 0.9
                risk_level = RiskLevel.HIGH
            elif volume_impact > Decimal("0.05"):  # Trade > 5% of daily volume
                risk_score = 0.6
                risk_level = RiskLevel.MEDIUM
            else:
                risk_score = 0.2
                risk_level = RiskLevel.LOW

            return {
                "score": risk_score,
                "level": risk_level,
                "volume_impact": volume_impact,
                "daily_volume": daily_volume,
                "reason": f"Trade impact: {volume_impact:.2%} of daily volume"
            }

        except Exception as e:
            self.logger.error(f"Error assessing liquidity risk: {str(e)}")
            return {"score": 0.7, "level": RiskLevel.MEDIUM, "reason": "Assessment error"}

    def _determine_risk_action(self, risk_score: float) -> Tuple[RiskLevel, RiskAction]:
        """Determine risk level and recommended action"""
        if risk_score >= 0.8:
            return RiskLevel.CRITICAL, RiskAction.BLOCK
        elif risk_score >= 0.6:
            return RiskLevel.HIGH, RiskAction.REDUCE_SIZE
        elif risk_score >= 0.4:
            return RiskLevel.MEDIUM, RiskAction.REDUCE_SIZE
        else:
            return RiskLevel.LOW, RiskAction.ALLOW

    def _get_risk_reason(self, risk_level: RiskLevel, action: RiskAction, risk_scores: List[float]) -> str:
        """Generate human-readable risk reason"""
        avg_score = sum(risk_scores) / len(risk_scores)

        if action == RiskAction.BLOCK:
            return f"Trade blocked due to {risk_level.value} risk (score: {avg_score:.2f})"
        elif action == RiskAction.REDUCE_SIZE:
            return f"Position size reduction recommended due to {risk_level.value} risk (score: {avg_score:.2f})"
        else:
            return f"Trade approved with {risk_level.value} risk (score: {avg_score:.2f})"

    async def _calculate_position_size(
        self,
        portfolio: Portfolio,
        token_address: str,
        trade_value: Decimal,
        risk_score: float
    ) -> Decimal:
        """Calculate recommended position size based on risk"""
        try:
            # Start with requested trade value
            recommended_size = trade_value

            # Reduce size based on risk score
            if risk_score >= 0.6:
                # High risk: reduce to 50% of requested size
                recommended_size = trade_value * Decimal("0.5")
            elif risk_score >= 0.4:
                # Medium risk: reduce to 75% of requested size
                recommended_size = trade_value * Decimal("0.75")

            # Ensure within position limits
            max_position_value = portfolio.total_value * self.max_position_size
            recommended_size = min(recommended_size, max_position_value)

            return recommended_size

        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return Decimal("0")

    async def _calculate_daily_pnl(self, portfolio_id: str) -> Decimal:
        """Calculate daily P&L for portfolio"""
        try:
            today = datetime.utcnow().date()
            trades = await Trade.find({
                "portfolio_id": portfolio_id,
                "executed_at": {
                    "$gte": datetime.combine(today, datetime.min.time()),
                    "$lt": datetime.combine(today + timedelta(days=1), datetime.min.time())
                }
            }).to_list()

            daily_pnl = sum(trade.pnl_usd for trade in trades if trade.pnl_usd)
            return daily_pnl

        except Exception as e:
            self.logger.error(f"Error calculating daily P&L: {str(e)}")
            return Decimal("0")

    async def _calculate_max_drawdown(self, portfolio_id: str) -> Decimal:
        """Calculate maximum drawdown for portfolio"""
        try:
            # This is a simplified calculation
            # In a full implementation, this would track historical portfolio values
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio:
                return Decimal("0")

            # For now, use total P&L as proxy for drawdown
            total_pnl_pct = portfolio.total_pnl / portfolio.initial_balance if portfolio.initial_balance > 0 else Decimal("0")

            # Return absolute value if negative (drawdown)
            return abs(min(Decimal("0"), total_pnl_pct))

        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {str(e)}")
            return Decimal("0")

    async def _check_position_concentration(self, portfolio_id: str) -> Dict[str, Any]:
        """Check position concentration risk"""
        try:
            portfolio = await Portfolio.get(portfolio_id)
            if not portfolio or not portfolio.positions:
                return {"risk_level": RiskLevel.LOW, "reason": "No positions"}

            # Calculate largest position percentage
            total_value = portfolio.total_value
            max_position_pct = Decimal("0")

            for position in portfolio.positions.values():
                position_value = position.get("value_usd", Decimal("0"))
                position_pct = position_value / total_value if total_value > 0 else Decimal("0")
                max_position_pct = max(max_position_pct, position_pct)

            if max_position_pct > self.max_position_size:
                return {
                    "risk_level": RiskLevel.CRITICAL,
                    "reason": f"Largest position: {max_position_pct:.2%} > {self.max_position_size:.2%}"
                }
            elif max_position_pct > self.max_position_size * Decimal("0.8"):
                return {
                    "risk_level": RiskLevel.HIGH,
                    "reason": f"Largest position: {max_position_pct:.2%}"
                }
            else:
                return {
                    "risk_level": RiskLevel.LOW,
                    "reason": f"Largest position: {max_position_pct:.2%}"
                }

        except Exception as e:
            self.logger.error(f"Error checking position concentration: {str(e)}")
            return {"risk_level": RiskLevel.MEDIUM, "reason": "Assessment error"}

    async def _cancel_portfolio_orders(self, portfolio_id: str, reason: str):
        """Cancel all orders for a specific portfolio"""
        try:
            orders = await Order.find({
                "portfolio_id": portfolio_id,
                "status": {"$in": [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]}
            }).to_list()

            for order in orders:
                order.status = OrderStatus.CANCELLED
                order.cancelled_at = datetime.utcnow()
                order.cancellation_reason = reason
                await order.save()

            self.logger.info(f"Cancelled {len(orders)} orders for portfolio {portfolio_id}")

        except Exception as e:
            self.logger.error(f"Error cancelling portfolio orders: {str(e)}")

    async def _cancel_all_orders(self, reason: str):
        """Cancel all active orders"""
        try:
            orders = await Order.find({
                "status": {"$in": [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]}
            }).to_list()

            for order in orders:
                order.status = OrderStatus.CANCELLED
                order.cancelled_at = datetime.utcnow()
                order.cancellation_reason = reason
                await order.save()

            self.logger.info(f"Cancelled {len(orders)} orders globally")

        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {str(e)}")
