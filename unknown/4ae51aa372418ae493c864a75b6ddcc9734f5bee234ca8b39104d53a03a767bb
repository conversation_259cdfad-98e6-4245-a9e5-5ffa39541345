"""
📈 Advanced Analytics Module

Machine learning models, advanced metrics, and market analysis
for enhanced trading intelligence and performance optimization.
"""

from .ml_models import MLModelManager, PricePredictionModel, PatternRecognitionModel
from .sentiment_analyzer import SentimentAnalyzer
from .market_regime_detector import MarketRegimeDetector
from .advanced_metrics import AdvancedMetricsCalculator
from .market_analyzer import MarketAnalyzer
from .correlation_analyzer import CorrelationAnalyzer
from .routes import router

__all__ = [
    "MLModelManager",
    "PricePredictionModel",
    "PatternRecognitionModel",
    "SentimentAnalyzer",
    "MarketRegimeDetector",
    "AdvancedMetricsCalculator",
    "MarketAnalyzer",
    "CorrelationAnalyzer",
    "router"
]
