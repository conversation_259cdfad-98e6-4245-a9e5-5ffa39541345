apiVersion: apps/v1
kind: Deployment
metadata:
  name: tokentracker-v2
  namespace: tokentracker-production
  labels:
    app: tokentracker-v2
    version: blue
    environment: production
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "TokenTracker V2 Production Deployment"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: tokentracker-v2
      version: blue
  template:
    metadata:
      labels:
        app: tokentracker-v2
        version: blue
        environment: production
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: tokentracker-v2
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: tokentracker-v2
        image: ghcr.io/your-org/tokentracker-v2:IMAGE_TAG
        imagePullPolicy: Always
        ports:
        - name: http
          containerPort: 8000
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8000"
        - name: PYTHONPATH
          value: "/app/src"
        envFrom:
        - configMapRef:
            name: tokentracker-v2-config
        - secretRef:
            name: tokentracker-v2-secrets
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 4Gi
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          successThreshold: 1
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          successThreshold: 1
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
          successThreshold: 1
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: app-cache
          mountPath: /app/cache
        - name: tmp
          mountPath: /tmp
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: app-logs
        emptyDir:
          sizeLimit: 1Gi
      - name: app-cache
        emptyDir:
          sizeLimit: 2Gi
      - name: tmp
        emptyDir:
          sizeLimit: 500Mi
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - tokentracker-v2
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tokentracker-v2
  namespace: tokentracker-production
  labels:
    app: tokentracker-v2
    environment: production
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tokentracker-v2-pdb
  namespace: tokentracker-production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: tokentracker-v2
