"""
Third-Party Integrations Feature Module

This module provides comprehensive third-party integrations for TokenTracker V2,
including exchange APIs, additional data providers, and external services.

Features:
- Centralized exchange integrations (Binance, Coinbase, FTX, etc.)
- Additional data providers (CoinGecko, CoinMarketCap, etc.)
- Portfolio synchronization across platforms
- Cross-platform trading capabilities
- Arbitrage opportunity detection
- News and sentiment data integration
- Social media sentiment analysis

Following v2.INSTRUCTIONS:
- Feature-based modular architecture
- Clean separation of concerns
- Security-first design
- Performance optimization
- Comprehensive testing
"""

from .exchange_integrator import ExchangeIntegrator
from .data_provider_manager import DataProviderManager
from .portfolio_sync_service import PortfolioSyncService
from .arbitrage_detector import ArbitrageDetector
from .news_sentiment_service import NewsSentimentService

__all__ = [
    'ExchangeIntegrator',
    'DataProviderManager',
    'PortfolioSyncService',
    'ArbitrageDetector',
    'NewsSentimentService'
]
