"""
📈 Analytics API Routes

FastAPI routes for advanced analytics including ML models, sentiment analysis,
market regime detection, advanced metrics, and correlation analysis.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from .ml_models import MLModelManager, ModelType
from .sentiment_analyzer import <PERSON>timentAnalyzer, SentimentSource
from .market_regime_detector import MarketRegimeDetector, MarketRegime
from .advanced_metrics import AdvancedMetricsCalculator, MetricType, BenchmarkType
from .market_analyzer import MarketAnalyzer, SectorType
from .correlation_analyzer import CorrelationAnalyzer, CorrelationType

logger = get_logger(__name__)
router = APIRouter()

# Initialize analytics components
ml_manager = MLModelManager()
sentiment_analyzer = SentimentAnalyzer()
regime_detector = MarketRegimeDetector()
metrics_calculator = AdvancedMetricsCalculator()
market_analyzer = MarketAnalyzer()
correlation_analyzer = CorrelationAnalyzer()


# Request/Response Models
class TrainModelRequest(BaseModel):
    model_id: str = Field(..., description="Model ID to train")
    token_address: Optional[str] = Field(None, description="Token address for training")
    training_period_days: int = Field(30, description="Training period in days")


class PredictPriceRequest(BaseModel):
    token_address: str = Field(..., description="Token address")
    prediction_horizon: int = Field(24, description="Prediction horizon in hours")


class SentimentAnalysisRequest(BaseModel):
    token_address: str = Field(..., description="Token address")
    time_window_hours: int = Field(24, description="Time window for analysis")
    sources: Optional[List[SentimentSource]] = Field(None, description="Sentiment sources")


class AdvancedMetricsRequest(BaseModel):
    portfolio_id: str = Field(..., description="Portfolio ID")
    benchmark: BenchmarkType = Field(BenchmarkType.SOL, description="Benchmark for comparison")
    period_days: int = Field(90, description="Analysis period in days")


# ML Models Routes
@router.post("/ml/train", response_model=Dict[str, Any])
async def train_model(request: TrainModelRequest):
    """Train a machine learning model"""
    try:
        result = await ml_manager.train_model(
            model_id=request.model_id,
            token_address=request.token_address,
            training_period_days=request.training_period_days
        )
        
        return {"success": True, "training_result": result}
        
    except Exception as e:
        logger.error(f"Error training model: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ml/predict/price", response_model=Dict[str, Any])
async def predict_price(request: PredictPriceRequest):
    """Get price prediction for a token"""
    try:
        prediction = await ml_manager.predict_price(
            token_address=request.token_address,
            prediction_horizon=request.prediction_horizon
        )
        
        return {"success": True, "prediction": prediction}
        
    except Exception as e:
        logger.error(f"Error predicting price: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ml/patterns/{token_address}", response_model=Dict[str, Any])
async def detect_patterns(token_address: str):
    """Detect patterns for a token"""
    try:
        patterns = await ml_manager.detect_patterns(token_address)
        return {"success": True, "patterns": patterns}
        
    except Exception as e:
        logger.error(f"Error detecting patterns: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/ml/models/{model_id}/status", response_model=Dict[str, Any])
async def get_model_status(model_id: str):
    """Get status of a specific model"""
    try:
        status = await ml_manager.get_model_status(model_id)
        return {"success": True, "model_status": status}
        
    except Exception as e:
        logger.error(f"Error getting model status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Sentiment Analysis Routes
@router.post("/sentiment/analyze", response_model=Dict[str, Any])
async def analyze_sentiment(request: SentimentAnalysisRequest):
    """Analyze sentiment for a token"""
    try:
        sentiment = await sentiment_analyzer.analyze_token_sentiment(
            token_address=request.token_address,
            time_window_hours=request.time_window_hours,
            sources=request.sources
        )
        
        return {"success": True, "sentiment_analysis": sentiment}
        
    except Exception as e:
        logger.error(f"Error analyzing sentiment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/sentiment/text", response_model=Dict[str, Any])
async def analyze_text_sentiment(text: str = Body(...)):
    """Analyze sentiment of a text"""
    try:
        sentiment = await sentiment_analyzer.analyze_text_sentiment(text)
        return {"success": True, "sentiment": sentiment}
        
    except Exception as e:
        logger.error(f"Error analyzing text sentiment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sentiment/market-overview", response_model=Dict[str, Any])
async def get_market_sentiment_overview():
    """Get overall market sentiment overview"""
    try:
        overview = await sentiment_analyzer.get_market_sentiment_overview()
        return {"success": True, "market_sentiment": overview}
        
    except Exception as e:
        logger.error(f"Error getting market sentiment overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Market Regime Detection Routes
@router.get("/regime/{token_address}", response_model=Dict[str, Any])
async def detect_market_regime(
    token_address: str,
    timeframes: Optional[List[str]] = Query(None)
):
    """Detect market regime for a token"""
    try:
        regime = await regime_detector.detect_market_regime(
            token_address=token_address,
            timeframes=timeframes
        )
        
        return {"success": True, "market_regime": regime}
        
    except Exception as e:
        logger.error(f"Error detecting market regime: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/regime/{token_address}/history", response_model=Dict[str, Any])
async def get_regime_history(
    token_address: str,
    days: int = Query(30, description="Number of days for history")
):
    """Get historical regime analysis"""
    try:
        history = await regime_detector.get_regime_history(
            token_address=token_address,
            days=days
        )
        
        return {"success": True, "regime_history": history}
        
    except Exception as e:
        logger.error(f"Error getting regime history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Advanced Metrics Routes
@router.post("/metrics/advanced", response_model=Dict[str, Any])
async def calculate_advanced_metrics(request: AdvancedMetricsRequest):
    """Calculate all advanced metrics for a portfolio"""
    try:
        metrics = await metrics_calculator.calculate_all_metrics(
            portfolio_id=request.portfolio_id,
            benchmark=request.benchmark,
            period_days=request.period_days
        )
        
        return {"success": True, "advanced_metrics": metrics}
        
    except Exception as e:
        logger.error(f"Error calculating advanced metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics/{portfolio_id}/{metric_type}", response_model=Dict[str, Any])
async def calculate_specific_metric(
    portfolio_id: str,
    metric_type: MetricType,
    benchmark: BenchmarkType = Query(BenchmarkType.SOL),
    period_days: int = Query(90)
):
    """Calculate a specific metric"""
    try:
        metric = await metrics_calculator.calculate_metric(
            portfolio_id=portfolio_id,
            metric_type=metric_type,
            benchmark=benchmark,
            period_days=period_days
        )
        
        return {"success": True, "metric": metric}
        
    except Exception as e:
        logger.error(f"Error calculating metric: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Market Analysis Routes
@router.get("/market/overview", response_model=Dict[str, Any])
async def get_market_overview():
    """Get comprehensive market overview"""
    try:
        overview = await market_analyzer.analyze_market_overview()
        return {"success": True, "market_overview": overview}
        
    except Exception as e:
        logger.error(f"Error getting market overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market/sector/{sector}", response_model=Dict[str, Any])
async def analyze_sector_performance(sector: SectorType):
    """Analyze performance of a specific sector"""
    try:
        analysis = await market_analyzer.analyze_sector_performance(sector)
        return {"success": True, "sector_analysis": analysis}
        
    except Exception as e:
        logger.error(f"Error analyzing sector performance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market/liquidity/{token_address}", response_model=Dict[str, Any])
async def analyze_token_liquidity(token_address: str):
    """Analyze liquidity for a specific token"""
    try:
        liquidity = await market_analyzer.analyze_token_liquidity(token_address)
        return {"success": True, "liquidity_analysis": liquidity}
        
    except Exception as e:
        logger.error(f"Error analyzing token liquidity: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market/microstructure/{token_address}", response_model=Dict[str, Any])
async def analyze_market_microstructure(token_address: str):
    """Analyze market microstructure for a token"""
    try:
        microstructure = await market_analyzer.analyze_market_microstructure(token_address)
        return {"success": True, "microstructure_analysis": microstructure}
        
    except Exception as e:
        logger.error(f"Error analyzing market microstructure: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Correlation Analysis Routes
@router.get("/correlation/portfolio/{portfolio_id}", response_model=Dict[str, Any])
async def analyze_portfolio_correlations(
    portfolio_id: str,
    analysis_window_days: int = Query(30)
):
    """Analyze correlations within a portfolio"""
    try:
        correlations = await correlation_analyzer.analyze_portfolio_correlations(
            portfolio_id=portfolio_id,
            analysis_window_days=analysis_window_days
        )
        
        return {"success": True, "portfolio_correlations": correlations}
        
    except Exception as e:
        logger.error(f"Error analyzing portfolio correlations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/correlation/tokens", response_model=Dict[str, Any])
async def analyze_token_correlations(
    token_addresses: List[str] = Body(...),
    correlation_type: CorrelationType = Body(CorrelationType.PEARSON),
    analysis_window_days: int = Body(30)
):
    """Analyze correlations between specific tokens"""
    try:
        correlations = await correlation_analyzer.analyze_token_correlations(
            token_addresses=token_addresses,
            correlation_type=correlation_type,
            analysis_window_days=analysis_window_days
        )
        
        return {"success": True, "token_correlations": correlations}
        
    except Exception as e:
        logger.error(f"Error analyzing token correlations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/correlation/heatmap", response_model=Dict[str, Any])
async def get_correlation_heatmap(
    token_addresses: List[str] = Body(...),
    analysis_window_days: int = Body(30)
):
    """Get correlation data for heatmap visualization"""
    try:
        heatmap_data = await correlation_analyzer.get_correlation_heatmap_data(
            token_addresses=token_addresses,
            analysis_window_days=analysis_window_days
        )
        
        return {"success": True, "heatmap_data": heatmap_data}
        
    except Exception as e:
        logger.error(f"Error getting correlation heatmap data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/correlation/uncorrelated", response_model=Dict[str, Any])
async def find_uncorrelated_tokens(
    base_tokens: List[str] = Body(...),
    candidate_tokens: List[str] = Body(...),
    max_correlation: float = Body(0.3),
    analysis_window_days: int = Body(30)
):
    """Find tokens with low correlation to base tokens"""
    try:
        uncorrelated = await correlation_analyzer.find_uncorrelated_tokens(
            base_tokens=base_tokens,
            candidate_tokens=candidate_tokens,
            max_correlation=max_correlation,
            analysis_window_days=analysis_window_days
        )
        
        return {"success": True, "uncorrelated_analysis": uncorrelated}
        
    except Exception as e:
        logger.error(f"Error finding uncorrelated tokens: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
