# 📊 TokenTracker V2 - Monitoring & Observability

Comprehensive monitoring and observability system for TokenTracker V2 with metrics collection, health monitoring, alerting, and performance profiling.

## 🎯 Overview

The monitoring system provides:
- **Real-time metrics collection** with Prometheus integration
- **Health monitoring** for all services and dependencies
- **Intelligent alerting** with threshold-based and anomaly detection
- **Performance profiling** for requests, database operations, and system resources
- **Centralized log aggregation** with pattern detection and analysis
- **Grafana dashboards** for visualization and monitoring

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Application    │    │   Monitoring    │    │   External      │
│                 │    │     System      │    │   Systems       │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   API       │─┼────┼─│ Metrics     │ │    │ │ Prometheus  │ │
│ │ Endpoints   │ │    │ │ Collector   │─┼────┼─│             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Business    │─┼────┼─│ Health      │ │    │ │   Grafana   │ │
│ │   Logic     │ │    │ │ Monitor     │─┼────┼─│             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Database    │─┼────┼─│ Alert       │ │    │ │ Alertmanager│ │
│ │ Operations  │ │    │ │ Manager     │─┼────┼─│             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📊 Metrics Collection

### System Metrics
- **CPU Usage**: Real-time CPU utilization percentage
- **Memory Usage**: Memory consumption in bytes and percentage
- **Disk Usage**: Disk utilization and I/O operations
- **Network**: Bytes sent/received and active connections

### Business Metrics
- **Trading Performance**: Success rate, volume, P&L
- **Signal Accuracy**: Signal generation and accuracy rates
- **Portfolio Metrics**: Value, performance, positions
- **Token Analysis**: Analysis speed and throughput

### Application Metrics
- **HTTP Requests**: Rate, duration, status codes
- **Database Queries**: Query rate, duration, operations
- **Cache Performance**: Hit rate, operations
- **External APIs**: Response times, error rates

## 🏥 Health Monitoring

### Service Health Checks
- **Database**: MongoDB connection and query performance
- **Cache**: Redis availability and response times
- **APIs**: Internal API endpoint availability
- **Dependencies**: External service health

### Health Endpoints
- `GET /health` - Overall system health
- `GET /ready` - Kubernetes readiness probe
- `GET /metrics` - Prometheus metrics endpoint

### Health Status Levels
- **Healthy**: All services operational
- **Degraded**: Some non-critical services down
- **Unhealthy**: Critical services unavailable

## 🚨 Alert Management

### Default Alert Rules

#### System Alerts
- **High CPU Usage**: CPU > 80% for 5 minutes
- **High Memory Usage**: Memory > 85% for 5 minutes
- **High Disk Usage**: Disk > 90% for 5 minutes
- **Database Connection Failed**: Database unavailable

#### Application Alerts
- **High Error Rate**: Error rate > 1% for 10 minutes
- **Slow Response Time**: Average response > 1s for 15 minutes
- **External API Failure**: External API unavailable
- **Low Signal Accuracy**: Signal accuracy < 70% for 30 minutes

### Alert Severity Levels
- **Critical**: Immediate attention required
- **High**: Urgent but not critical
- **Medium**: Important but not urgent
- **Low**: Informational
- **Info**: General notifications

### Alert Features
- **Rate Limiting**: Prevent alert spam
- **Escalation**: Automatic severity escalation
- **Suppression**: Temporary alert suppression
- **Acknowledgment**: Manual alert acknowledgment

## ⚡ Performance Profiling

### Request Profiling
- **Duration**: Request processing time
- **Memory Usage**: Memory consumption per request
- **CPU Usage**: CPU utilization per request
- **Database Queries**: Query count and time per request

### Database Profiling
- **Query Duration**: Individual query performance
- **Operation Types**: Find, insert, update, delete operations
- **Collection Performance**: Per-collection metrics
- **Slow Query Detection**: Automatic bottleneck identification

### Memory Profiling
- **Memory Snapshots**: Point-in-time memory analysis
- **Memory Leaks**: Automatic leak detection
- **Garbage Collection**: GC performance metrics
- **Memory Growth**: Memory usage trends

### CPU Profiling
- **Function-Level Profiling**: Detailed CPU usage analysis
- **Hot Spots**: CPU-intensive code identification
- **Performance Bottlenecks**: Automatic bottleneck detection

## 📝 Log Aggregation

### Log Collection
- **Centralized Logging**: All application logs in one place
- **Structured Logging**: JSON-formatted logs with metadata
- **Log Levels**: Trace, Debug, Info, Warning, Error, Critical
- **Correlation IDs**: Request tracing across services

### Pattern Detection
- **Error Patterns**: Automatic error pattern recognition
- **Anomaly Detection**: Unusual log pattern identification
- **Threshold Alerts**: Pattern frequency alerts
- **Custom Patterns**: User-defined pattern rules

### Log Analysis
- **Search & Filter**: Advanced log querying
- **Error Correlation**: Related error grouping
- **Frequency Analysis**: Log volume and trends
- **Export**: JSON and CSV export formats

## 🔧 Configuration

### Environment Variables

```bash
# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_PORT=8001
PROMETHEUS_ENABLED=true
HEALTH_CHECK_INTERVAL=60
DEEP_HEALTH_CHECK_INTERVAL=300

# Performance Monitoring
PERFORMANCE_PROFILING_ENABLED=true
MEMORY_PROFILING_ENABLED=false
CPU_PROFILING_ENABLED=false
MAX_REQUEST_PROFILES=1000
MAX_DATABASE_PROFILES=1000

# Alert Configuration
ALERT_ENABLED=true
ALERT_COOLDOWN_MINUTES=15
ALERT_ESCALATION_MINUTES=60
MAX_ALERTS_PER_HOUR=50

# Log Aggregation
LOG_AGGREGATION_ENABLED=true
MAX_LOG_ENTRIES=10000
LOG_RETENTION_DAYS=30
LOG_PATTERN_DETECTION_ENABLED=true

# Performance Thresholds
SLOW_REQUEST_THRESHOLD_MS=1000
SLOW_DATABASE_THRESHOLD_MS=500
HIGH_MEMORY_THRESHOLD_MB=500
HIGH_CPU_THRESHOLD_PERCENT=80.0
HIGH_DISK_THRESHOLD_PERCENT=90.0
```

## 📊 API Endpoints

### Metrics Endpoints
- `GET /api/v1/monitoring/metrics` - Prometheus metrics
- `GET /api/v1/monitoring/metrics/summary` - Metrics summary
- `POST /api/v1/monitoring/metrics/query` - Query historical metrics

### Health Endpoints
- `GET /api/v1/monitoring/health` - System health status
- `GET /api/v1/monitoring/health/{service}` - Service health
- `GET /api/v1/monitoring/health/dependencies/status` - Dependencies health

### Alert Endpoints
- `GET /api/v1/monitoring/alerts` - Active alerts
- `GET /api/v1/monitoring/alerts/history` - Alert history
- `POST /api/v1/monitoring/alerts/{id}/acknowledge` - Acknowledge alert
- `GET /api/v1/monitoring/alerts/rules` - Alert rules
- `POST /api/v1/monitoring/alerts/rules` - Create alert rule
- `DELETE /api/v1/monitoring/alerts/rules/{name}` - Delete alert rule
- `POST /api/v1/monitoring/alerts/rules/{name}/suppress` - Suppress alerts

### Performance Endpoints
- `GET /api/v1/monitoring/performance/summary` - Performance summary
- `GET /api/v1/monitoring/performance/requests` - Request profiles
- `GET /api/v1/monitoring/performance/database` - Database profiles
- `GET /api/v1/monitoring/performance/bottlenecks` - Performance bottlenecks
- `GET /api/v1/monitoring/performance/memory` - Memory analysis
- `POST /api/v1/monitoring/performance/memory/snapshot` - Take memory snapshot
- `POST /api/v1/monitoring/performance/cpu/start` - Start CPU profiling
- `POST /api/v1/monitoring/performance/cpu/stop` - Stop CPU profiling

### Log Endpoints
- `POST /api/v1/monitoring/logs/search` - Search logs
- `GET /api/v1/monitoring/logs/analysis` - Log analysis
- `GET /api/v1/monitoring/logs/statistics` - Log statistics
- `GET /api/v1/monitoring/logs/export` - Export logs

### System Status
- `GET /api/v1/monitoring/status` - Comprehensive system status

## 📈 Grafana Dashboards

### Overview Dashboard
- System health indicators
- Application uptime
- Resource utilization (CPU, Memory, Disk)
- HTTP request metrics
- Active alerts summary

### Trading Dashboard
- Trading success rate
- Portfolio performance
- Signal generation and accuracy
- Token analysis metrics
- Trading volume and P&L

### Performance Dashboard
- Request performance metrics
- Database query performance
- Memory and CPU profiling
- Performance bottlenecks
- Cache performance

## 🐳 Docker Integration

### Health Checks
```dockerfile
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health && \
        curl -f http://localhost:3000/ready && \
        curl -f http://localhost:3000/metrics || exit 1
```

### Kubernetes Probes
```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 3000
  initialDelaySeconds: 30
  periodSeconds: 30

readinessProbe:
  httpGet:
    path: /ready
    port: 3000
  initialDelaySeconds: 5
  periodSeconds: 10
```

## 🔍 Troubleshooting

### Common Issues

#### High Memory Usage
1. Check memory profiling data
2. Look for memory leaks in snapshots
3. Review garbage collection metrics
4. Check for large object allocations

#### Slow Performance
1. Review request profiling data
2. Check database query performance
3. Look for performance bottlenecks
4. Analyze CPU profiling results

#### Alert Fatigue
1. Review alert thresholds
2. Suppress noisy alerts temporarily
3. Adjust alert cooldown periods
4. Implement alert escalation

#### Missing Metrics
1. Verify monitoring is enabled
2. Check Prometheus configuration
3. Ensure metrics collector is running
4. Review application logs

## 🚀 Best Practices

### Monitoring
- Set appropriate alert thresholds
- Use meaningful metric labels
- Monitor business metrics, not just technical ones
- Implement proper error handling

### Performance
- Profile regularly in production
- Set performance budgets
- Monitor key user journeys
- Use caching effectively

### Alerting
- Alert on symptoms, not causes
- Keep alerts actionable
- Avoid alert fatigue
- Document alert runbooks

### Logging
- Use structured logging
- Include correlation IDs
- Log at appropriate levels
- Implement log rotation
