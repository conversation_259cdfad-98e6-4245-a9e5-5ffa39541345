"""
Web Interface Routes

FastAPI routes for the web interface providing dashboard, user management,
charts, and trade history endpoints. Follows security best practices.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from fastapi.responses import Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

import structlog

from src.shared.types import (
    DashboardData, RealTimeUpdate, UserInterfaceData, UserProfile,
    NotificationPreferences, PerformanceChart, SignalChart, TradeChart,
    PaginatedTradeHistory, TradeAnalytics, TradeExportData, TradeFilter
)
from src.features.security.auth_manager import AuthManager
from src.features.web_interface.dashboard_service import DashboardService
from src.features.web_interface.user_interface_manager import UserInterfaceManager
from src.features.web_interface.chart_service import ChartService, TimeFrame, ChartType
from src.features.web_interface.trade_history_service import TradeHistoryService, SortOrder

logger = structlog.get_logger(__name__)
security = HTTPBearer()

# Create router
router = APIRouter(prefix="/api/v1/web", tags=["Web Interface"])


# Dependency to get current user
async def get_current_user(token: str = Depends(security)) -> str:
    """Get current user from JWT token"""
    auth_manager = AuthManager()  # This should be injected in production
    user_id = await auth_manager.verify_token(token.credentials)
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return user_id


# Dashboard endpoints
@router.get("/dashboard", response_model=DashboardData)
async def get_dashboard(
    current_user: str = Depends(get_current_user),
    dashboard_service: DashboardService = Depends()
):
    """
    Get comprehensive dashboard data for the current user.
    
    Returns:
        Complete dashboard data including portfolio, signals, and performance
    """
    try:
        dashboard_data = await dashboard_service.get_dashboard_data(current_user)
        logger.info("Dashboard data retrieved", user_id=current_user)
        return dashboard_data
    except Exception as e:
        logger.error("Failed to get dashboard data", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to load dashboard")


@router.get("/dashboard/realtime", response_model=RealTimeUpdate)
async def get_realtime_updates(
    current_user: str = Depends(get_current_user),
    dashboard_service: DashboardService = Depends()
):
    """
    Get real-time dashboard updates.
    
    Returns:
        Real-time update data
    """
    try:
        updates = await dashboard_service.get_real_time_updates(current_user)
        return updates
    except Exception as e:
        logger.error("Failed to get real-time updates", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get real-time updates")


@router.post("/dashboard/refresh")
async def refresh_dashboard(
    current_user: str = Depends(get_current_user),
    dashboard_service: DashboardService = Depends()
):
    """
    Refresh dashboard cache for the current user.
    
    Returns:
        Success status
    """
    try:
        success = await dashboard_service.invalidate_cache(current_user)
        if success:
            return {"status": "success", "message": "Dashboard cache refreshed"}
        else:
            raise HTTPException(status_code=500, detail="Failed to refresh dashboard")
    except Exception as e:
        logger.error("Failed to refresh dashboard", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to refresh dashboard")


# User interface endpoints
@router.get("/user", response_model=UserInterfaceData)
async def get_user_interface_data(
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends()
):
    """
    Get comprehensive user interface data.
    
    Returns:
        Complete user interface data
    """
    try:
        ui_data = await ui_manager.get_user_interface_data(current_user)
        logger.info("User interface data retrieved", user_id=current_user)
        return ui_data
    except Exception as e:
        logger.error("Failed to get UI data", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to load user interface data")


@router.put("/user/profile", response_model=UserProfile)
async def update_user_profile(
    profile_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends()
):
    """
    Update user profile.
    
    Args:
        profile_data: Profile data to update
        
    Returns:
        Updated user profile
    """
    try:
        updated_profile = await ui_manager.update_user_profile(current_user, profile_data)
        logger.info("User profile updated", user_id=current_user)
        return updated_profile
    except Exception as e:
        logger.error("Failed to update profile", user_id=current_user, error=str(e))
        raise


@router.put("/user/preferences", response_model=NotificationPreferences)
async def update_notification_preferences(
    preferences: NotificationPreferences = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends()
):
    """
    Update notification preferences.
    
    Args:
        preferences: New notification preferences
        
    Returns:
        Updated preferences
    """
    try:
        updated_preferences = await ui_manager.update_notification_preferences(
            current_user, preferences
        )
        logger.info("Notification preferences updated", user_id=current_user)
        return updated_preferences
    except Exception as e:
        logger.error("Failed to update preferences", user_id=current_user, error=str(e))
        raise


@router.post("/user/api-keys")
async def create_api_key(
    key_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends()
):
    """
    Create new API key.
    
    Args:
        key_data: API key creation data
        
    Returns:
        Created API key data
    """
    try:
        api_key = await ui_manager.create_api_key(
            user_id=current_user,
            key_name=key_data.get("name"),
            permissions=key_data.get("permissions", []),
            expires_in_days=key_data.get("expires_in_days")
        )
        logger.info("API key created", user_id=current_user, key_name=key_data.get("name"))
        return api_key
    except Exception as e:
        logger.error("Failed to create API key", user_id=current_user, error=str(e))
        raise


@router.delete("/user/api-keys/{key_id}")
async def revoke_api_key(
    key_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends()
):
    """
    Revoke an API key.
    
    Args:
        key_id: API key identifier
        
    Returns:
        Success status
    """
    try:
        success = await ui_manager.revoke_api_key(current_user, key_id)
        if success:
            logger.info("API key revoked", user_id=current_user, key_id=key_id)
            return {"status": "success", "message": "API key revoked"}
        else:
            raise HTTPException(status_code=404, detail="API key not found")
    except Exception as e:
        logger.error("Failed to revoke API key", user_id=current_user, key_id=key_id, error=str(e))
        raise


# Chart endpoints
@router.get("/charts/portfolio", response_model=PerformanceChart)
async def get_portfolio_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    chart_type: ChartType = Query(ChartType.LINE),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends()
):
    """
    Get portfolio performance chart.
    
    Args:
        timeframe: Chart timeframe
        chart_type: Type of chart
        
    Returns:
        Portfolio performance chart data
    """
    try:
        chart = await chart_service.get_portfolio_chart(current_user, timeframe, chart_type)
        return chart
    except Exception as e:
        logger.error("Failed to get portfolio chart", user_id=current_user, error=str(e))
        raise


@router.get("/charts/signals", response_model=SignalChart)
async def get_signals_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    signal_type: Optional[str] = Query(None),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends()
):
    """
    Get signals chart.
    
    Args:
        timeframe: Chart timeframe
        signal_type: Filter by signal type (optional)
        
    Returns:
        Signals chart data
    """
    try:
        chart = await chart_service.get_signals_chart(current_user, timeframe, signal_type)
        return chart
    except Exception as e:
        logger.error("Failed to get signals chart", user_id=current_user, error=str(e))
        raise


@router.get("/charts/trades", response_model=TradeChart)
async def get_trade_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends()
):
    """
    Get trade history chart.
    
    Args:
        timeframe: Chart timeframe
        
    Returns:
        Trade history chart data
    """
    try:
        chart = await chart_service.get_trade_history_chart(current_user, timeframe)
        return chart
    except Exception as e:
        logger.error("Failed to get trade chart", user_id=current_user, error=str(e))
        raise


# Trade history endpoints
@router.get("/trades", response_model=PaginatedTradeHistory)
async def get_trade_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=200),
    sort_by: str = Query("created_at"),
    sort_order: SortOrder = Query(SortOrder.DESC),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    token_address: Optional[str] = Query(None),
    trade_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    min_value: Optional[float] = Query(None),
    max_value: Optional[float] = Query(None),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends()
):
    """
    Get paginated trade history with filtering and sorting.
    
    Returns:
        Paginated trade history
    """
    try:
        # Create filter object
        filters = TradeFilter(
            start_date=start_date,
            end_date=end_date,
            token_address=token_address,
            trade_type=trade_type,
            status=status,
            min_value=min_value,
            max_value=max_value
        )
        
        trade_history = await trade_service.get_trade_history(
            user_id=current_user,
            filters=filters,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        return trade_history
    except Exception as e:
        logger.error("Failed to get trade history", user_id=current_user, error=str(e))
        raise


@router.get("/trades/{trade_id}")
async def get_trade_details(
    trade_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends()
):
    """
    Get detailed information for a specific trade.
    
    Args:
        trade_id: Trade identifier
        
    Returns:
        Detailed trade information
    """
    try:
        trade_details = await trade_service.get_trade_details(current_user, trade_id)
        return trade_details
    except Exception as e:
        logger.error("Failed to get trade details", user_id=current_user, trade_id=trade_id, error=str(e))
        raise


@router.get("/trades/analytics", response_model=TradeAnalytics)
async def get_trade_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends()
):
    """
    Get comprehensive trade analytics.
    
    Args:
        start_date: Start date for analysis
        end_date: End date for analysis
        
    Returns:
        Trade analytics data
    """
    try:
        analytics = await trade_service.get_trade_analytics(current_user, start_date, end_date)
        return analytics
    except Exception as e:
        logger.error("Failed to get trade analytics", user_id=current_user, error=str(e))
        raise


@router.post("/trades/export")
async def export_trade_history(
    export_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends()
):
    """
    Export trade history in specified format.
    
    Args:
        export_data: Export configuration
        
    Returns:
        Export data with download content
    """
    try:
        # Create filter object from export data
        filters = None
        if export_data.get("filters"):
            filter_data = export_data["filters"]
            filters = TradeFilter(
                start_date=filter_data.get("start_date"),
                end_date=filter_data.get("end_date"),
                token_address=filter_data.get("token_address"),
                trade_type=filter_data.get("trade_type"),
                status=filter_data.get("status"),
                min_value=filter_data.get("min_value"),
                max_value=filter_data.get("max_value")
            )
        
        export_result = await trade_service.export_trade_history(
            user_id=current_user,
            filters=filters,
            format=export_data.get("format", "csv")
        )
        
        logger.info("Trade history exported", user_id=current_user, format=export_data.get("format"))
        
        # Return as file download
        return Response(
            content=export_result.content.encode('utf-8'),
            media_type=export_result.content_type,
            headers={
                "Content-Disposition": f"attachment; filename={export_result.filename}"
            }
        )
    except Exception as e:
        logger.error("Failed to export trade history", user_id=current_user, error=str(e))
        raise
