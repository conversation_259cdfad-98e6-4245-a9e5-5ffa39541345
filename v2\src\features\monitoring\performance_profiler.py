"""
⚡ Performance Profiler

Comprehensive performance profiling system for request profiling, database profiling,
memory profiling, CPU profiling, and bottleneck detection.
"""

import asyncio
import time
import tracemalloc
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from functools import wraps
import cProfile
import pstats
from io import StringIO

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


@dataclass
class PerformanceMetric:
    """Performance metric data"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RequestProfile:
    """Request performance profile"""
    endpoint: str
    method: str
    duration: float
    memory_usage: float
    cpu_usage: float
    database_queries: int
    database_time: float
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DatabaseProfile:
    """Database operation profile"""
    operation: str
    collection: str
    duration: float
    query_type: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceProfiler:
    """
    ⚡ Comprehensive performance profiling system
    """
    
    def __init__(self):
        self.settings = settings
        self.is_running = False
        self.profiling_enabled = True
        
        # Performance data storage
        self.request_profiles: List[RequestProfile] = []
        self.database_profiles: List[DatabaseProfile] = []
        self.performance_metrics: List[PerformanceMetric] = []
        
        # Memory tracking
        self.memory_snapshots = []
        self.memory_tracking_enabled = False
        
        # CPU profiling
        self.cpu_profiler = None
        self.cpu_profiling_enabled = False
        
        # Bottleneck detection
        self.bottlenecks = []
        self.performance_thresholds = {
            'slow_request_ms': 1000,
            'slow_database_ms': 500,
            'high_memory_mb': 500,
            'high_cpu_percent': 80
        }
        
        logger.info("PerformanceProfiler initialized")
    
    async def start(self):
        """Start performance profiling"""
        if self.is_running:
            logger.warning("PerformanceProfiler already running")
            return
        
        self.is_running = True
        logger.info("Starting PerformanceProfiler")
        
        # Start memory tracking if enabled
        if self.memory_tracking_enabled:
            tracemalloc.start()
        
        # Start background profiling tasks
        asyncio.create_task(self._profiling_loop())
        asyncio.create_task(self._bottleneck_detection_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """Stop performance profiling"""
        self.is_running = False
        
        # Stop memory tracking
        if tracemalloc.is_tracing():
            tracemalloc.stop()
        
        # Stop CPU profiling
        if self.cpu_profiler:
            self.cpu_profiler.disable()
        
        logger.info("PerformanceProfiler stopped")
    
    async def _profiling_loop(self):
        """Main profiling loop"""
        while self.is_running:
            try:
                await self._collect_system_performance()
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                logger.error(
                    "Error in profiling loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(60)
    
    async def _bottleneck_detection_loop(self):
        """Bottleneck detection loop"""
        while self.is_running:
            try:
                await self._detect_bottlenecks()
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(
                    "Error in bottleneck detection",
                    error=str(e)
                )
                await asyncio.sleep(300)
    
    async def _cleanup_loop(self):
        """Cleanup old profiling data"""
        while self.is_running:
            try:
                await self._cleanup_old_data()
                await asyncio.sleep(3600)  # Cleanup every hour
                
            except Exception as e:
                logger.error(
                    "Error in cleanup loop",
                    error=str(e)
                )
                await asyncio.sleep(3600)
    
    async def _collect_system_performance(self):
        """Collect system performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.performance_metrics.append(
                PerformanceMetric("cpu_usage", cpu_percent, "percent")
            )
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.performance_metrics.append(
                PerformanceMetric("memory_usage", memory.used, "bytes")
            )
            self.performance_metrics.append(
                PerformanceMetric("memory_percent", memory.percent, "percent")
            )
            
            # Disk I/O metrics
            disk_io = psutil.disk_io_counters()
            if disk_io:
                self.performance_metrics.append(
                    PerformanceMetric("disk_read_bytes", disk_io.read_bytes, "bytes")
                )
                self.performance_metrics.append(
                    PerformanceMetric("disk_write_bytes", disk_io.write_bytes, "bytes")
                )
            
            # Network I/O metrics
            net_io = psutil.net_io_counters()
            if net_io:
                self.performance_metrics.append(
                    PerformanceMetric("network_bytes_sent", net_io.bytes_sent, "bytes")
                )
                self.performance_metrics.append(
                    PerformanceMetric("network_bytes_recv", net_io.bytes_recv, "bytes")
                )
            
        except Exception as e:
            logger.error(
                "Error collecting system performance",
                error=str(e)
            )
    
    def profile_request(self, func: Callable) -> Callable:
        """Decorator for profiling request performance"""
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not self.profiling_enabled:
                return await func(*args, **kwargs)
            
            start_time = time.time()
            start_memory = self._get_memory_usage()
            start_cpu = psutil.cpu_percent()
            
            try:
                result = await func(*args, **kwargs)
                
                # Calculate metrics
                duration = time.time() - start_time
                memory_usage = self._get_memory_usage() - start_memory
                cpu_usage = psutil.cpu_percent() - start_cpu
                
                # Create profile
                profile = RequestProfile(
                    endpoint=getattr(func, '__name__', 'unknown'),
                    method='async',
                    duration=duration,
                    memory_usage=memory_usage,
                    cpu_usage=cpu_usage,
                    database_queries=0,  # Would be tracked separately
                    database_time=0.0,   # Would be tracked separately
                    metadata={'args_count': len(args), 'kwargs_count': len(kwargs)}
                )
                
                self.request_profiles.append(profile)
                
                # Check for slow requests
                if duration > self.performance_thresholds['slow_request_ms'] / 1000:
                    await self._record_bottleneck(
                        'slow_request',
                        f"Slow request: {func.__name__} took {duration:.3f}s",
                        {'function': func.__name__, 'duration': duration}
                    )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    "Error in profiled request",
                    function=func.__name__,
                    duration=f"{duration:.3f}s",
                    error=str(e)
                )
                raise
        
        return wrapper
    
    def profile_database_operation(self, operation: str, collection: str):
        """Decorator for profiling database operations"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def wrapper(*args, **kwargs):
                if not self.profiling_enabled:
                    return await func(*args, **kwargs)
                
                start_time = time.time()
                
                try:
                    result = await func(*args, **kwargs)
                    
                    duration = time.time() - start_time
                    
                    # Create database profile
                    profile = DatabaseProfile(
                        operation=operation,
                        collection=collection,
                        duration=duration,
                        query_type=operation,
                        metadata={'function': func.__name__}
                    )
                    
                    self.database_profiles.append(profile)
                    
                    # Check for slow queries
                    if duration > self.performance_thresholds['slow_database_ms'] / 1000:
                        await self._record_bottleneck(
                            'slow_database_query',
                            f"Slow database query: {operation} on {collection} took {duration:.3f}s",
                            {'operation': operation, 'collection': collection, 'duration': duration}
                        )
                    
                    return result
                    
                except Exception as e:
                    duration = time.time() - start_time
                    logger.error(
                        "Error in profiled database operation",
                        operation=operation,
                        collection=collection,
                        duration=f"{duration:.3f}s",
                        error=str(e)
                    )
                    raise
            
            return wrapper
        return decorator
    
    def start_memory_profiling(self):
        """Start memory profiling"""
        if not tracemalloc.is_tracing():
            tracemalloc.start()
            self.memory_tracking_enabled = True
            logger.info("Memory profiling started")
    
    def stop_memory_profiling(self):
        """Stop memory profiling"""
        if tracemalloc.is_tracing():
            tracemalloc.stop()
            self.memory_tracking_enabled = False
            logger.info("Memory profiling stopped")
    
    def take_memory_snapshot(self, name: str = None):
        """Take memory snapshot"""
        if tracemalloc.is_tracing():
            snapshot = tracemalloc.take_snapshot()
            self.memory_snapshots.append({
                'name': name or f"snapshot_{len(self.memory_snapshots)}",
                'snapshot': snapshot,
                'timestamp': datetime.utcnow()
            })
            logger.info(f"Memory snapshot taken: {name}")
    
    def start_cpu_profiling(self):
        """Start CPU profiling"""
        if not self.cpu_profiling_enabled:
            self.cpu_profiler = cProfile.Profile()
            self.cpu_profiler.enable()
            self.cpu_profiling_enabled = True
            logger.info("CPU profiling started")
    
    def stop_cpu_profiling(self) -> str:
        """Stop CPU profiling and return results"""
        if self.cpu_profiling_enabled and self.cpu_profiler:
            self.cpu_profiler.disable()
            self.cpu_profiling_enabled = False
            
            # Get profiling results
            s = StringIO()
            ps = pstats.Stats(self.cpu_profiler, stream=s)
            ps.sort_stats('cumulative')
            ps.print_stats(20)  # Top 20 functions
            
            logger.info("CPU profiling stopped")
            return s.getvalue()
        
        return "CPU profiling was not active"

    async def _detect_bottlenecks(self):
        """Detect performance bottlenecks"""
        try:
            # Analyze recent request profiles
            recent_requests = [
                r for r in self.request_profiles
                if r.timestamp > datetime.utcnow() - timedelta(minutes=10)
            ]

            if recent_requests:
                avg_duration = sum(r.duration for r in recent_requests) / len(recent_requests)
                if avg_duration > self.performance_thresholds['slow_request_ms'] / 1000:
                    await self._record_bottleneck(
                        'high_average_response_time',
                        f"High average response time: {avg_duration:.3f}s",
                        {'avg_duration': avg_duration, 'request_count': len(recent_requests)}
                    )

            # Analyze database profiles
            recent_db_ops = [
                d for d in self.database_profiles
                if d.timestamp > datetime.utcnow() - timedelta(minutes=10)
            ]

            if recent_db_ops:
                avg_db_duration = sum(d.duration for d in recent_db_ops) / len(recent_db_ops)
                if avg_db_duration > self.performance_thresholds['slow_database_ms'] / 1000:
                    await self._record_bottleneck(
                        'high_database_response_time',
                        f"High database response time: {avg_db_duration:.3f}s",
                        {'avg_duration': avg_db_duration, 'query_count': len(recent_db_ops)}
                    )

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.used > self.performance_thresholds['high_memory_mb'] * 1024 * 1024:
                await self._record_bottleneck(
                    'high_memory_usage',
                    f"High memory usage: {memory.used / (1024*1024):.1f}MB",
                    {'memory_used_mb': memory.used / (1024*1024), 'memory_percent': memory.percent}
                )

        except Exception as e:
            logger.error(
                "Error detecting bottlenecks",
                error=str(e)
            )

    async def _record_bottleneck(self, bottleneck_type: str, description: str, metadata: Dict[str, Any]):
        """Record a performance bottleneck"""
        bottleneck = {
            'type': bottleneck_type,
            'description': description,
            'timestamp': datetime.utcnow(),
            'metadata': metadata
        }

        self.bottlenecks.append(bottleneck)

        logger.warning(
            "Performance bottleneck detected",
            type=bottleneck_type,
            description=description,
            metadata=metadata
        )

    async def _cleanup_old_data(self):
        """Clean up old profiling data"""
        cutoff = datetime.utcnow() - timedelta(hours=24)

        # Clean request profiles
        self.request_profiles = [
            r for r in self.request_profiles if r.timestamp > cutoff
        ]

        # Clean database profiles
        self.database_profiles = [
            d for d in self.database_profiles if d.timestamp > cutoff
        ]

        # Clean performance metrics
        self.performance_metrics = [
            m for m in self.performance_metrics if m.timestamp > cutoff
        ]

        # Clean bottlenecks (keep for 7 days)
        bottleneck_cutoff = datetime.utcnow() - timedelta(days=7)
        self.bottlenecks = [
            b for b in self.bottlenecks if b['timestamp'] > bottleneck_cutoff
        ]

        # Clean memory snapshots (keep last 10)
        if len(self.memory_snapshots) > 10:
            self.memory_snapshots = self.memory_snapshots[-10:]

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0

    # Public API methods
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary"""
        recent_cutoff = datetime.utcnow() - timedelta(hours=1)

        recent_requests = [
            r for r in self.request_profiles if r.timestamp > recent_cutoff
        ]
        recent_db_ops = [
            d for d in self.database_profiles if d.timestamp > recent_cutoff
        ]
        recent_bottlenecks = [
            b for b in self.bottlenecks if b['timestamp'] > recent_cutoff
        ]

        return {
            'request_performance': {
                'total_requests': len(recent_requests),
                'avg_duration': sum(r.duration for r in recent_requests) / len(recent_requests) if recent_requests else 0,
                'max_duration': max((r.duration for r in recent_requests), default=0),
                'min_duration': min((r.duration for r in recent_requests), default=0)
            },
            'database_performance': {
                'total_operations': len(recent_db_ops),
                'avg_duration': sum(d.duration for d in recent_db_ops) / len(recent_db_ops) if recent_db_ops else 0,
                'max_duration': max((d.duration for d in recent_db_ops), default=0),
                'slowest_operations': sorted(recent_db_ops, key=lambda x: x.duration, reverse=True)[:5]
            },
            'system_performance': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'memory_used_mb': psutil.virtual_memory().used / (1024 * 1024)
            },
            'bottlenecks': {
                'recent_count': len(recent_bottlenecks),
                'types': list(set(b['type'] for b in recent_bottlenecks))
            },
            'profiling_status': {
                'enabled': self.profiling_enabled,
                'memory_tracking': self.memory_tracking_enabled,
                'cpu_profiling': self.cpu_profiling_enabled
            }
        }

    async def get_request_profiles(self, limit: int = 100) -> List[RequestProfile]:
        """Get recent request profiles"""
        return sorted(
            self.request_profiles[-limit:],
            key=lambda x: x.timestamp,
            reverse=True
        )

    async def get_database_profiles(self, limit: int = 100) -> List[DatabaseProfile]:
        """Get recent database profiles"""
        return sorted(
            self.database_profiles[-limit:],
            key=lambda x: x.timestamp,
            reverse=True
        )

    async def get_bottlenecks(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent bottlenecks"""
        return sorted(
            self.bottlenecks[-limit:],
            key=lambda x: x['timestamp'],
            reverse=True
        )

    async def get_memory_analysis(self) -> Dict[str, Any]:
        """Get memory analysis"""
        if not self.memory_snapshots:
            return {'error': 'No memory snapshots available'}

        latest_snapshot = self.memory_snapshots[-1]
        snapshot = latest_snapshot['snapshot']

        # Get top memory consumers
        top_stats = snapshot.statistics('lineno')[:10]

        return {
            'snapshot_count': len(self.memory_snapshots),
            'latest_snapshot': {
                'name': latest_snapshot['name'],
                'timestamp': latest_snapshot['timestamp'],
                'top_memory_consumers': [
                    {
                        'file': stat.traceback.format()[0],
                        'size_mb': stat.size / (1024 * 1024),
                        'count': stat.count
                    }
                    for stat in top_stats
                ]
            },
            'current_memory': {
                'rss_mb': psutil.virtual_memory().used / (1024 * 1024),
                'percent': psutil.virtual_memory().percent
            }
        }

    def enable_profiling(self):
        """Enable performance profiling"""
        self.profiling_enabled = True
        logger.info("Performance profiling enabled")

    def disable_profiling(self):
        """Disable performance profiling"""
        self.profiling_enabled = False
        logger.info("Performance profiling disabled")

    def set_performance_thresholds(self, thresholds: Dict[str, float]):
        """Update performance thresholds"""
        self.performance_thresholds.update(thresholds)
        logger.info(
            "Performance thresholds updated",
            thresholds=thresholds
        )
