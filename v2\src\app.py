"""
🚀 TokenTracker V2 Main Application

FastAPI application following V2 instructions with comprehensive
features, monitoring, and production-ready configuration.
"""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .config.settings import get_settings
from .config.logging_config import setup_logging, get_logger
from .config.database import connect_database, disconnect_database
from .shared.middleware import (
    LoggingMiddleware,
    RateLimitMiddleware,
    SecurityMiddleware,
    MetricsMiddleware
)
from .shared.types import APIResponse, HealthCheck
from .features.monitoring.health_monitor import HealthMonitor
from .features.monitoring.metrics_collector import MetricsCollector

# Initialize logging
setup_logging()
logger = get_logger(__name__)

# Get settings
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    🔄 Application lifespan management
    """
    logger.info(
        "Starting TokenTracker V2",
        version="2.0.0",
        environment=settings.environment
    )
    
    try:
        # Initialize database
        await connect_database()
        logger.info("Database connection established")
        
        # Initialize monitoring
        health_monitor = HealthMonitor()
        metrics_collector = MetricsCollector()
        
        # Start background tasks
        await health_monitor.start()
        await metrics_collector.start()
        
        # Store in app state
        app.state.health_monitor = health_monitor
        app.state.metrics_collector = metrics_collector
        
        logger.info("Application startup completed successfully")
        
        yield
        
    except Exception as e:
        logger.error(
            "Application startup failed",
            error=str(e),
            error_type=type(e).__name__
        )
        raise
    
    finally:
        # Cleanup
        logger.info("Shutting down TokenTracker V2")
        
        try:
            # Stop background tasks
            if hasattr(app.state, 'health_monitor'):
                await app.state.health_monitor.stop()
            if hasattr(app.state, 'metrics_collector'):
                await app.state.metrics_collector.stop()
            
            # Close database connection
            await disconnect_database()
            logger.info("Database connection closed")
            
        except Exception as e:
            logger.error(
                "Error during application shutdown",
                error=str(e)
            )
        
        logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="TokenTracker V2",
    description="Advanced Trading Automation System for Solana Tokens",
    version="2.0.0",
    docs_url="/docs" if not settings.is_production else None,
    redoc_url="/redoc" if not settings.is_production else None,
    openapi_url="/openapi.json" if not settings.is_production else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.is_development else ["https://tokentracker.com"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(SecurityMiddleware)
app.add_middleware(MetricsMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(LoggingMiddleware)


# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions"""
    logger.warning(
        "HTTP exception occurred",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=APIResponse(
            success=False,
            message=exc.detail,
            errors=[exc.detail],
            timestamp=datetime.utcnow(),
            request_id=getattr(request.state, 'request_id', None)
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(
        "Unhandled exception occurred",
        error=str(exc),
        error_type=type(exc).__name__,
        path=request.url.path,
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=500,
        content=APIResponse(
            success=False,
            message="Internal server error",
            errors=["An unexpected error occurred"],
            timestamp=datetime.utcnow(),
            request_id=getattr(request.state, 'request_id', None)
        ).dict()
    )


# Health check endpoints
@app.get("/health", response_model=HealthCheck)
async def health_check():
    """
    🏥 Basic health check endpoint
    """
    try:
        health_monitor = app.state.health_monitor
        health_status = await health_monitor.get_health_status()
        
        return HealthCheck(
            status="healthy" if health_status["overall_status"] == "healthy" else "unhealthy",
            timestamp=datetime.utcnow(),
            services=health_status["services"],
            metrics=health_status["metrics"],
            uptime=health_status["uptime"],
            version="2.0.0"
        )
        
    except Exception as e:
        logger.error(
            "Health check failed",
            error=str(e)
        )
        return HealthCheck(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            services={},
            metrics={"error": str(e)},
            uptime=0.0,
            version="2.0.0"
        )


@app.get("/ready")
async def readiness_check():
    """
    🔍 Readiness check for Kubernetes
    """
    try:
        health_monitor = app.state.health_monitor
        health_status = await health_monitor.get_health_status()
        
        # Check if all critical services are healthy
        critical_services = ["database", "cache", "dune_api"]
        all_ready = all(
            health_status["services"].get(service, False)
            for service in critical_services
        )
        
        if all_ready:
            return {"status": "ready", "timestamp": datetime.utcnow()}
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not ready",
                    "timestamp": datetime.utcnow(),
                    "services": health_status["services"]
                }
            )
            
    except Exception as e:
        logger.error(
            "Readiness check failed",
            error=str(e)
        )
        return JSONResponse(
            status_code=503,
            content={
                "status": "not ready",
                "error": str(e),
                "timestamp": datetime.utcnow()
            }
        )


@app.get("/metrics")
async def metrics_endpoint():
    """
    📊 Prometheus metrics endpoint
    """
    try:
        metrics_collector = app.state.metrics_collector
        metrics = await metrics_collector.get_prometheus_metrics()
        
        return Response(
            content=metrics,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
        
    except Exception as e:
        logger.error(
            "Metrics collection failed",
            error=str(e)
        )
        raise HTTPException(status_code=500, detail="Metrics collection failed")


@app.get("/")
async def root():
    """
    🏠 Root endpoint
    """
    return APIResponse(
        success=True,
        message="TokenTracker V2 API is running",
        data={
            "version": "2.0.0",
            "environment": settings.environment,
            "timestamp": datetime.utcnow(),
            "docs_url": "/docs" if not settings.is_production else None
        }
    )


# Include feature routers
from .features.data_pipeline.routes import router as data_pipeline_router
from .features.signal_processing.routes import router as signal_processing_router
from .features.paper_trading.routes import router as paper_trading_router
from .features.automation.routes import router as automation_router
from .features.monitoring.routes import router as monitoring_router
from .features.notifications.routes import router as notifications_router
from .features.web_interface.routes import router as web_interface_router
from .features.mobile_integration.routes import router as mobile_integration_router
from .features.third_party_integrations.routes import router as third_party_integrations_router

app.include_router(
    data_pipeline_router,
    prefix="/api/v1/data",
    tags=["Data Pipeline"]
)

app.include_router(
    signal_processing_router,
    prefix="/api/v1/signals",
    tags=["Signal Processing"]
)

app.include_router(
    paper_trading_router,
    prefix="/api/v1/trading",
    tags=["Paper Trading"]
)

app.include_router(
    automation_router,
    prefix="/api/v1/automation",
    tags=["Trading Automation"]
)

app.include_router(
    monitoring_router,
    prefix="/api/v1/monitoring",
    tags=["Monitoring"]
)

app.include_router(
    notifications_router,
    prefix="/api/v1/notifications",
    tags=["Notifications"]
)

app.include_router(
    web_interface_router,
    tags=["Web Interface"]
)

app.include_router(
    mobile_integration_router,
    tags=["Mobile Integration"]
)

app.include_router(
    third_party_integrations_router,
    tags=["Third-Party Integrations"]
)


if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "src.app:app",
        host="0.0.0.0",
        port=settings.app_port,
        reload=settings.is_development,
        log_level=settings.log_level,
        access_log=True
    )
