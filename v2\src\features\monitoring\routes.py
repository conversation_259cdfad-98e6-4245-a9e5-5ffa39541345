"""
📊 Monitoring API Routes

Comprehensive API routes for monitoring endpoints including metrics, health,
alerts, and performance data with proper error handling and validation.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Query, Depends, Request, Response
from fastapi.responses import PlainTextResponse
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.types import APIResponse
from ...database.models.monitoring import (
    MetricRecord, AlertRule, AlertInstance, HealthCheckRecord,
    PerformanceLog, SystemMetrics
)
from .metrics_collector import MetricsCollector
from .health_monitor import HealthMonitor
from .alert_manager import AlertManager, AlertSeverity
from .performance_profiler import PerformanceProfiler
from .log_aggregator import LogAggregator, LogLevel

logger = get_logger(__name__)
settings = get_settings()

# Create router
router = APIRouter()


# Request/Response Models
class MetricQuery(BaseModel):
    """Metric query parameters"""
    metric_name: Optional[str] = None
    source: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=1000, le=10000)


class AlertRuleCreate(BaseModel):
    """Alert rule creation model"""
    name: str
    description: str
    condition: str
    threshold: float
    severity: AlertSeverity
    enabled: bool = True
    cooldown_minutes: int = 15
    escalation_minutes: int = 60
    tags: Dict[str, str] = Field(default_factory=dict)
    notification_channels: List[str] = Field(default_factory=list)


class AlertRuleUpdate(BaseModel):
    """Alert rule update model"""
    description: Optional[str] = None
    condition: Optional[str] = None
    threshold: Optional[float] = None
    severity: Optional[AlertSeverity] = None
    enabled: Optional[bool] = None
    cooldown_minutes: Optional[int] = None
    escalation_minutes: Optional[int] = None
    tags: Optional[Dict[str, str]] = None
    notification_channels: Optional[List[str]] = None


class LogQuery(BaseModel):
    """Log query parameters"""
    query: Optional[str] = None
    level: Optional[LogLevel] = None
    module: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = Field(default=100, le=1000)


# Dependency functions
async def get_metrics_collector(request: Request) -> MetricsCollector:
    """Get metrics collector from app state"""
    if not hasattr(request.app.state, 'metrics_collector'):
        raise HTTPException(status_code=503, detail="Metrics collector not available")
    return request.app.state.metrics_collector


async def get_health_monitor(request: Request) -> HealthMonitor:
    """Get health monitor from app state"""
    if not hasattr(request.app.state, 'health_monitor'):
        raise HTTPException(status_code=503, detail="Health monitor not available")
    return request.app.state.health_monitor


async def get_alert_manager(request: Request) -> AlertManager:
    """Get alert manager from app state"""
    if not hasattr(request.app.state, 'alert_manager'):
        # Create alert manager if not exists
        alert_manager = AlertManager()
        await alert_manager.start()
        request.app.state.alert_manager = alert_manager
    return request.app.state.alert_manager


async def get_performance_profiler(request: Request) -> PerformanceProfiler:
    """Get performance profiler from app state"""
    if not hasattr(request.app.state, 'performance_profiler'):
        # Create performance profiler if not exists
        profiler = PerformanceProfiler()
        await profiler.start()
        request.app.state.performance_profiler = profiler
    return request.app.state.performance_profiler


async def get_log_aggregator(request: Request) -> LogAggregator:
    """Get log aggregator from app state"""
    if not hasattr(request.app.state, 'log_aggregator'):
        # Create log aggregator if not exists
        aggregator = LogAggregator()
        await aggregator.start()
        request.app.state.log_aggregator = aggregator
    return request.app.state.log_aggregator


# Metrics Endpoints
@router.get("/metrics", response_class=PlainTextResponse)
async def get_prometheus_metrics(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector)
):
    """
    📊 Get metrics in Prometheus format
    """
    try:
        metrics = await metrics_collector.get_prometheus_metrics()
        return Response(
            content=metrics,
            media_type="text/plain; version=0.0.4; charset=utf-8"
        )
    except Exception as e:
        logger.error("Error getting Prometheus metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get metrics")


@router.get("/metrics/summary")
async def get_metrics_summary(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector)
):
    """
    📊 Get comprehensive metrics summary
    """
    try:
        summary = await metrics_collector.get_metrics_summary()
        return APIResponse(
            success=True,
            message="Metrics summary retrieved successfully",
            data=summary
        )
    except Exception as e:
        logger.error("Error getting metrics summary", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get metrics summary")


@router.post("/metrics/query")
async def query_metrics(
    query: MetricQuery,
    metrics_collector: MetricsCollector = Depends(get_metrics_collector)
):
    """
    📊 Query historical metrics
    """
    try:
        # This would query the database for historical metrics
        # For now, return current metrics
        summary = await metrics_collector.get_metrics_summary()
        
        return APIResponse(
            success=True,
            message="Metrics query completed",
            data={
                "query": query.dict(),
                "results": summary,
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error querying metrics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to query metrics")


# Health Endpoints
@router.get("/health")
async def get_health_status(
    health_monitor: HealthMonitor = Depends(get_health_monitor)
):
    """
    🏥 Get comprehensive health status
    """
    try:
        health_status = await health_monitor.get_health_status()
        return APIResponse(
            success=True,
            message="Health status retrieved successfully",
            data=health_status
        )
    except Exception as e:
        logger.error("Error getting health status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get health status")


@router.get("/health/{service_name}")
async def get_service_health(
    service_name: str,
    health_monitor: HealthMonitor = Depends(get_health_monitor)
):
    """
    🏥 Get health status of specific service
    """
    try:
        service_health = await health_monitor.get_service_health(service_name)
        if service_health is None:
            raise HTTPException(status_code=404, detail=f"Service '{service_name}' not found")
        
        return APIResponse(
            success=True,
            message=f"Health status for {service_name} retrieved successfully",
            data={
                "service": service_name,
                "healthy": service_health,
                "timestamp": datetime.utcnow()
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting service health", service=service_name, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get service health")


@router.get("/health/dependencies/status")
async def get_dependencies_health(
    health_monitor: HealthMonitor = Depends(get_health_monitor)
):
    """
    🏥 Get health status of all dependencies
    """
    try:
        health_status = await health_monitor.get_health_status()
        dependencies = health_status.get('dependencies', {})
        
        return APIResponse(
            success=True,
            message="Dependencies health status retrieved successfully",
            data={
                "dependencies": dependencies,
                "healthy_count": sum(1 for status in dependencies.values() if status),
                "total_count": len(dependencies),
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error getting dependencies health", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get dependencies health")


# Alert Endpoints
@router.get("/alerts")
async def get_active_alerts(
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Get all active alerts
    """
    try:
        alerts = await alert_manager.get_active_alerts()
        return APIResponse(
            success=True,
            message="Active alerts retrieved successfully",
            data={
                "alerts": [alert.__dict__ for alert in alerts],
                "count": len(alerts),
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error getting active alerts", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get active alerts")


@router.get("/alerts/history")
async def get_alert_history(
    limit: int = Query(default=100, le=1000),
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Get alert history
    """
    try:
        history = await alert_manager.get_alert_history(limit=limit)
        return APIResponse(
            success=True,
            message="Alert history retrieved successfully",
            data={
                "alerts": [alert.__dict__ for alert in history],
                "count": len(history),
                "limit": limit,
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error getting alert history", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get alert history")


@router.post("/alerts/{alert_id}/acknowledge")
async def acknowledge_alert(
    alert_id: str,
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Acknowledge an alert
    """
    try:
        success = await alert_manager.acknowledge_alert(alert_id)
        if not success:
            raise HTTPException(status_code=404, detail="Alert not found")
        
        return APIResponse(
            success=True,
            message=f"Alert {alert_id} acknowledged successfully",
            data={"alert_id": alert_id, "acknowledged_at": datetime.utcnow()}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error acknowledging alert", alert_id=alert_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to acknowledge alert")


@router.get("/alerts/rules")
async def get_alert_rules(
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Get all alert rules
    """
    try:
        rules = await alert_manager.get_alert_rules()
        return APIResponse(
            success=True,
            message="Alert rules retrieved successfully",
            data={
                "rules": {name: rule.__dict__ for name, rule in rules.items()},
                "count": len(rules),
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error getting alert rules", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get alert rules")


@router.post("/alerts/rules")
async def create_alert_rule(
    rule_data: AlertRuleCreate,
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Create new alert rule
    """
    try:
        from .alert_manager import AlertRule as AlertRuleClass
        
        rule = AlertRuleClass(
            name=rule_data.name,
            description=rule_data.description,
            condition=rule_data.condition,
            threshold=rule_data.threshold,
            severity=rule_data.severity,
            enabled=rule_data.enabled,
            cooldown_minutes=rule_data.cooldown_minutes,
            escalation_minutes=rule_data.escalation_minutes,
            tags=rule_data.tags
        )
        
        await alert_manager.add_alert_rule(rule)
        
        return APIResponse(
            success=True,
            message=f"Alert rule '{rule_data.name}' created successfully",
            data=rule.__dict__
        )
    except Exception as e:
        logger.error("Error creating alert rule", rule_name=rule_data.name, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create alert rule")


@router.delete("/alerts/rules/{rule_name}")
async def delete_alert_rule(
    rule_name: str,
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Delete alert rule
    """
    try:
        await alert_manager.remove_alert_rule(rule_name)
        return APIResponse(
            success=True,
            message=f"Alert rule '{rule_name}' deleted successfully",
            data={"rule_name": rule_name, "deleted_at": datetime.utcnow()}
        )
    except Exception as e:
        logger.error("Error deleting alert rule", rule_name=rule_name, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete alert rule")


@router.post("/alerts/rules/{rule_name}/suppress")
async def suppress_alert_rule(
    rule_name: str,
    duration_minutes: int = Query(default=60, ge=1, le=1440),
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Suppress alerts for a specific rule
    """
    try:
        await alert_manager.suppress_alert_rule(rule_name, duration_minutes)
        return APIResponse(
            success=True,
            message=f"Alert rule '{rule_name}' suppressed for {duration_minutes} minutes",
            data={
                "rule_name": rule_name,
                "duration_minutes": duration_minutes,
                "suppressed_until": datetime.utcnow() + timedelta(minutes=duration_minutes)
            }
        )
    except Exception as e:
        logger.error("Error suppressing alert rule", rule_name=rule_name, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to suppress alert rule")


@router.get("/alerts/statistics")
async def get_alert_statistics(
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🚨 Get alert statistics
    """
    try:
        stats = await alert_manager.get_alert_statistics()
        return APIResponse(
            success=True,
            message="Alert statistics retrieved successfully",
            data=stats
        )
    except Exception as e:
        logger.error("Error getting alert statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get alert statistics")


# Performance Endpoints
@router.get("/performance/summary")
async def get_performance_summary(
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Get performance summary
    """
    try:
        summary = await profiler.get_performance_summary()
        return APIResponse(
            success=True,
            message="Performance summary retrieved successfully",
            data=summary
        )
    except Exception as e:
        logger.error("Error getting performance summary", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get performance summary")


@router.get("/performance/requests")
async def get_request_profiles(
    limit: int = Query(default=100, le=1000),
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Get request performance profiles
    """
    try:
        profiles = await profiler.get_request_profiles(limit=limit)
        return APIResponse(
            success=True,
            message="Request profiles retrieved successfully",
            data={
                "profiles": [profile.__dict__ for profile in profiles],
                "count": len(profiles),
                "limit": limit
            }
        )
    except Exception as e:
        logger.error("Error getting request profiles", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get request profiles")


@router.get("/performance/database")
async def get_database_profiles(
    limit: int = Query(default=100, le=1000),
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Get database performance profiles
    """
    try:
        profiles = await profiler.get_database_profiles(limit=limit)
        return APIResponse(
            success=True,
            message="Database profiles retrieved successfully",
            data={
                "profiles": [profile.__dict__ for profile in profiles],
                "count": len(profiles),
                "limit": limit
            }
        )
    except Exception as e:
        logger.error("Error getting database profiles", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get database profiles")


@router.get("/performance/bottlenecks")
async def get_bottlenecks(
    limit: int = Query(default=50, le=200),
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Get performance bottlenecks
    """
    try:
        bottlenecks = await profiler.get_bottlenecks(limit=limit)
        return APIResponse(
            success=True,
            message="Performance bottlenecks retrieved successfully",
            data={
                "bottlenecks": bottlenecks,
                "count": len(bottlenecks),
                "limit": limit
            }
        )
    except Exception as e:
        logger.error("Error getting bottlenecks", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get bottlenecks")


@router.get("/performance/memory")
async def get_memory_analysis(
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Get memory analysis
    """
    try:
        analysis = await profiler.get_memory_analysis()
        return APIResponse(
            success=True,
            message="Memory analysis retrieved successfully",
            data=analysis
        )
    except Exception as e:
        logger.error("Error getting memory analysis", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get memory analysis")


@router.post("/performance/memory/snapshot")
async def take_memory_snapshot(
    name: Optional[str] = Query(None),
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Take memory snapshot
    """
    try:
        profiler.take_memory_snapshot(name)
        return APIResponse(
            success=True,
            message="Memory snapshot taken successfully",
            data={
                "snapshot_name": name or f"snapshot_{datetime.utcnow().isoformat()}",
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error taking memory snapshot", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to take memory snapshot")


@router.post("/performance/cpu/start")
async def start_cpu_profiling(
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Start CPU profiling
    """
    try:
        profiler.start_cpu_profiling()
        return APIResponse(
            success=True,
            message="CPU profiling started successfully",
            data={"started_at": datetime.utcnow()}
        )
    except Exception as e:
        logger.error("Error starting CPU profiling", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to start CPU profiling")


@router.post("/performance/cpu/stop")
async def stop_cpu_profiling(
    profiler: PerformanceProfiler = Depends(get_performance_profiler)
):
    """
    ⚡ Stop CPU profiling and get results
    """
    try:
        results = profiler.stop_cpu_profiling()
        return APIResponse(
            success=True,
            message="CPU profiling stopped successfully",
            data={
                "results": results,
                "stopped_at": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error stopping CPU profiling", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to stop CPU profiling")


# Log Endpoints
@router.post("/logs/search")
async def search_logs(
    query: LogQuery,
    aggregator: LogAggregator = Depends(get_log_aggregator)
):
    """
    📝 Search logs with filters
    """
    try:
        logs = await aggregator.search_logs(
            query=query.query,
            level=query.level,
            module=query.module,
            start_time=query.start_time,
            end_time=query.end_time,
            limit=query.limit
        )

        return APIResponse(
            success=True,
            message="Log search completed successfully",
            data={
                "logs": [log.__dict__ for log in logs],
                "count": len(logs),
                "query": query.dict(),
                "timestamp": datetime.utcnow()
            }
        )
    except Exception as e:
        logger.error("Error searching logs", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to search logs")


@router.get("/logs/analysis")
async def get_log_analysis(
    hours: int = Query(default=24, ge=1, le=168),
    aggregator: LogAggregator = Depends(get_log_aggregator)
):
    """
    📝 Get comprehensive log analysis
    """
    try:
        analysis = await aggregator.get_log_analysis(hours=hours)
        return APIResponse(
            success=True,
            message="Log analysis retrieved successfully",
            data=analysis.__dict__
        )
    except Exception as e:
        logger.error("Error getting log analysis", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get log analysis")


@router.get("/logs/statistics")
async def get_log_statistics(
    aggregator: LogAggregator = Depends(get_log_aggregator)
):
    """
    📝 Get log statistics
    """
    try:
        stats = await aggregator.get_log_statistics()
        return APIResponse(
            success=True,
            message="Log statistics retrieved successfully",
            data=stats
        )
    except Exception as e:
        logger.error("Error getting log statistics", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get log statistics")


@router.get("/logs/export")
async def export_logs(
    format_type: str = Query(default="json", regex="^(json|csv)$"),
    start_time: Optional[datetime] = Query(None),
    end_time: Optional[datetime] = Query(None),
    aggregator: LogAggregator = Depends(get_log_aggregator)
):
    """
    📝 Export logs in specified format
    """
    try:
        exported_data = await aggregator.export_logs(
            format_type=format_type,
            start_time=start_time,
            end_time=end_time
        )

        media_type = "application/json" if format_type == "json" else "text/csv"
        filename = f"logs_export_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{format_type}"

        return Response(
            content=exported_data,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        logger.error("Error exporting logs", format_type=format_type, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to export logs")


# System Status Endpoint
@router.get("/status")
async def get_system_status(
    metrics_collector: MetricsCollector = Depends(get_metrics_collector),
    health_monitor: HealthMonitor = Depends(get_health_monitor),
    alert_manager: AlertManager = Depends(get_alert_manager)
):
    """
    🖥️ Get comprehensive system status
    """
    try:
        # Get data from all monitoring components
        health_status = await health_monitor.get_health_status()
        metrics_summary = await metrics_collector.get_metrics_summary()
        alert_stats = await alert_manager.get_alert_statistics()

        system_status = {
            "overall_health": health_status.get("overall_status", "unknown"),
            "uptime_seconds": health_status.get("uptime", 0),
            "services": health_status.get("services", {}),
            "dependencies": health_status.get("dependencies", {}),
            "metrics": {
                "collection_status": metrics_summary.get("collection_status", "unknown"),
                "system_metrics": metrics_summary.get("system_metrics", {}),
                "business_metrics": metrics_summary.get("business_metrics", {})
            },
            "alerts": {
                "active_count": alert_stats.get("active_alerts", 0),
                "total_24h": alert_stats.get("total_alerts_24h", 0),
                "critical_alerts": alert_stats.get("alerts_by_severity_24h", {}).get("critical", 0)
            },
            "timestamp": datetime.utcnow()
        }

        return APIResponse(
            success=True,
            message="System status retrieved successfully",
            data=system_status
        )
    except Exception as e:
        logger.error("Error getting system status", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to get system status")
