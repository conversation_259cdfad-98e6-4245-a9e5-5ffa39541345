"""
✅ Shared Validators

Common validation functions for data integrity and business rules.
"""

import re
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from ..config.logging_config import get_logger

logger = get_logger(__name__)


def validate_solana_address(address: str) -> bool:
    """
    Validate Solana address format
    
    Args:
        address: Address to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not address or not isinstance(address, str):
            return False
        
        # Solana addresses are base58 encoded and 32-44 characters long
        if len(address) < 32 or len(address) > 44:
            return False
        
        # Check for valid base58 characters
        base58_chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
        return all(c in base58_chars for c in address)
        
    except Exception as e:
        logger.error(f"Error validating Solana address: {str(e)}")
        return False


def validate_email(email: str) -> bool:
    """
    Validate email address format
    
    Args:
        email: Email to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not email or not isinstance(email, str):
            return False
        
        # Basic email regex pattern
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
        
    except Exception as e:
        logger.error(f"Error validating email: {str(e)}")
        return False


def validate_password(password: str, min_length: int = 8) -> Dict[str, Any]:
    """
    Validate password strength
    
    Args:
        password: Password to validate
        min_length: Minimum password length
        
    Returns:
        Validation result with details
    """
    try:
        result = {
            "valid": True,
            "errors": [],
            "strength": "weak"
        }
        
        if not password or not isinstance(password, str):
            result["valid"] = False
            result["errors"].append("Password is required")
            return result
        
        # Check minimum length
        if len(password) < min_length:
            result["valid"] = False
            result["errors"].append(f"Password must be at least {min_length} characters long")
        
        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            result["errors"].append("Password must contain at least one uppercase letter")
        
        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            result["errors"].append("Password must contain at least one lowercase letter")
        
        # Check for digit
        if not re.search(r'\d', password):
            result["errors"].append("Password must contain at least one digit")
        
        # Check for special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            result["errors"].append("Password must contain at least one special character")
        
        # Determine strength
        if len(result["errors"]) == 0:
            if len(password) >= 12:
                result["strength"] = "strong"
            elif len(password) >= 10:
                result["strength"] = "medium"
        
        if result["errors"]:
            result["valid"] = False
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating password: {str(e)}")
        return {"valid": False, "errors": ["Password validation failed"], "strength": "weak"}


def validate_decimal_range(
    value: Union[Decimal, float, int, str],
    min_value: Optional[Decimal] = None,
    max_value: Optional[Decimal] = None,
    field_name: str = "value"
) -> Dict[str, Any]:
    """
    Validate decimal value within range
    
    Args:
        value: Value to validate
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        field_name: Field name for error messages
        
    Returns:
        Validation result
    """
    try:
        result = {"valid": True, "errors": [], "value": None}
        
        # Convert to Decimal
        try:
            decimal_value = Decimal(str(value))
            result["value"] = decimal_value
        except (ValueError, TypeError):
            result["valid"] = False
            result["errors"].append(f"{field_name} must be a valid number")
            return result
        
        # Check minimum value
        if min_value is not None and decimal_value < min_value:
            result["valid"] = False
            result["errors"].append(f"{field_name} must be at least {min_value}")
        
        # Check maximum value
        if max_value is not None and decimal_value > max_value:
            result["valid"] = False
            result["errors"].append(f"{field_name} must be at most {max_value}")
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating decimal range: {str(e)}")
        return {"valid": False, "errors": [f"{field_name} validation failed"], "value": None}


def validate_percentage(
    value: Union[Decimal, float, int, str],
    field_name: str = "percentage"
) -> Dict[str, Any]:
    """
    Validate percentage value (0-100)
    
    Args:
        value: Percentage value to validate
        field_name: Field name for error messages
        
    Returns:
        Validation result
    """
    return validate_decimal_range(
        value=value,
        min_value=Decimal("0"),
        max_value=Decimal("100"),
        field_name=field_name
    )


def validate_token_symbol(symbol: str) -> bool:
    """
    Validate token symbol format
    
    Args:
        symbol: Token symbol to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not symbol or not isinstance(symbol, str):
            return False
        
        # Token symbols are typically 2-10 characters, alphanumeric
        if len(symbol) < 2 or len(symbol) > 10:
            return False
        
        # Check for valid characters (letters, numbers, some special chars)
        pattern = r'^[A-Za-z0-9._-]+$'
        return bool(re.match(pattern, symbol))
        
    except Exception as e:
        logger.error(f"Error validating token symbol: {str(e)}")
        return False


def validate_url(url: str) -> bool:
    """
    Validate URL format
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not url or not isinstance(url, str):
            return False
        
        # Basic URL pattern
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(pattern, url))
        
    except Exception as e:
        logger.error(f"Error validating URL: {str(e)}")
        return False


def validate_portfolio_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate portfolio data
    
    Args:
        data: Portfolio data to validate
        
    Returns:
        Validation result
    """
    try:
        result = {"valid": True, "errors": []}
        
        # Required fields
        required_fields = ["name", "user_id"]
        for field in required_fields:
            if field not in data or not data[field]:
                result["valid"] = False
                result["errors"].append(f"{field} is required")
        
        # Validate name
        if "name" in data:
            name = data["name"]
            if not isinstance(name, str) or len(name) < 1 or len(name) > 100:
                result["valid"] = False
                result["errors"].append("Portfolio name must be 1-100 characters")
        
        # Validate description if provided
        if "description" in data and data["description"]:
            description = data["description"]
            if not isinstance(description, str) or len(description) > 500:
                result["valid"] = False
                result["errors"].append("Portfolio description must be at most 500 characters")
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating portfolio data: {str(e)}")
        return {"valid": False, "errors": ["Portfolio validation failed"]}


def validate_trade_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate trade data
    
    Args:
        data: Trade data to validate
        
    Returns:
        Validation result
    """
    try:
        result = {"valid": True, "errors": []}
        
        # Required fields
        required_fields = ["token_address", "side", "quantity", "price"]
        for field in required_fields:
            if field not in data or data[field] is None:
                result["valid"] = False
                result["errors"].append(f"{field} is required")
        
        # Validate token address
        if "token_address" in data:
            if not validate_solana_address(data["token_address"]):
                result["valid"] = False
                result["errors"].append("Invalid token address format")
        
        # Validate side
        if "side" in data:
            if data["side"] not in ["buy", "sell"]:
                result["valid"] = False
                result["errors"].append("Trade side must be 'buy' or 'sell'")
        
        # Validate quantity
        if "quantity" in data:
            qty_validation = validate_decimal_range(
                data["quantity"],
                min_value=Decimal("0.000001"),
                field_name="quantity"
            )
            if not qty_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(qty_validation["errors"])
        
        # Validate price
        if "price" in data:
            price_validation = validate_decimal_range(
                data["price"],
                min_value=Decimal("0.000001"),
                field_name="price"
            )
            if not price_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(price_validation["errors"])
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating trade data: {str(e)}")
        return {"valid": False, "errors": ["Trade validation failed"]}


def validate_signal_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate signal data
    
    Args:
        data: Signal data to validate
        
    Returns:
        Validation result
    """
    try:
        result = {"valid": True, "errors": []}
        
        # Required fields
        required_fields = ["token_address", "signal_type", "strength", "confidence"]
        for field in required_fields:
            if field not in data or data[field] is None:
                result["valid"] = False
                result["errors"].append(f"{field} is required")
        
        # Validate token address
        if "token_address" in data:
            if not validate_solana_address(data["token_address"]):
                result["valid"] = False
                result["errors"].append("Invalid token address format")
        
        # Validate signal type
        if "signal_type" in data:
            valid_types = ["buy", "sell", "hold", "strong_buy", "strong_sell"]
            if data["signal_type"] not in valid_types:
                result["valid"] = False
                result["errors"].append(f"Signal type must be one of: {', '.join(valid_types)}")
        
        # Validate strength
        if "strength" in data:
            strength_validation = validate_decimal_range(
                data["strength"],
                min_value=Decimal("0"),
                max_value=Decimal("1"),
                field_name="strength"
            )
            if not strength_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(strength_validation["errors"])
        
        # Validate confidence
        if "confidence" in data:
            confidence_validation = validate_decimal_range(
                data["confidence"],
                min_value=Decimal("0"),
                max_value=Decimal("1"),
                field_name="confidence"
            )
            if not confidence_validation["valid"]:
                result["valid"] = False
                result["errors"].extend(confidence_validation["errors"])
        
        return result
        
    except Exception as e:
        logger.error(f"Error validating signal data: {str(e)}")
        return {"valid": False, "errors": ["Signal validation failed"]}


def sanitize_input(value: Any, max_length: int = 1000) -> str:
    """
    Sanitize input string
    
    Args:
        value: Value to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized string
    """
    try:
        if value is None:
            return ""
        
        # Convert to string
        text = str(value)
        
        # Remove null bytes and control characters
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        return text.strip()
        
    except Exception as e:
        logger.error(f"Error sanitizing input: {str(e)}")
        return ""
