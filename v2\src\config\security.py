"""
🔐 Security Configuration

Security-related configuration and utilities.
"""

from typing import Dict, List, Optional
from .settings import get_settings
from .logging_config import get_logger

logger = get_logger(__name__)
settings = get_settings()


class SecurityConfig:
    """Security configuration manager"""
    
    def __init__(self):
        self.logger = logger
        self.settings = settings
        
        # Security headers
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY", 
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin"
        }
        
        # CORS settings
        self.cors_origins = [
            "http://localhost:3000",
            "http://localhost:8000",
            "https://tokentracker.app"
        ]
        
        # Rate limiting
        self.rate_limits = {
            "default": {"requests": 100, "window": 60},  # 100 requests per minute
            "auth": {"requests": 5, "window": 60},       # 5 auth requests per minute
            "api": {"requests": 1000, "window": 3600}    # 1000 API requests per hour
        }
    
    def get_security_headers(self) -> Dict[str, str]:
        """Get security headers"""
        return self.security_headers.copy()
    
    def get_cors_origins(self) -> List[str]:
        """Get CORS allowed origins"""
        return self.cors_origins.copy()
    
    def get_rate_limit(self, endpoint_type: str = "default") -> Dict[str, int]:
        """Get rate limit for endpoint type"""
        return self.rate_limits.get(endpoint_type, self.rate_limits["default"])
    
    def is_secure_environment(self) -> bool:
        """Check if running in secure environment"""
        return self.settings.environment in ["production", "staging"]
    
    def get_jwt_config(self) -> Dict[str, str]:
        """Get JWT configuration"""
        return {
            "secret": self.settings.jwt_secret,
            "algorithm": "HS256",
            "expires_in": self.settings.jwt_expires_in
        }
    
    def get_encryption_config(self) -> Dict[str, str]:
        """Get encryption configuration"""
        return {
            "key": self.settings.encryption_key,
            "algorithm": "AES-256-GCM"
        }
