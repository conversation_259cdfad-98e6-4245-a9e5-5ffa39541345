"""
Web Interface Feature Module

This module provides a comprehensive web interface for TokenTracker V2,
including dashboard, user management, and real-time features.

Features:
- Real-time portfolio dashboard
- Signal monitoring interface
- Performance charts and analytics
- Trade history and management
- User profile management
- Subscription management
- Notification preferences
- API access management

Following v2.INSTRUCTIONS:
- Feature-based modular architecture
- Clean separation of concerns
- Security-first design
- Performance optimization
- Comprehensive testing
"""

from .dashboard_service import DashboardService
from .user_interface_manager import UserInterfaceManager
from .chart_service import ChartService
from .trade_history_service import TradeHistoryService

__all__ = [
    'DashboardService',
    'UserInterfaceManager', 
    'ChartService',
    'TradeHistoryService'
]
