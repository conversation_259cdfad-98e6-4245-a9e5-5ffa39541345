"""
🚀 Advanced Cache Manager - Phase 3 Production Optimization

Multi-level caching system with intelligent cache invalidation, cache warming,
and comprehensive cache monitoring for production-grade performance.
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
import weakref

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import API_CONSTANTS
from .cache_manager import CacheManager

logger = get_logger(__name__)


class CacheLevel(Enum):
    """Cache levels for multi-level caching"""
    MEMORY = "memory"
    REDIS = "redis"
    CDN = "cdn"


class CacheStrategy(Enum):
    """Cache invalidation strategies"""
    TTL = "ttl"
    LRU = "lru"
    WRITE_THROUGH = "write_through"
    WRITE_BEHIND = "write_behind"
    REFRESH_AHEAD = "refresh_ahead"


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl: Optional[int]
    tags: List[str]
    size_bytes: int
    
    def is_expired(self) -> bool:
        """Check if cache entry is expired"""
        if not self.ttl:
            return False
        return datetime.utcnow() > self.created_at + timedelta(seconds=self.ttl)
    
    def touch(self) -> None:
        """Update last accessed time and increment access count"""
        self.last_accessed = datetime.utcnow()
        self.access_count += 1


@dataclass
class CacheStats:
    """Cache performance statistics"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    memory_usage_bytes: int = 0
    avg_access_time_ms: float = 0.0
    hit_ratio: float = 0.0
    
    def update_hit_ratio(self) -> None:
        """Update hit ratio calculation"""
        total_requests = self.hits + self.misses
        self.hit_ratio = (self.hits / total_requests * 100) if total_requests > 0 else 0.0


class AdvancedCacheManager:
    """
    🚀 Advanced Multi-Level Cache Manager
    
    Features:
    - Multi-level caching (Memory -> Redis -> CDN)
    - Intelligent cache invalidation strategies
    - Cache warming and preloading
    - Comprehensive monitoring and analytics
    - Distributed cache coordination
    """
    
    def __init__(self, project_id: str = "v2"):
        self.settings = get_settings()
        self.project_id = project_id
        
        # Initialize base Redis cache manager
        self.redis_cache = CacheManager(project_id)
        
        # Memory cache (L1)
        self.memory_cache: Dict[str, CacheEntry] = {}
        self.memory_cache_max_size = 1000  # Max entries in memory
        self.memory_cache_max_bytes = 100 * 1024 * 1024  # 100MB max memory usage
        
        # Cache statistics per level
        self.stats = {
            CacheLevel.MEMORY: CacheStats(),
            CacheLevel.REDIS: CacheStats(),
            CacheLevel.CDN: CacheStats()
        }
        
        # Cache warming configuration
        self.warming_enabled = True
        self.warming_tasks: Dict[str, asyncio.Task] = {}
        
        # Cache invalidation tracking
        self.invalidation_callbacks: Dict[str, List[Callable]] = {}
        self.tag_mappings: Dict[str, List[str]] = {}  # tag -> keys mapping
        
        # Performance monitoring
        self.access_times: List[float] = []
        self.slow_queries_threshold = 0.1  # 100ms
        
        logger.info(
            "Advanced cache manager initialized",
            project_id=self.project_id,
            memory_max_size=self.memory_cache_max_size,
            memory_max_bytes=self.memory_cache_max_bytes
        )
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.redis_cache.connect()
        await self._start_background_tasks()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._stop_background_tasks()
        await self.redis_cache.close()
    
    async def get(
        self,
        key: str,
        data_type: str = None,
        cache_levels: List[CacheLevel] = None
    ) -> Optional[Any]:
        """
        📥 Get data from multi-level cache
        
        Args:
            key: Cache key
            data_type: Expected data type
            cache_levels: Cache levels to check (defaults to all)
            
        Returns:
            Cached data or None if not found
        """
        start_time = time.time()
        
        if cache_levels is None:
            cache_levels = [CacheLevel.MEMORY, CacheLevel.REDIS]
        
        try:
            # Check memory cache first (L1)
            if CacheLevel.MEMORY in cache_levels:
                memory_result = await self._get_from_memory(key)
                if memory_result is not None:
                    self.stats[CacheLevel.MEMORY].hits += 1
                    self._record_access_time(start_time)
                    return memory_result
                else:
                    self.stats[CacheLevel.MEMORY].misses += 1
            
            # Check Redis cache (L2)
            if CacheLevel.REDIS in cache_levels:
                redis_result = await self.redis_cache.get(key, data_type)
                if redis_result is not None:
                    self.stats[CacheLevel.REDIS].hits += 1
                    
                    # Promote to memory cache
                    await self._set_in_memory(key, redis_result)
                    
                    self._record_access_time(start_time)
                    return redis_result
                else:
                    self.stats[CacheLevel.REDIS].misses += 1
            
            # All cache levels missed
            self._record_access_time(start_time)
            return None
            
        except Exception as e:
            logger.error(f"Error getting data from cache: {str(e)}")
            self._record_access_time(start_time)
            return None
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: List[str] = None,
        cache_levels: List[CacheLevel] = None,
        strategy: CacheStrategy = CacheStrategy.WRITE_THROUGH
    ) -> bool:
        """
        📤 Set data in multi-level cache
        
        Args:
            key: Cache key
            value: Data to cache
            ttl: Time to live in seconds
            tags: Cache tags for invalidation
            cache_levels: Cache levels to write to
            strategy: Cache write strategy
            
        Returns:
            True if successful
        """
        if cache_levels is None:
            cache_levels = [CacheLevel.MEMORY, CacheLevel.REDIS]
        
        if tags is None:
            tags = []
        
        try:
            success = True
            
            # Set in memory cache (L1)
            if CacheLevel.MEMORY in cache_levels:
                memory_success = await self._set_in_memory(key, value, ttl, tags)
                if memory_success:
                    self.stats[CacheLevel.MEMORY].sets += 1
                success = success and memory_success
            
            # Set in Redis cache (L2) based on strategy
            if CacheLevel.REDIS in cache_levels:
                if strategy in [CacheStrategy.WRITE_THROUGH, CacheStrategy.TTL]:
                    redis_success = await self.redis_cache.set(key, value, ttl)
                    if redis_success:
                        self.stats[CacheLevel.REDIS].sets += 1
                    success = success and redis_success
                elif strategy == CacheStrategy.WRITE_BEHIND:
                    # Async write to Redis
                    asyncio.create_task(self._async_redis_write(key, value, ttl))
            
            # Update tag mappings
            if tags:
                await self._update_tag_mappings(key, tags)
            
            return success
            
        except Exception as e:
            logger.error(f"Error setting data in cache: {str(e)}")
            return False
    
    async def delete(self, key: str, cache_levels: List[CacheLevel] = None) -> bool:
        """
        🗑️ Delete data from multi-level cache
        """
        if cache_levels is None:
            cache_levels = [CacheLevel.MEMORY, CacheLevel.REDIS]
        
        try:
            success = True
            
            # Delete from memory cache
            if CacheLevel.MEMORY in cache_levels:
                if key in self.memory_cache:
                    del self.memory_cache[key]
                    self.stats[CacheLevel.MEMORY].deletes += 1
            
            # Delete from Redis cache
            if CacheLevel.REDIS in cache_levels:
                redis_success = await self.redis_cache.delete(key)
                if redis_success:
                    self.stats[CacheLevel.REDIS].deletes += 1
                success = success and redis_success
            
            # Clean up tag mappings
            await self._cleanup_tag_mappings(key)
            
            return success
            
        except Exception as e:
            logger.error(f"Error deleting data from cache: {str(e)}")
            return False

    async def invalidate_by_tags(self, tags: List[str]) -> int:
        """
        🏷️ Invalidate cache entries by tags

        Args:
            tags: List of tags to invalidate

        Returns:
            Number of entries invalidated
        """
        invalidated_count = 0

        try:
            keys_to_invalidate = set()

            # Collect all keys with matching tags
            for tag in tags:
                if tag in self.tag_mappings:
                    keys_to_invalidate.update(self.tag_mappings[tag])

            # Invalidate collected keys
            for key in keys_to_invalidate:
                success = await self.delete(key)
                if success:
                    invalidated_count += 1

            logger.info(
                "Cache invalidation by tags completed",
                tags=tags,
                invalidated_count=invalidated_count
            )

            return invalidated_count

        except Exception as e:
            logger.error(f"Error invalidating cache by tags: {str(e)}")
            return 0

    async def warm_cache(self, warming_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        🔥 Warm cache with critical data

        Args:
            warming_config: Configuration for cache warming

        Returns:
            Warming results
        """
        if not self.warming_enabled:
            return {"status": "disabled"}

        try:
            warming_results = {
                "started_at": datetime.utcnow().isoformat(),
                "entries_warmed": 0,
                "errors": 0,
                "duration_seconds": 0
            }

            start_time = time.time()

            # Warm critical token data
            if warming_config.get("warm_tokens", False):
                token_results = await self._warm_token_data(warming_config.get("token_addresses", []))
                warming_results["entries_warmed"] += token_results["warmed"]
                warming_results["errors"] += token_results["errors"]

            # Warm signal data
            if warming_config.get("warm_signals", False):
                signal_results = await self._warm_signal_data()
                warming_results["entries_warmed"] += signal_results["warmed"]
                warming_results["errors"] += signal_results["errors"]

            # Warm portfolio data
            if warming_config.get("warm_portfolios", False):
                portfolio_results = await self._warm_portfolio_data()
                warming_results["entries_warmed"] += portfolio_results["warmed"]
                warming_results["errors"] += portfolio_results["errors"]

            warming_results["duration_seconds"] = time.time() - start_time
            warming_results["completed_at"] = datetime.utcnow().isoformat()

            logger.info(
                "Cache warming completed",
                **warming_results
            )

            return warming_results

        except Exception as e:
            logger.error(f"Error during cache warming: {str(e)}")
            return {"status": "error", "error": str(e)}

    async def get_cache_analytics(self) -> Dict[str, Any]:
        """
        📊 Get comprehensive cache analytics
        """
        try:
            # Update hit ratios
            for level_stats in self.stats.values():
                level_stats.update_hit_ratio()

            # Calculate memory usage
            memory_usage = sum(
                entry.size_bytes for entry in self.memory_cache.values()
            )
            self.stats[CacheLevel.MEMORY].memory_usage_bytes = memory_usage

            # Calculate average access time
            if self.access_times:
                avg_access_time = sum(self.access_times) / len(self.access_times) * 1000  # Convert to ms
                for level_stats in self.stats.values():
                    level_stats.avg_access_time_ms = avg_access_time

            analytics = {
                "cache_levels": {
                    level.value: asdict(stats) for level, stats in self.stats.items()
                },
                "memory_cache": {
                    "entries_count": len(self.memory_cache),
                    "max_entries": self.memory_cache_max_size,
                    "memory_usage_bytes": memory_usage,
                    "max_memory_bytes": self.memory_cache_max_bytes,
                    "memory_utilization_percent": (memory_usage / self.memory_cache_max_bytes * 100) if self.memory_cache_max_bytes > 0 else 0
                },
                "performance": {
                    "avg_access_time_ms": avg_access_time if self.access_times else 0,
                    "slow_queries_count": len([t for t in self.access_times if t > self.slow_queries_threshold]),
                    "total_queries": len(self.access_times)
                },
                "tag_mappings_count": len(self.tag_mappings),
                "warming_tasks_active": len(self.warming_tasks),
                "timestamp": datetime.utcnow().isoformat()
            }

            return analytics

        except Exception as e:
            logger.error(f"Error getting cache analytics: {str(e)}")
            return {"error": str(e)}

    # Private helper methods

    async def _get_from_memory(self, key: str) -> Optional[Any]:
        """Get data from memory cache"""
        if key in self.memory_cache:
            entry = self.memory_cache[key]

            # Check if expired
            if entry.is_expired():
                del self.memory_cache[key]
                self.stats[CacheLevel.MEMORY].evictions += 1
                return None

            # Update access info
            entry.touch()
            return entry.value

        return None

    async def _set_in_memory(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: List[str] = None
    ) -> bool:
        """Set data in memory cache with LRU eviction"""
        try:
            # Calculate size
            size_bytes = len(str(value).encode('utf-8'))

            # Check memory limits
            if len(self.memory_cache) >= self.memory_cache_max_size:
                await self._evict_lru_entries(1)

            current_memory = sum(entry.size_bytes for entry in self.memory_cache.values())
            if current_memory + size_bytes > self.memory_cache_max_bytes:
                # Evict entries to make space
                await self._evict_memory_for_size(size_bytes)

            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                access_count=1,
                ttl=ttl,
                tags=tags or [],
                size_bytes=size_bytes
            )

            self.memory_cache[key] = entry
            return True

        except Exception as e:
            logger.error(f"Error setting data in memory cache: {str(e)}")
            return False

    async def _evict_lru_entries(self, count: int) -> None:
        """Evict least recently used entries"""
        if not self.memory_cache:
            return

        # Sort by last accessed time
        sorted_entries = sorted(
            self.memory_cache.items(),
            key=lambda x: x[1].last_accessed
        )

        # Remove oldest entries
        for i in range(min(count, len(sorted_entries))):
            key = sorted_entries[i][0]
            del self.memory_cache[key]
            self.stats[CacheLevel.MEMORY].evictions += 1

    async def _evict_memory_for_size(self, required_bytes: int) -> None:
        """Evict entries to free up required memory"""
        current_memory = sum(entry.size_bytes for entry in self.memory_cache.values())
        target_memory = self.memory_cache_max_bytes - required_bytes

        if current_memory <= target_memory:
            return

        # Sort by access frequency and recency
        sorted_entries = sorted(
            self.memory_cache.items(),
            key=lambda x: (x[1].access_count, x[1].last_accessed)
        )

        # Remove entries until we have enough space
        for key, entry in sorted_entries:
            if current_memory <= target_memory:
                break

            current_memory -= entry.size_bytes
            del self.memory_cache[key]
            self.stats[CacheLevel.MEMORY].evictions += 1

    async def _async_redis_write(self, key: str, value: Any, ttl: Optional[int]) -> None:
        """Async write to Redis for write-behind strategy"""
        try:
            await self.redis_cache.set(key, value, ttl)
            self.stats[CacheLevel.REDIS].sets += 1
        except Exception as e:
            logger.error(f"Error in async Redis write: {str(e)}")

    async def _update_tag_mappings(self, key: str, tags: List[str]) -> None:
        """Update tag to key mappings"""
        for tag in tags:
            if tag not in self.tag_mappings:
                self.tag_mappings[tag] = []
            if key not in self.tag_mappings[tag]:
                self.tag_mappings[tag].append(key)

    async def _cleanup_tag_mappings(self, key: str) -> None:
        """Clean up tag mappings for deleted key"""
        for tag, keys in self.tag_mappings.items():
            if key in keys:
                keys.remove(key)

        # Remove empty tag mappings
        self.tag_mappings = {
            tag: keys for tag, keys in self.tag_mappings.items() if keys
        }

    def _record_access_time(self, start_time: float) -> None:
        """Record cache access time for performance monitoring"""
        access_time = time.time() - start_time
        self.access_times.append(access_time)

        # Keep only last 1000 access times
        if len(self.access_times) > 1000:
            self.access_times = self.access_times[-1000:]

    async def _warm_token_data(self, token_addresses: List[str]) -> Dict[str, int]:
        """Warm cache with token data"""
        results = {"warmed": 0, "errors": 0}

        try:
            # Import here to avoid circular imports
            from .data_aggregator import DataAggregator

            aggregator = DataAggregator(self.project_id)

            for address in token_addresses:
                try:
                    # Fetch and cache token data
                    token_data = await aggregator.get_aggregated_data(address)
                    if token_data:
                        cache_key = f"token_data_{address}"
                        await self.set(
                            cache_key,
                            token_data,
                            ttl=300,  # 5 minutes
                            tags=["token_data", f"token_{address}"]
                        )
                        results["warmed"] += 1
                except Exception as e:
                    logger.warning(f"Error warming token data for {address}: {str(e)}")
                    results["errors"] += 1

        except Exception as e:
            logger.error(f"Error in token data warming: {str(e)}")
            results["errors"] += 1

        return results

    async def _warm_signal_data(self) -> Dict[str, int]:
        """Warm cache with recent signal data"""
        results = {"warmed": 0, "errors": 0}

        try:
            # This would typically fetch recent signals from database
            # For now, we'll just return empty results
            logger.info("Signal data warming completed")

        except Exception as e:
            logger.error(f"Error in signal data warming: {str(e)}")
            results["errors"] += 1

        return results

    async def _warm_portfolio_data(self) -> Dict[str, int]:
        """Warm cache with portfolio data"""
        results = {"warmed": 0, "errors": 0}

        try:
            # This would typically fetch active portfolios from database
            # For now, we'll just return empty results
            logger.info("Portfolio data warming completed")

        except Exception as e:
            logger.error(f"Error in portfolio data warming: {str(e)}")
            results["errors"] += 1

        return results

    async def _start_background_tasks(self) -> None:
        """Start background maintenance tasks"""
        try:
            # Start cache cleanup task
            self.warming_tasks["cleanup"] = asyncio.create_task(self._cache_cleanup_task())

            # Start cache warming task if enabled
            if self.warming_enabled:
                self.warming_tasks["warming"] = asyncio.create_task(self._cache_warming_task())

            logger.info("Cache background tasks started")

        except Exception as e:
            logger.error(f"Error starting background tasks: {str(e)}")

    async def _stop_background_tasks(self) -> None:
        """Stop background maintenance tasks"""
        try:
            for task_name, task in self.warming_tasks.items():
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            self.warming_tasks.clear()
            logger.info("Cache background tasks stopped")

        except Exception as e:
            logger.error(f"Error stopping background tasks: {str(e)}")

    async def _cache_cleanup_task(self) -> None:
        """Background task for cache cleanup"""
        while True:
            try:
                # Clean up expired entries every 5 minutes
                await asyncio.sleep(300)

                expired_keys = []
                for key, entry in self.memory_cache.items():
                    if entry.is_expired():
                        expired_keys.append(key)

                for key in expired_keys:
                    del self.memory_cache[key]
                    self.stats[CacheLevel.MEMORY].evictions += 1

                if expired_keys:
                    logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cache cleanup task: {str(e)}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _cache_warming_task(self) -> None:
        """Background task for cache warming"""
        while True:
            try:
                # Warm cache every 30 minutes
                await asyncio.sleep(1800)

                warming_config = {
                    "warm_tokens": True,
                    "warm_signals": True,
                    "warm_portfolios": True,
                    "token_addresses": []  # Would be populated with critical tokens
                }

                await self.warm_cache(warming_config)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cache warming task: {str(e)}")
                await asyncio.sleep(300)  # Wait before retrying
