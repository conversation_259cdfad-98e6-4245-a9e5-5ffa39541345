"""
📋 Order Model

Database model for trading orders with comprehensive order management
and tracking capabilities.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any
from enum import Enum

from beanie import Document, Indexed
from pydantic import Field

from ...shared.types import SignalType, OrderType


class OrderStatus(str, Enum):
    """Order status enumeration"""
    PENDING = "pending"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    REJECTED = "rejected"


class OrderPriority(str, Enum):
    """Order execution priority levels"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Order(Document):
    """
    📋 Order Model
    
    Represents a trading order with comprehensive tracking and management.
    """
    
    # Order identification
    portfolio_id: Indexed(str) = Field(..., description="Portfolio ID")
    signal_id: Optional[str] = Field(None, description="Source signal ID")
    
    # Order details
    token_address: Indexed(str) = Field(..., description="Token contract address")
    side: SignalType = Field(..., description="Order side (BUY/SELL)")
    order_type: OrderType = Field(..., description="Order type")
    
    # Quantities and prices
    quantity: Decimal = Field(..., description="Order quantity")
    price: Decimal = Field(..., description="Order price")
    stop_price: Optional[Decimal] = Field(None, description="Stop trigger price")
    limit_price: Optional[Decimal] = Field(None, description="Limit price")
    stop_loss: Optional[Decimal] = Field(None, description="Stop loss price")
    take_profit: Optional[Decimal] = Field(None, description="Take profit price")
    value_usd: Decimal = Field(..., description="Order value in USD")
    
    # Execution tracking
    filled_quantity: Decimal = Field(default=Decimal("0"), description="Filled quantity")
    avg_fill_price: Decimal = Field(default=Decimal("0"), description="Average fill price")
    total_fees: Decimal = Field(default=Decimal("0"), description="Total fees paid")
    
    # Order status and lifecycle
    status: OrderStatus = Field(default=OrderStatus.PENDING, description="Order status")
    priority: OrderPriority = Field(default=OrderPriority.NORMAL, description="Execution priority")
    
    # Timestamps
    created_at: Indexed(datetime) = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    expires_at: datetime = Field(..., description="Order expiration timestamp")
    filled_at: Optional[datetime] = Field(None, description="Fill completion timestamp")
    cancelled_at: Optional[datetime] = Field(None, description="Cancellation timestamp")
    expired_at: Optional[datetime] = Field(None, description="Expiration timestamp")
    
    # Additional information
    cancellation_reason: Optional[str] = Field(None, description="Cancellation reason")
    rejection_reason: Optional[str] = Field(None, description="Rejection reason")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional order metadata")
    
    # Execution details
    execution_venue: Optional[str] = Field(None, description="Execution venue (DEX)")
    slippage: Optional[Decimal] = Field(None, description="Execution slippage")
    gas_used: Optional[int] = Field(None, description="Gas used for execution")
    transaction_hash: Optional[str] = Field(None, description="Transaction hash")
    
    class Settings:
        name = "orders"
        indexes = [
            [("portfolio_id", 1), ("status", 1)],
            [("token_address", 1), ("created_at", -1)],
            [("status", 1), ("expires_at", 1)],
            [("signal_id", 1)],
            [("created_at", -1)],
        ]
    
    @property
    def remaining_quantity(self) -> Decimal:
        """Calculate remaining quantity to be filled"""
        return self.quantity - self.filled_quantity
    
    @property
    def fill_percentage(self) -> float:
        """Calculate fill percentage"""
        if self.quantity == 0:
            return 0.0
        return float(self.filled_quantity / self.quantity * 100)
    
    @property
    def is_active(self) -> bool:
        """Check if order is active (can be filled)"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]
    
    @property
    def is_expired(self) -> bool:
        """Check if order has expired"""
        return datetime.utcnow() >= self.expires_at
    
    def can_be_cancelled(self) -> bool:
        """Check if order can be cancelled"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]
    
    def can_be_modified(self) -> bool:
        """Check if order can be modified"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]
    
    async def update_fill(
        self,
        fill_quantity: Decimal,
        fill_price: Decimal,
        fees: Decimal = Decimal("0")
    ):
        """
        Update order with partial or complete fill
        
        Args:
            fill_quantity: Quantity filled in this execution
            fill_price: Price of this fill
            fees: Fees paid for this fill
        """
        # Update filled quantity
        self.filled_quantity += fill_quantity
        
        # Update average fill price
        if self.filled_quantity > 0:
            total_value = (self.avg_fill_price * (self.filled_quantity - fill_quantity)) + (fill_price * fill_quantity)
            self.avg_fill_price = total_value / self.filled_quantity
        
        # Update total fees
        self.total_fees += fees
        
        # Update status
        if self.filled_quantity >= self.quantity:
            self.status = OrderStatus.FILLED
            self.filled_at = datetime.utcnow()
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIALLY_FILLED
        
        # Update timestamp
        self.updated_at = datetime.utcnow()
        
        # Save to database
        await self.save()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert order to dictionary"""
        return {
            "order_id": str(self.id),
            "portfolio_id": self.portfolio_id,
            "signal_id": self.signal_id,
            "token_address": self.token_address,
            "side": self.side,
            "order_type": self.order_type,
            "quantity": self.quantity,
            "price": self.price,
            "stop_price": self.stop_price,
            "limit_price": self.limit_price,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "value_usd": self.value_usd,
            "filled_quantity": self.filled_quantity,
            "remaining_quantity": self.remaining_quantity,
            "avg_fill_price": self.avg_fill_price,
            "total_fees": self.total_fees,
            "status": self.status,
            "priority": self.priority,
            "fill_percentage": self.fill_percentage,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "expires_at": self.expires_at,
            "filled_at": self.filled_at,
            "cancelled_at": self.cancelled_at,
            "expired_at": self.expired_at,
            "cancellation_reason": self.cancellation_reason,
            "rejection_reason": self.rejection_reason,
            "metadata": self.metadata,
            "execution_venue": self.execution_venue,
            "slippage": self.slippage,
            "gas_used": self.gas_used,
            "transaction_hash": self.transaction_hash,
            "is_active": self.is_active,
            "is_expired": self.is_expired
        }
