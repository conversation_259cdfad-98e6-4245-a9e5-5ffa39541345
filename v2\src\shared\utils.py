"""
🛠️ Shared Utilities

Common utility functions used across the application.
"""

import asyncio
import hashlib
import json
import re
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from ..config.logging_config import get_logger

logger = get_logger(__name__)


def validate_solana_address(address: str) -> bool:
    """
    Validate Solana address format
    
    Args:
        address: Address to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        if not address or not isinstance(address, str):
            return False
        
        # Solana addresses are base58 encoded and 32-44 characters long
        if len(address) < 32 or len(address) > 44:
            return False
        
        # Check for valid base58 characters
        base58_chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
        return all(c in base58_chars for c in address)
        
    except Exception as e:
        logger.error(f"Error validating Solana address: {str(e)}")
        return False


def format_currency(amount: Union[Decimal, float, int], currency: str = "USD") -> str:
    """
    Format currency amount for display
    
    Args:
        amount: Amount to format
        currency: Currency code
        
    Returns:
        Formatted currency string
    """
    try:
        if amount is None:
            return f"0.00 {currency}"
        
        amount = float(amount)
        
        if abs(amount) >= 1_000_000:
            return f"{amount / 1_000_000:.2f}M {currency}"
        elif abs(amount) >= 1_000:
            return f"{amount / 1_000:.2f}K {currency}"
        else:
            return f"{amount:.2f} {currency}"
            
    except Exception as e:
        logger.error(f"Error formatting currency: {str(e)}")
        return f"0.00 {currency}"


def format_percentage(value: Union[Decimal, float, int]) -> str:
    """
    Format percentage for display
    
    Args:
        value: Percentage value (0.1 = 10%)
        
    Returns:
        Formatted percentage string
    """
    try:
        if value is None:
            return "0.00%"
        
        percentage = float(value) * 100
        
        if abs(percentage) >= 100:
            return f"{percentage:.1f}%"
        else:
            return f"{percentage:.2f}%"
            
    except Exception as e:
        logger.error(f"Error formatting percentage: {str(e)}")
        return "0.00%"


def truncate_address(address: str, start_chars: int = 6, end_chars: int = 4) -> str:
    """
    Truncate address for display
    
    Args:
        address: Address to truncate
        start_chars: Number of characters to show at start
        end_chars: Number of characters to show at end
        
    Returns:
        Truncated address
    """
    try:
        if not address or len(address) <= start_chars + end_chars:
            return address
        
        return f"{address[:start_chars]}...{address[-end_chars:]}"
        
    except Exception as e:
        logger.error(f"Error truncating address: {str(e)}")
        return address


def calculate_hash(data: Union[str, bytes, Dict]) -> str:
    """
    Calculate SHA256 hash of data
    
    Args:
        data: Data to hash
        
    Returns:
        Hex hash string
    """
    try:
        if isinstance(data, dict):
            data = json.dumps(data, sort_keys=True)
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        return hashlib.sha256(data).hexdigest()
        
    except Exception as e:
        logger.error(f"Error calculating hash: {str(e)}")
        return ""


def safe_decimal(value: Any, default: Decimal = Decimal("0")) -> Decimal:
    """
    Safely convert value to Decimal
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Decimal value
    """
    try:
        if value is None:
            return default
        
        if isinstance(value, Decimal):
            return value
        
        return Decimal(str(value))
        
    except Exception as e:
        logger.error(f"Error converting to Decimal: {str(e)}")
        return default


def safe_float(value: Any, default: float = 0.0) -> float:
    """
    Safely convert value to float
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Float value
    """
    try:
        if value is None:
            return default
        
        return float(value)
        
    except Exception as e:
        logger.error(f"Error converting to float: {str(e)}")
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """
    Safely convert value to int
    
    Args:
        value: Value to convert
        default: Default value if conversion fails
        
    Returns:
        Integer value
    """
    try:
        if value is None:
            return default
        
        return int(float(value))
        
    except Exception as e:
        logger.error(f"Error converting to int: {str(e)}")
        return default


def validate_url(url: str) -> bool:
    """
    Validate URL format
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        result = urlparse(url)
        return all([result.scheme, result.netloc])
        
    except Exception as e:
        logger.error(f"Error validating URL: {str(e)}")
        return False


def sanitize_string(text: str, max_length: int = 1000) -> str:
    """
    Sanitize string for safe storage/display
    
    Args:
        text: Text to sanitize
        max_length: Maximum allowed length
        
    Returns:
        Sanitized text
    """
    try:
        if not text:
            return ""
        
        # Remove null bytes and control characters
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x84\x86-\x9f]', '', text)
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        return text.strip()
        
    except Exception as e:
        logger.error(f"Error sanitizing string: {str(e)}")
        return ""


def get_utc_timestamp() -> datetime:
    """Get current UTC timestamp"""
    return datetime.now(timezone.utc)


def format_timestamp(timestamp: datetime, format_str: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """
    Format timestamp for display
    
    Args:
        timestamp: Timestamp to format
        format_str: Format string
        
    Returns:
        Formatted timestamp string
    """
    try:
        if not timestamp:
            return ""
        
        # Ensure UTC timezone
        if timestamp.tzinfo is None:
            timestamp = timestamp.replace(tzinfo=timezone.utc)
        
        return timestamp.strftime(format_str)
        
    except Exception as e:
        logger.error(f"Error formatting timestamp: {str(e)}")
        return ""


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    try:
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
        
    except Exception as e:
        logger.error(f"Error chunking list: {str(e)}")
        return [lst] if lst else []


async def retry_async(
    func,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    Retry async function with exponential backoff
    
    Args:
        func: Async function to retry
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Backoff multiplier
        exceptions: Exceptions to catch and retry
        
    Returns:
        Function result
    """
    last_exception = None
    
    for attempt in range(max_retries + 1):
        try:
            return await func()
            
        except exceptions as e:
            last_exception = e
            
            if attempt == max_retries:
                break
            
            wait_time = delay * (backoff_factor ** attempt)
            logger.warning(f"Attempt {attempt + 1} failed, retrying in {wait_time}s: {str(e)}")
            await asyncio.sleep(wait_time)
    
    raise last_exception


def deep_merge_dicts(dict1: Dict, dict2: Dict) -> Dict:
    """
    Deep merge two dictionaries
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary
        
    Returns:
        Merged dictionary
    """
    try:
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = deep_merge_dicts(result[key], value)
            else:
                result[key] = value
        
        return result
        
    except Exception as e:
        logger.error(f"Error merging dictionaries: {str(e)}")
        return dict1


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """
    Calculate percentage change between two values
    
    Args:
        old_value: Original value
        new_value: New value
        
    Returns:
        Percentage change
    """
    try:
        if old_value == 0:
            return 0.0 if new_value == 0 else float('inf')
        
        return ((new_value - old_value) / old_value) * 100
        
    except Exception as e:
        logger.error(f"Error calculating percentage change: {str(e)}")
        return 0.0
