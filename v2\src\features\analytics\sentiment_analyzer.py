"""
💭 Sentiment Analyzer

Advanced sentiment analysis for social media, news, and market data
to enhance trading signals with market sentiment insights.
"""

import asyncio
import re
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import json

from ...config.logging_config import get_logger
from ...shared.types import SignalType
from ...database.models import Token

logger = get_logger(__name__)


class SentimentSource(str, Enum):
    """Sentiment data sources"""
    TWITTER = "twitter"
    REDDIT = "reddit"
    TELEGRAM = "telegram"
    NEWS = "news"
    DISCORD = "discord"
    YOUTUBE = "youtube"


class SentimentScore(str, Enum):
    """Sentiment score categories"""
    VERY_NEGATIVE = "very_negative"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"
    POSITIVE = "positive"
    VERY_POSITIVE = "very_positive"


class SentimentAnalyzer:
    """
    💭 Sentiment Analyzer
    
    Analyzes market sentiment from multiple sources:
    - Social media sentiment (Twitter, Reddit, Telegram)
    - News sentiment analysis
    - Community discussions
    - Influencer sentiment tracking
    - Aggregate sentiment scoring
    """
    
    def __init__(self):
        self.logger = logger
        
        # Sentiment keywords and weights
        self.positive_keywords = {
            "bullish": 2.0, "moon": 1.5, "pump": 1.8, "buy": 1.2, "hold": 1.0,
            "diamond": 1.5, "rocket": 1.3, "green": 1.1, "profit": 1.4,
            "gains": 1.3, "up": 1.0, "rise": 1.2, "surge": 1.6, "rally": 1.5,
            "breakout": 1.7, "support": 1.1, "strong": 1.2, "good": 1.0,
            "great": 1.3, "excellent": 1.5, "amazing": 1.4, "awesome": 1.3
        }
        
        self.negative_keywords = {
            "bearish": -2.0, "dump": -1.8, "sell": -1.2, "crash": -2.5,
            "drop": -1.3, "fall": -1.2, "red": -1.1, "loss": -1.4,
            "down": -1.0, "decline": -1.2, "plunge": -1.6, "collapse": -2.0,
            "resistance": -1.1, "weak": -1.2, "bad": -1.0, "terrible": -1.5,
            "awful": -1.4, "horrible": -1.3, "scam": -2.5, "rug": -2.8
        }
        
        # Source weights
        self.source_weights = {
            SentimentSource.TWITTER: 1.0,
            SentimentSource.REDDIT: 1.2,
            SentimentSource.TELEGRAM: 0.8,
            SentimentSource.NEWS: 1.5,
            SentimentSource.DISCORD: 0.7,
            SentimentSource.YOUTUBE: 1.1
        }
        
        # Sentiment cache
        self.sentiment_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def analyze_token_sentiment(
        self,
        token_address: str,
        time_window_hours: int = 24,
        sources: Optional[List[SentimentSource]] = None
    ) -> Dict[str, Any]:
        """
        Analyze overall sentiment for a token
        
        Args:
            token_address: Token address
            time_window_hours: Time window for analysis
            sources: Sentiment sources to analyze
            
        Returns:
            Comprehensive sentiment analysis
        """
        try:
            self.logger.info(f"Analyzing sentiment for token {token_address}")
            
            # Check cache
            cache_key = f"{token_address}_{time_window_hours}"
            if self._is_cache_valid(cache_key):
                return self.sentiment_cache[cache_key]
            
            # Default sources
            if not sources:
                sources = list(SentimentSource)
            
            # Analyze sentiment from each source
            source_sentiments = {}
            for source in sources:
                sentiment = await self._analyze_source_sentiment(
                    token_address, source, time_window_hours
                )
                source_sentiments[source.value] = sentiment
            
            # Calculate aggregate sentiment
            aggregate_sentiment = self._calculate_aggregate_sentiment(source_sentiments)
            
            # Sentiment trends
            sentiment_trend = await self._calculate_sentiment_trend(
                token_address, time_window_hours
            )
            
            # Sentiment signals
            sentiment_signals = self._generate_sentiment_signals(
                aggregate_sentiment, sentiment_trend
            )
            
            result = {
                "token_address": token_address,
                "analysis_time": datetime.utcnow(),
                "time_window_hours": time_window_hours,
                "aggregate_sentiment": aggregate_sentiment,
                "source_sentiments": source_sentiments,
                "sentiment_trend": sentiment_trend,
                "sentiment_signals": sentiment_signals,
                "confidence": self._calculate_confidence(source_sentiments)
            }
            
            # Cache result
            self.sentiment_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing token sentiment: {str(e)}")
            raise
    
    async def analyze_text_sentiment(self, text: str) -> Dict[str, Any]:
        """
        Analyze sentiment of a single text
        
        Args:
            text: Text to analyze
            
        Returns:
            Sentiment analysis result
        """
        try:
            # Clean and normalize text
            cleaned_text = self._clean_text(text)
            
            # Calculate sentiment score
            sentiment_score = self._calculate_text_sentiment(cleaned_text)
            
            # Classify sentiment
            sentiment_class = self._classify_sentiment(sentiment_score)
            
            # Extract keywords
            keywords = self._extract_keywords(cleaned_text)
            
            return {
                "text": text,
                "cleaned_text": cleaned_text,
                "sentiment_score": sentiment_score,
                "sentiment_class": sentiment_class,
                "keywords": keywords,
                "analysis_time": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing text sentiment: {str(e)}")
            raise
    
    async def get_market_sentiment_overview(self) -> Dict[str, Any]:
        """Get overall market sentiment overview"""
        try:
            # This would analyze sentiment across multiple tokens
            # For now, return a simplified overview
            
            return {
                "overall_sentiment": "neutral",
                "sentiment_score": 0.1,
                "trending_positive": ["BTC", "ETH", "SOL"],
                "trending_negative": ["DOGE", "SHIB"],
                "sentiment_distribution": {
                    "very_positive": 15,
                    "positive": 25,
                    "neutral": 35,
                    "negative": 20,
                    "very_negative": 5
                },
                "analysis_time": datetime.utcnow()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting market sentiment overview: {str(e)}")
            raise
    
    async def _analyze_source_sentiment(
        self,
        token_address: str,
        source: SentimentSource,
        time_window_hours: int
    ) -> Dict[str, Any]:
        """Analyze sentiment from a specific source"""
        try:
            # This would integrate with actual APIs
            # For now, simulate sentiment data
            
            import random
            random.seed(hash(f"{token_address}_{source}_{time_window_hours}"))
            
            # Simulate posts/mentions
            num_mentions = random.randint(10, 100)
            sentiments = []
            
            for _ in range(num_mentions):
                # Generate random sentiment score
                score = random.uniform(-1, 1)
                sentiments.append(score)
            
            avg_sentiment = sum(sentiments) / len(sentiments) if sentiments else 0
            
            return {
                "source": source.value,
                "mentions_count": num_mentions,
                "avg_sentiment": avg_sentiment,
                "sentiment_distribution": {
                    "positive": len([s for s in sentiments if s > 0.2]),
                    "neutral": len([s for s in sentiments if -0.2 <= s <= 0.2]),
                    "negative": len([s for s in sentiments if s < -0.2])
                },
                "weight": self.source_weights[source],
                "weighted_sentiment": avg_sentiment * self.source_weights[source]
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing {source} sentiment: {str(e)}")
            return {
                "source": source.value,
                "mentions_count": 0,
                "avg_sentiment": 0,
                "sentiment_distribution": {"positive": 0, "neutral": 0, "negative": 0},
                "weight": 0,
                "weighted_sentiment": 0
            }
    
    def _calculate_aggregate_sentiment(self, source_sentiments: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate aggregate sentiment across all sources"""
        try:
            total_weighted_sentiment = 0
            total_weight = 0
            total_mentions = 0
            
            for source_data in source_sentiments.values():
                total_weighted_sentiment += source_data["weighted_sentiment"]
                total_weight += source_data["weight"]
                total_mentions += source_data["mentions_count"]
            
            avg_sentiment = total_weighted_sentiment / total_weight if total_weight > 0 else 0
            sentiment_class = self._classify_sentiment(avg_sentiment)
            
            return {
                "score": avg_sentiment,
                "class": sentiment_class,
                "total_mentions": total_mentions,
                "confidence": min(total_mentions / 100, 1.0)  # Confidence based on volume
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating aggregate sentiment: {str(e)}")
            return {"score": 0, "class": "neutral", "total_mentions": 0, "confidence": 0}
    
    async def _calculate_sentiment_trend(
        self,
        token_address: str,
        time_window_hours: int
    ) -> Dict[str, Any]:
        """Calculate sentiment trend over time"""
        try:
            # Simulate historical sentiment data
            import random
            random.seed(hash(f"{token_address}_trend"))
            
            # Generate trend data
            trend_points = []
            for i in range(24):  # Last 24 hours
                timestamp = datetime.utcnow() - timedelta(hours=23-i)
                sentiment = random.uniform(-0.5, 0.5)
                trend_points.append({
                    "timestamp": timestamp,
                    "sentiment": sentiment
                })
            
            # Calculate trend direction
            recent_sentiment = sum(p["sentiment"] for p in trend_points[-6:]) / 6
            older_sentiment = sum(p["sentiment"] for p in trend_points[:6]) / 6
            trend_direction = "improving" if recent_sentiment > older_sentiment else "declining"
            
            return {
                "trend_direction": trend_direction,
                "trend_strength": abs(recent_sentiment - older_sentiment),
                "recent_sentiment": recent_sentiment,
                "trend_points": trend_points[-12:]  # Last 12 hours
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating sentiment trend: {str(e)}")
            return {
                "trend_direction": "stable",
                "trend_strength": 0,
                "recent_sentiment": 0,
                "trend_points": []
            }
    
    def _generate_sentiment_signals(
        self,
        aggregate_sentiment: Dict[str, Any],
        sentiment_trend: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate trading signals based on sentiment"""
        try:
            signals = []
            
            sentiment_score = aggregate_sentiment["score"]
            trend_direction = sentiment_trend["trend_direction"]
            confidence = aggregate_sentiment["confidence"]
            
            # Strong positive sentiment signal
            if sentiment_score > 0.3 and trend_direction == "improving" and confidence > 0.5:
                signals.append({
                    "signal_type": SignalType.BUY,
                    "strength": "moderate",
                    "reason": "Strong positive sentiment with improving trend",
                    "confidence": confidence
                })
            
            # Strong negative sentiment signal
            elif sentiment_score < -0.3 and trend_direction == "declining" and confidence > 0.5:
                signals.append({
                    "signal_type": SignalType.SELL,
                    "strength": "moderate",
                    "reason": "Strong negative sentiment with declining trend",
                    "confidence": confidence
                })
            
            # Sentiment reversal signals
            elif abs(sentiment_score) > 0.4 and trend_direction == "improving" and sentiment_score < 0:
                signals.append({
                    "signal_type": SignalType.BUY,
                    "strength": "weak",
                    "reason": "Potential sentiment reversal from negative to positive",
                    "confidence": confidence * 0.7
                })
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error generating sentiment signals: {str(e)}")
            return []
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for analysis"""
        try:
            # Convert to lowercase
            text = text.lower()
            
            # Remove URLs
            text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
            
            # Remove special characters but keep spaces
            text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
            
            # Remove extra whitespace
            text = ' '.join(text.split())
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error cleaning text: {str(e)}")
            return text
    
    def _calculate_text_sentiment(self, text: str) -> float:
        """Calculate sentiment score for text"""
        try:
            words = text.split()
            sentiment_score = 0
            word_count = 0
            
            for word in words:
                if word in self.positive_keywords:
                    sentiment_score += self.positive_keywords[word]
                    word_count += 1
                elif word in self.negative_keywords:
                    sentiment_score += self.negative_keywords[word]
                    word_count += 1
            
            # Normalize by word count
            if word_count > 0:
                sentiment_score = sentiment_score / word_count
            
            # Clamp to [-1, 1] range
            sentiment_score = max(-1, min(1, sentiment_score))
            
            return sentiment_score
            
        except Exception as e:
            self.logger.error(f"Error calculating text sentiment: {str(e)}")
            return 0.0
    
    def _classify_sentiment(self, score: float) -> str:
        """Classify sentiment score into categories"""
        if score >= 0.4:
            return SentimentScore.VERY_POSITIVE
        elif score >= 0.1:
            return SentimentScore.POSITIVE
        elif score <= -0.4:
            return SentimentScore.VERY_NEGATIVE
        elif score <= -0.1:
            return SentimentScore.NEGATIVE
        else:
            return SentimentScore.NEUTRAL
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text"""
        try:
            words = text.split()
            keywords = []
            
            for word in words:
                if word in self.positive_keywords or word in self.negative_keywords:
                    keywords.append(word)
            
            return list(set(keywords))  # Remove duplicates
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {str(e)}")
            return []
    
    def _calculate_confidence(self, source_sentiments: Dict[str, Any]) -> float:
        """Calculate confidence based on data quality"""
        try:
            total_mentions = sum(s["mentions_count"] for s in source_sentiments.values())
            source_count = len([s for s in source_sentiments.values() if s["mentions_count"] > 0])
            
            # Base confidence on volume and source diversity
            volume_confidence = min(total_mentions / 50, 1.0)
            diversity_confidence = source_count / len(SentimentSource)
            
            return (volume_confidence + diversity_confidence) / 2
            
        except Exception as e:
            self.logger.error(f"Error calculating confidence: {str(e)}")
            return 0.0
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid"""
        try:
            if cache_key not in self.sentiment_cache:
                return False
            
            cache_time = self.sentiment_cache[cache_key]["analysis_time"]
            age = (datetime.utcnow() - cache_time).total_seconds()
            
            return age < self.cache_ttl
            
        except Exception as e:
            self.logger.error(f"Error checking cache validity: {str(e)}")
            return False
