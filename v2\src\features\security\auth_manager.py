"""
🔐 Authentication Manager

Comprehensive authentication system with JWT token management,
user registration, login, role-based access control, and session management.
"""

import asyncio
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import jwt
import bcrypt
from passlib.context import CryptContext

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...database.models import User
from ...shared.types import UserRole

logger = get_logger(__name__)
settings = get_settings()


class TokenType(str, Enum):
    """JWT token types"""
    ACCESS = "access"
    REFRESH = "refresh"
    RESET = "reset"
    VERIFICATION = "verification"


class SessionStatus(str, Enum):
    """Session status"""
    ACTIVE = "active"
    EXPIRED = "expired"
    REVOKED = "revoked"
    INVALID = "invalid"


class JWTManager:
    """
    🔑 JWT Manager
    
    Handles JWT token creation, validation, and management
    """
    
    def __init__(self):
        self.logger = logger
        self.secret_key = settings.jwt_secret_key
        self.algorithm = "HS256"
        
        # Token expiration times
        self.access_token_expire = timedelta(hours=1)
        self.refresh_token_expire = timedelta(days=30)
        self.reset_token_expire = timedelta(hours=1)
        self.verification_token_expire = timedelta(days=7)
        
        # Token blacklist (in production, use Redis)
        self.blacklisted_tokens: set = set()
    
    def create_access_token(self, user_id: str, user_role: UserRole) -> str:
        """Create access token"""
        try:
            payload = {
                "user_id": user_id,
                "user_role": user_role.value,
                "token_type": TokenType.ACCESS.value,
                "exp": datetime.utcnow() + self.access_token_expire,
                "iat": datetime.utcnow(),
                "jti": secrets.token_urlsafe(32)  # JWT ID for revocation
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            return token
            
        except Exception as e:
            self.logger.error(f"Error creating access token: {str(e)}")
            raise
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create refresh token"""
        try:
            payload = {
                "user_id": user_id,
                "token_type": TokenType.REFRESH.value,
                "exp": datetime.utcnow() + self.refresh_token_expire,
                "iat": datetime.utcnow(),
                "jti": secrets.token_urlsafe(32)
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            return token
            
        except Exception as e:
            self.logger.error(f"Error creating refresh token: {str(e)}")
            raise
    
    def create_reset_token(self, user_id: str) -> str:
        """Create password reset token"""
        try:
            payload = {
                "user_id": user_id,
                "token_type": TokenType.RESET.value,
                "exp": datetime.utcnow() + self.reset_token_expire,
                "iat": datetime.utcnow(),
                "jti": secrets.token_urlsafe(32)
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
            return token
            
        except Exception as e:
            self.logger.error(f"Error creating reset token: {str(e)}")
            raise
    
    def verify_token(self, token: str, expected_type: TokenType = None) -> Dict[str, Any]:
        """Verify and decode JWT token"""
        try:
            # Check if token is blacklisted
            if token in self.blacklisted_tokens:
                return {"valid": False, "error": "Token has been revoked"}
            
            # Decode token
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type if specified
            if expected_type and payload.get("token_type") != expected_type.value:
                return {"valid": False, "error": f"Invalid token type. Expected {expected_type.value}"}
            
            # Check expiration
            if datetime.utcnow() > datetime.fromtimestamp(payload["exp"]):
                return {"valid": False, "error": "Token has expired"}
            
            return {
                "valid": True,
                "payload": payload,
                "user_id": payload.get("user_id"),
                "user_role": payload.get("user_role"),
                "token_type": payload.get("token_type"),
                "jti": payload.get("jti")
            }
            
        except jwt.ExpiredSignatureError:
            return {"valid": False, "error": "Token has expired"}
        except jwt.InvalidTokenError:
            return {"valid": False, "error": "Invalid token"}
        except Exception as e:
            self.logger.error(f"Error verifying token: {str(e)}")
            return {"valid": False, "error": "Token verification failed"}
    
    def revoke_token(self, token: str) -> bool:
        """Revoke a token by adding it to blacklist"""
        try:
            # In production, store in Redis with expiration
            self.blacklisted_tokens.add(token)
            return True
            
        except Exception as e:
            self.logger.error(f"Error revoking token: {str(e)}")
            return False
    
    def refresh_access_token(self, refresh_token: str) -> Optional[Dict[str, str]]:
        """Create new access token from refresh token"""
        try:
            # Verify refresh token
            token_data = self.verify_token(refresh_token, TokenType.REFRESH)
            
            if not token_data["valid"]:
                return None
            
            user_id = token_data["user_id"]
            
            # Get user to check if still active
            user = User.get(user_id)
            if not user or not user.is_active:
                return None
            
            # Create new access token
            new_access_token = self.create_access_token(user_id, user.role)
            
            return {
                "access_token": new_access_token,
                "token_type": "bearer"
            }
            
        except Exception as e:
            self.logger.error(f"Error refreshing access token: {str(e)}")
            return None


class AuthManager:
    """
    🔐 Authentication Manager
    
    Handles user authentication, registration, login, and session management
    """
    
    def __init__(self):
        self.logger = logger
        self.jwt_manager = JWTManager()
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        
        # Authentication settings
        self.max_login_attempts = 5
        self.lockout_duration = timedelta(minutes=30)
        self.password_min_length = 8
        
        # Active sessions tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def register_user(
        self,
        email: str,
        password: str,
        username: str,
        role: UserRole = UserRole.USER
    ) -> Dict[str, Any]:
        """Register a new user"""
        try:
            self.logger.info(f"Registering new user: {email}")
            
            # Validate input
            validation_result = await self._validate_registration_data(email, password, username)
            if not validation_result["valid"]:
                return {"success": False, "error": validation_result["error"]}
            
            # Check if user already exists
            existing_user = await User.find_one({"email": email})
            if existing_user:
                return {"success": False, "error": "User with this email already exists"}
            
            # Hash password
            password_hash = self.pwd_context.hash(password)
            
            # Create user
            user = User(
                email=email,
                username=username,
                password_hash=password_hash,
                role=role,
                is_active=True,
                is_verified=False,
                created_at=datetime.utcnow(),
                last_login=None,
                login_attempts=0,
                locked_until=None
            )
            
            await user.save()
            
            # Create verification token
            verification_token = self.jwt_manager.create_reset_token(str(user.id))
            
            self.logger.info(f"User registered successfully: {user.id}")
            
            return {
                "success": True,
                "user_id": str(user.id),
                "verification_token": verification_token,
                "message": "User registered successfully. Please verify your email."
            }
            
        except Exception as e:
            self.logger.error(f"Error registering user: {str(e)}")
            return {"success": False, "error": "Registration failed"}
    
    async def login_user(self, email: str, password: str, ip_address: str = None) -> Dict[str, Any]:
        """Authenticate user login"""
        try:
            self.logger.info(f"Login attempt for user: {email}")
            
            # Get user
            user = await User.find_one({"email": email})
            if not user:
                return {"success": False, "error": "Invalid credentials"}
            
            # Check if account is locked
            if user.locked_until and datetime.utcnow() < user.locked_until:
                return {
                    "success": False,
                    "error": f"Account locked until {user.locked_until}",
                    "locked_until": user.locked_until
                }
            
            # Check if account is active
            if not user.is_active:
                return {"success": False, "error": "Account is deactivated"}
            
            # Verify password
            if not self.pwd_context.verify(password, user.password_hash):
                # Increment login attempts
                user.login_attempts += 1
                
                # Lock account if too many attempts
                if user.login_attempts >= self.max_login_attempts:
                    user.locked_until = datetime.utcnow() + self.lockout_duration
                    self.logger.warning(f"Account locked due to failed login attempts: {email}")
                
                await user.save()
                
                return {"success": False, "error": "Invalid credentials"}
            
            # Reset login attempts on successful login
            user.login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.utcnow()
            await user.save()
            
            # Create tokens
            access_token = self.jwt_manager.create_access_token(str(user.id), user.role)
            refresh_token = self.jwt_manager.create_refresh_token(str(user.id))
            
            # Create session
            session_id = secrets.token_urlsafe(32)
            session_data = {
                "user_id": str(user.id),
                "email": user.email,
                "role": user.role.value,
                "ip_address": ip_address,
                "created_at": datetime.utcnow(),
                "last_activity": datetime.utcnow(),
                "status": SessionStatus.ACTIVE
            }
            
            self.active_sessions[session_id] = session_data
            
            self.logger.info(f"User logged in successfully: {user.id}")
            
            return {
                "success": True,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "session_id": session_id,
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "username": user.username,
                    "role": user.role.value,
                    "is_verified": user.is_verified
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error during login: {str(e)}")
            return {"success": False, "error": "Login failed"}
    
    async def logout_user(self, access_token: str, session_id: str = None) -> Dict[str, Any]:
        """Logout user and invalidate tokens"""
        try:
            # Revoke access token
            self.jwt_manager.revoke_token(access_token)
            
            # Remove session
            if session_id and session_id in self.active_sessions:
                self.active_sessions[session_id]["status"] = SessionStatus.REVOKED
                del self.active_sessions[session_id]
            
            return {"success": True, "message": "Logged out successfully"}
            
        except Exception as e:
            self.logger.error(f"Error during logout: {str(e)}")
            return {"success": False, "error": "Logout failed"}
    
    async def verify_email(self, verification_token: str) -> Dict[str, Any]:
        """Verify user email with token"""
        try:
            # Verify token
            token_data = self.jwt_manager.verify_token(verification_token, TokenType.RESET)
            
            if not token_data["valid"]:
                return {"success": False, "error": "Invalid or expired verification token"}
            
            user_id = token_data["user_id"]
            user = await User.get(user_id)
            
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Mark as verified
            user.is_verified = True
            user.verified_at = datetime.utcnow()
            await user.save()
            
            return {"success": True, "message": "Email verified successfully"}
            
        except Exception as e:
            self.logger.error(f"Error verifying email: {str(e)}")
            return {"success": False, "error": "Email verification failed"}

    async def reset_password_request(self, email: str) -> Dict[str, Any]:
        """Request password reset"""
        try:
            user = await User.find_one({"email": email})
            if not user:
                # Don't reveal if email exists
                return {"success": True, "message": "If email exists, reset instructions have been sent"}

            # Create reset token
            reset_token = self.jwt_manager.create_reset_token(str(user.id))

            # In production, send email with reset link
            self.logger.info(f"Password reset requested for user: {user.id}")

            return {
                "success": True,
                "reset_token": reset_token,  # In production, don't return this
                "message": "Password reset instructions sent to email"
            }

        except Exception as e:
            self.logger.error(f"Error requesting password reset: {str(e)}")
            return {"success": False, "error": "Password reset request failed"}

    async def reset_password(self, reset_token: str, new_password: str) -> Dict[str, Any]:
        """Reset password with token"""
        try:
            # Verify reset token
            token_data = self.jwt_manager.verify_token(reset_token, TokenType.RESET)

            if not token_data["valid"]:
                return {"success": False, "error": "Invalid or expired reset token"}

            # Validate new password
            if not self._validate_password(new_password):
                return {"success": False, "error": f"Password must be at least {self.password_min_length} characters"}

            user_id = token_data["user_id"]
            user = await User.get(user_id)

            if not user:
                return {"success": False, "error": "User not found"}

            # Update password
            user.password_hash = self.pwd_context.hash(new_password)
            user.password_changed_at = datetime.utcnow()
            await user.save()

            # Revoke the reset token
            self.jwt_manager.revoke_token(reset_token)

            return {"success": True, "message": "Password reset successfully"}

        except Exception as e:
            self.logger.error(f"Error resetting password: {str(e)}")
            return {"success": False, "error": "Password reset failed"}

    async def change_password(
        self,
        user_id: str,
        current_password: str,
        new_password: str
    ) -> Dict[str, Any]:
        """Change user password"""
        try:
            user = await User.get(user_id)
            if not user:
                return {"success": False, "error": "User not found"}

            # Verify current password
            if not self.pwd_context.verify(current_password, user.password_hash):
                return {"success": False, "error": "Current password is incorrect"}

            # Validate new password
            if not self._validate_password(new_password):
                return {"success": False, "error": f"Password must be at least {self.password_min_length} characters"}

            # Update password
            user.password_hash = self.pwd_context.hash(new_password)
            user.password_changed_at = datetime.utcnow()
            await user.save()

            return {"success": True, "message": "Password changed successfully"}

        except Exception as e:
            self.logger.error(f"Error changing password: {str(e)}")
            return {"success": False, "error": "Password change failed"}

    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information"""
        try:
            if session_id not in self.active_sessions:
                return None

            session = self.active_sessions[session_id]

            # Check if session is expired (1 hour of inactivity)
            if datetime.utcnow() - session["last_activity"] > timedelta(hours=1):
                session["status"] = SessionStatus.EXPIRED
                del self.active_sessions[session_id]
                return None

            # Update last activity
            session["last_activity"] = datetime.utcnow()

            return session

        except Exception as e:
            self.logger.error(f"Error getting session info: {str(e)}")
            return None

    async def revoke_all_sessions(self, user_id: str) -> bool:
        """Revoke all sessions for a user"""
        try:
            sessions_to_remove = []

            for session_id, session_data in self.active_sessions.items():
                if session_data["user_id"] == user_id:
                    sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                self.active_sessions[session_id]["status"] = SessionStatus.REVOKED
                del self.active_sessions[session_id]

            self.logger.info(f"Revoked {len(sessions_to_remove)} sessions for user {user_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error revoking sessions: {str(e)}")
            return False

    async def _validate_registration_data(
        self,
        email: str,
        password: str,
        username: str
    ) -> Dict[str, Any]:
        """Validate registration data"""
        try:
            # Email validation
            if not email or "@" not in email:
                return {"valid": False, "error": "Invalid email address"}

            # Password validation
            if not self._validate_password(password):
                return {"valid": False, "error": f"Password must be at least {self.password_min_length} characters"}

            # Username validation
            if not username or len(username) < 3:
                return {"valid": False, "error": "Username must be at least 3 characters"}

            # Check if username is taken
            existing_username = await User.find_one({"username": username})
            if existing_username:
                return {"valid": False, "error": "Username is already taken"}

            return {"valid": True}

        except Exception as e:
            self.logger.error(f"Error validating registration data: {str(e)}")
            return {"valid": False, "error": "Validation failed"}

    def _validate_password(self, password: str) -> bool:
        """Validate password strength"""
        if not password or len(password) < self.password_min_length:
            return False

        # Add more password complexity rules as needed
        # - Must contain uppercase, lowercase, number, special char
        # - Cannot be common passwords
        # - Cannot be similar to username/email

        return True
