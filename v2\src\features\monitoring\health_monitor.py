"""
🏥 Health Monitor

Comprehensive health monitoring system for database connectivity, external API availability,
service health, and dependency monitoring with automated health checks.
"""

import asyncio
import time
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from motor.motor_asyncio import AsyncIOMotorClient

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...config.database import get_database
from ...shared.constants import MONITORING_CONSTANTS

logger = get_logger(__name__)
settings = get_settings()


class HealthMonitor:
    """
    🏥 Comprehensive health monitoring system
    """
    
    def __init__(self):
        self.settings = settings
        self.is_running = False
        self.start_time = time.time()
        
        # Health status storage
        self.health_status = {
            'overall_status': 'unknown',
            'services': {},
            'dependencies': {},
            'metrics': {},
            'last_check': None,
            'uptime': 0.0
        }
        
        # Health check intervals
        self.check_interval = MONITORING_CONSTANTS.HEALTH_CHECK_INTERVAL
        self.deep_check_interval = MONITORING_CONSTANTS.DEEP_HEALTH_CHECK_INTERVAL
        
        logger.info("HealthMonitor initialized")
    
    async def start(self):
        """Start health monitoring"""
        if self.is_running:
            logger.warning("HealthMonitor already running")
            return
        
        self.is_running = True
        logger.info("Starting HealthMonitor")
        
        # Start background monitoring tasks
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._deep_health_check_loop())
    
    async def stop(self):
        """Stop health monitoring"""
        self.is_running = False
        logger.info("HealthMonitor stopped")
    
    async def _health_check_loop(self):
        """Main health check loop"""
        while self.is_running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(
                    "Error in health check loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(self.check_interval)
    
    async def _deep_health_check_loop(self):
        """Deep health check loop (less frequent)"""
        while self.is_running:
            try:
                await self._perform_deep_health_checks()
                await asyncio.sleep(self.deep_check_interval)
                
            except Exception as e:
                logger.error(
                    "Error in deep health check loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(self.deep_check_interval)
    
    async def _perform_health_checks(self):
        """Perform standard health checks"""
        try:
            services = {}
            
            # Database health check
            services['database'] = await self._check_database_health()
            
            # Cache health check (Redis)
            services['cache'] = await self._check_cache_health()
            
            # Basic API health check
            services['api'] = await self._check_api_health()
            
            # Update health status
            self.health_status.update({
                'services': services,
                'last_check': datetime.utcnow(),
                'uptime': time.time() - self.start_time,
                'overall_status': self._calculate_overall_status(services)
            })
            
            logger.debug(
                "Health check completed",
                overall_status=self.health_status['overall_status'],
                services=services
            )
            
        except Exception as e:
            logger.error(
                "Error performing health checks",
                error=str(e)
            )
            self.health_status['overall_status'] = 'unhealthy'
    
    async def _perform_deep_health_checks(self):
        """Perform comprehensive health checks"""
        try:
            dependencies = {}
            
            # External API health checks
            dependencies['dune_api'] = await self._check_dune_api_health()
            dependencies['jupiter_api'] = await self._check_jupiter_api_health()
            dependencies['telegram_api'] = await self._check_telegram_api_health()
            dependencies['solana_rpc'] = await self._check_solana_rpc_health()
            
            # Update dependencies status
            self.health_status['dependencies'] = dependencies
            
            logger.debug(
                "Deep health check completed",
                dependencies=dependencies
            )
            
        except Exception as e:
            logger.error(
                "Error performing deep health checks",
                error=str(e)
            )
    
    async def _check_database_health(self) -> bool:
        """Check MongoDB database health"""
        try:
            db = await get_database()
            if db is None:
                return False
            
            # Perform a simple ping
            await db.command("ping")
            return True
            
        except Exception as e:
            logger.error(
                "Database health check failed",
                error=str(e)
            )
            return False
    
    async def _check_cache_health(self) -> bool:
        """Check Redis cache health"""
        try:
            # This would check Redis connection
            # For now, return True as placeholder
            return True
            
        except Exception as e:
            logger.error(
                "Cache health check failed",
                error=str(e)
            )
            return False
    
    async def _check_api_health(self) -> bool:
        """Check basic API health"""
        try:
            # Check if the application is responding
            return True
            
        except Exception as e:
            logger.error(
                "API health check failed",
                error=str(e)
            )
            return False
    
    async def _check_dune_api_health(self) -> bool:
        """Check Dune Analytics API health"""
        try:
            if not self.settings.dune_api_key:
                return False
            
            async with aiohttp.ClientSession() as session:
                headers = {"X-Dune-API-Key": self.settings.dune_api_key}
                async with session.get(
                    "https://api.dune.com/api/v1/query/1/results",
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status < 500
                    
        except Exception as e:
            logger.error(
                "Dune API health check failed",
                error=str(e)
            )
            return False
    
    async def _check_jupiter_api_health(self) -> bool:
        """Check Jupiter API health"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.settings.jupiter_api_url}/tokens",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status < 500
                    
        except Exception as e:
            logger.error(
                "Jupiter API health check failed",
                error=str(e)
            )
            return False
    
    async def _check_telegram_api_health(self) -> bool:
        """Check Telegram Bot API health"""
        try:
            if not self.settings.telegram_bot_token:
                return False
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"https://api.telegram.org/bot{self.settings.telegram_bot_token}/getMe",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(
                "Telegram API health check failed",
                error=str(e)
            )
            return False
    
    async def _check_solana_rpc_health(self) -> bool:
        """Check Solana RPC health"""
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "getHealth"
                }
                async with session.post(
                    self.settings.solana_rpc_url,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(
                "Solana RPC health check failed",
                error=str(e)
            )
            return False
    
    def _calculate_overall_status(self, services: Dict[str, bool]) -> str:
        """Calculate overall health status"""
        if not services:
            return 'unknown'
        
        # Critical services that must be healthy
        critical_services = ['database', 'api']
        
        # Check if all critical services are healthy
        critical_healthy = all(
            services.get(service, False) 
            for service in critical_services
        )
        
        if not critical_healthy:
            return 'unhealthy'
        
        # Check overall health percentage
        total_services = len(services)
        healthy_services = sum(1 for status in services.values() if status)
        health_percentage = (healthy_services / total_services) * 100
        
        if health_percentage >= 90:
            return 'healthy'
        elif health_percentage >= 70:
            return 'degraded'
        else:
            return 'unhealthy'
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current health status"""
        return self.health_status.copy()
    
    async def get_service_health(self, service_name: str) -> Optional[bool]:
        """Get health status of specific service"""
        return self.health_status['services'].get(service_name)
    
    async def get_dependency_health(self, dependency_name: str) -> Optional[bool]:
        """Get health status of specific dependency"""
        return self.health_status['dependencies'].get(dependency_name)
    
    async def is_healthy(self) -> bool:
        """Check if system is overall healthy"""
        return self.health_status['overall_status'] == 'healthy'
    
    async def get_uptime(self) -> float:
        """Get system uptime in seconds"""
        return time.time() - self.start_time
