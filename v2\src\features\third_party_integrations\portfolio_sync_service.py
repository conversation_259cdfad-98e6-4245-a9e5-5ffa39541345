"""
Portfolio Sync Service

Synchronizes portfolio data across multiple exchanges and platforms,
providing unified portfolio view and cross-platform analytics.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

from src.shared.types import PortfolioData, PositionData
from src.features.third_party_integrations.exchange_integrator import (
    ExchangeIntegrator, ExchangeType, ExchangeBalance
)
from src.features.paper_trading.portfolio_manager import PortfolioManager
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


class SyncStatus(Enum):
    """Portfolio sync status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PARTIAL = "partial"


@dataclass
class SyncConfig:
    """Portfolio sync configuration"""
    sync_interval: int = 300  # 5 minutes
    auto_sync: bool = True
    include_dust: bool = False  # Include very small balances
    dust_threshold: float = 1.0  # USD value threshold for dust
    max_sync_time: int = 120  # Maximum sync time in seconds
    retry_attempts: int = 3


@dataclass
class SyncResult:
    """Portfolio sync result"""
    user_id: str
    status: SyncStatus
    exchanges_synced: List[ExchangeType]
    total_positions: int
    total_value_usd: float
    sync_duration: float
    errors: List[str]
    warnings: List[str]
    timestamp: datetime


@dataclass
class UnifiedPosition:
    """Unified position across exchanges"""
    asset: str
    total_quantity: float
    total_value_usd: float
    exchange_positions: Dict[ExchangeType, ExchangeBalance]
    average_price: Optional[float] = None
    unrealized_pnl: Optional[float] = None
    allocation_percentage: float = 0.0


@dataclass
class PortfolioSyncReport:
    """Comprehensive portfolio sync report"""
    user_id: str
    sync_result: SyncResult
    unified_positions: List[UnifiedPosition]
    total_portfolio_value: float
    exchange_breakdown: Dict[ExchangeType, float]
    asset_allocation: Dict[str, float]
    sync_history: List[SyncResult]


class PortfolioSyncService:
    """
    Synchronizes portfolio data across multiple exchanges and platforms,
    providing unified portfolio view and analytics.
    """
    
    def __init__(
        self,
        exchange_integrator: ExchangeIntegrator,
        portfolio_manager: PortfolioManager,
        cache_manager: CacheManager,
        config: Optional[SyncConfig] = None
    ):
        self.exchange_integrator = exchange_integrator
        self.portfolio_manager = portfolio_manager
        self.cache_manager = cache_manager
        self.config = config or SyncConfig()
        self.logger = logger.bind(service="portfolio_sync")
    
    async def sync_user_portfolio(
        self,
        user_id: str,
        exchanges: Optional[List[ExchangeType]] = None,
        force_sync: bool = False
    ) -> SyncResult:
        """
        Synchronize user portfolio across exchanges.
        
        Args:
            user_id: User identifier
            exchanges: List of exchanges to sync (if None, sync all configured)
            force_sync: Force sync even if recently synced
            
        Returns:
            Portfolio sync result
        """
        try:
            start_time = datetime.utcnow()
            
            # Check if sync is needed
            if not force_sync and not await self._should_sync(user_id):
                last_sync = await self._get_last_sync_result(user_id)
                if last_sync:
                    self.logger.info("Portfolio sync skipped - recent sync available", user_id=user_id)
                    return last_sync
            
            self.logger.info("Starting portfolio sync", user_id=user_id, exchanges=exchanges)
            
            # Get balances from all exchanges
            exchange_balances = await self.exchange_integrator.get_portfolio_balances(
                user_id=user_id,
                exchanges=exchanges
            )
            
            # Process and unify positions
            unified_positions = await self._unify_positions(exchange_balances)
            
            # Filter out dust if configured
            if not self.config.include_dust:
                unified_positions = self._filter_dust_positions(unified_positions)
            
            # Calculate total portfolio value
            total_value = sum(pos.total_value_usd for pos in unified_positions)
            
            # Update internal portfolio
            await self._update_internal_portfolio(user_id, unified_positions)
            
            # Create sync result
            sync_duration = (datetime.utcnow() - start_time).total_seconds()
            sync_result = SyncResult(
                user_id=user_id,
                status=SyncStatus.COMPLETED,
                exchanges_synced=list(exchange_balances.keys()),
                total_positions=len(unified_positions),
                total_value_usd=total_value,
                sync_duration=sync_duration,
                errors=[],
                warnings=[],
                timestamp=datetime.utcnow()
            )
            
            # Cache sync result
            await self._cache_sync_result(user_id, sync_result)
            
            # Store sync history
            await self._store_sync_history(user_id, sync_result)
            
            self.logger.info(
                "Portfolio sync completed",
                user_id=user_id,
                positions=len(unified_positions),
                total_value=total_value,
                duration=sync_duration
            )
            
            return sync_result
            
        except Exception as e:
            # Create failed sync result
            sync_duration = (datetime.utcnow() - start_time).total_seconds()
            sync_result = SyncResult(
                user_id=user_id,
                status=SyncStatus.FAILED,
                exchanges_synced=[],
                total_positions=0,
                total_value_usd=0.0,
                sync_duration=sync_duration,
                errors=[str(e)],
                warnings=[],
                timestamp=datetime.utcnow()
            )
            
            await self._cache_sync_result(user_id, sync_result)
            
            self.logger.error("Portfolio sync failed", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Portfolio sync failed")
    
    async def get_unified_portfolio(
        self,
        user_id: str,
        include_exchange_breakdown: bool = True
    ) -> PortfolioSyncReport:
        """
        Get unified portfolio view across all exchanges.
        
        Args:
            user_id: User identifier
            include_exchange_breakdown: Include per-exchange breakdown
            
        Returns:
            Comprehensive portfolio sync report
        """
        try:
            # Get latest sync result
            sync_result = await self._get_last_sync_result(user_id)
            
            if not sync_result or sync_result.status != SyncStatus.COMPLETED:
                # Trigger sync if no recent successful sync
                sync_result = await self.sync_user_portfolio(user_id)
            
            # Get unified positions
            unified_positions = await self._get_cached_unified_positions(user_id)
            
            if not unified_positions:
                # Fallback to fresh sync
                sync_result = await self.sync_user_portfolio(user_id, force_sync=True)
                unified_positions = await self._get_cached_unified_positions(user_id)
            
            # Calculate portfolio metrics
            total_value = sum(pos.total_value_usd for pos in unified_positions)
            
            # Exchange breakdown
            exchange_breakdown = {}
            if include_exchange_breakdown:
                for position in unified_positions:
                    for exchange, balance in position.exchange_positions.items():
                        if exchange not in exchange_breakdown:
                            exchange_breakdown[exchange] = 0.0
                        exchange_breakdown[exchange] += balance.total * (position.total_value_usd / position.total_quantity) if position.total_quantity > 0 else 0
            
            # Asset allocation
            asset_allocation = {}
            for position in unified_positions:
                allocation_pct = (position.total_value_usd / total_value * 100) if total_value > 0 else 0
                asset_allocation[position.asset] = allocation_pct
                position.allocation_percentage = allocation_pct
            
            # Get sync history
            sync_history = await self._get_sync_history(user_id, limit=10)
            
            report = PortfolioSyncReport(
                user_id=user_id,
                sync_result=sync_result,
                unified_positions=unified_positions,
                total_portfolio_value=total_value,
                exchange_breakdown=exchange_breakdown,
                asset_allocation=asset_allocation,
                sync_history=sync_history
            )
            
            self.logger.info(
                "Unified portfolio retrieved",
                user_id=user_id,
                total_value=total_value,
                positions=len(unified_positions)
            )
            
            return report
            
        except Exception as e:
            self.logger.error("Failed to get unified portfolio", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get unified portfolio")
    
    async def schedule_auto_sync(self, user_id: str) -> bool:
        """
        Schedule automatic portfolio sync for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if scheduling was successful
        """
        try:
            if not self.config.auto_sync:
                return False
            
            # Store auto-sync schedule
            schedule_key = f"auto_sync_schedule:{user_id}"
            schedule_data = {
                "user_id": user_id,
                "interval": self.config.sync_interval,
                "next_sync": (datetime.utcnow() + timedelta(seconds=self.config.sync_interval)).isoformat(),
                "enabled": True
            }
            
            await self.cache_manager.set(schedule_key, schedule_data, ttl=86400)  # 24 hours
            
            self.logger.info("Auto-sync scheduled", user_id=user_id, interval=self.config.sync_interval)
            return True
            
        except Exception as e:
            self.logger.error("Failed to schedule auto-sync", user_id=user_id, error=str(e))
            return False
    
    async def get_portfolio_analytics(
        self,
        user_id: str,
        timeframe: str = "30d"
    ) -> Dict[str, Any]:
        """
        Get portfolio analytics across exchanges.
        
        Args:
            user_id: User identifier
            timeframe: Analytics timeframe
            
        Returns:
            Portfolio analytics data
        """
        try:
            # Get sync history for the timeframe
            days = int(timeframe.replace('d', '')) if 'd' in timeframe else 30
            start_date = datetime.utcnow() - timedelta(days=days)
            
            sync_history = await self._get_sync_history(user_id, start_date=start_date)
            
            if not sync_history:
                return {
                    "total_value_history": [],
                    "exchange_performance": {},
                    "asset_performance": {},
                    "sync_reliability": 0.0
                }
            
            # Calculate analytics
            total_value_history = [
                {
                    "timestamp": result.timestamp,
                    "total_value": result.total_value_usd
                }
                for result in sync_history
            ]
            
            # Sync reliability
            successful_syncs = len([r for r in sync_history if r.status == SyncStatus.COMPLETED])
            sync_reliability = (successful_syncs / len(sync_history) * 100) if sync_history else 0
            
            analytics = {
                "timeframe": timeframe,
                "total_value_history": total_value_history,
                "sync_count": len(sync_history),
                "sync_reliability": sync_reliability,
                "average_sync_duration": sum(r.sync_duration for r in sync_history) / len(sync_history) if sync_history else 0,
                "last_sync": sync_history[0].timestamp if sync_history else None
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error("Failed to get portfolio analytics", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get portfolio analytics")
    
    async def _unify_positions(
        self,
        exchange_balances: Dict[ExchangeType, List[ExchangeBalance]]
    ) -> List[UnifiedPosition]:
        """Unify positions across exchanges"""
        asset_positions = {}
        
        # Group balances by asset
        for exchange, balances in exchange_balances.items():
            for balance in balances:
                asset = balance.asset
                
                if asset not in asset_positions:
                    asset_positions[asset] = UnifiedPosition(
                        asset=asset,
                        total_quantity=0.0,
                        total_value_usd=0.0,
                        exchange_positions={}
                    )
                
                asset_positions[asset].exchange_positions[exchange] = balance
                asset_positions[asset].total_quantity += balance.total
                
                # Add USD value if available
                if balance.usd_value:
                    asset_positions[asset].total_value_usd += balance.usd_value
                else:
                    # Estimate USD value (would need price data in production)
                    estimated_value = balance.total * 1.0  # Placeholder
                    asset_positions[asset].total_value_usd += estimated_value
        
        return list(asset_positions.values())
    
    def _filter_dust_positions(
        self,
        positions: List[UnifiedPosition]
    ) -> List[UnifiedPosition]:
        """Filter out dust positions below threshold"""
        return [
            pos for pos in positions
            if pos.total_value_usd >= self.config.dust_threshold
        ]
    
    async def _update_internal_portfolio(
        self,
        user_id: str,
        unified_positions: List[UnifiedPosition]
    ) -> None:
        """Update internal portfolio with unified positions"""
        try:
            # Convert unified positions to internal format
            portfolio_positions = []
            
            for position in unified_positions:
                # Create position data for internal portfolio
                position_data = PositionData(
                    token_address=f"unified_{position.asset}",  # Placeholder
                    token_symbol=position.asset,
                    quantity=position.total_quantity,
                    average_price=position.average_price or 0.0,
                    current_price=position.total_value_usd / position.total_quantity if position.total_quantity > 0 else 0.0,
                    unrealized_pnl=position.unrealized_pnl or 0.0,
                    created_at=datetime.utcnow()
                )
                portfolio_positions.append(position_data)
            
            # Update portfolio manager
            # Note: This would require extending portfolio manager to handle external positions
            
            self.logger.info("Internal portfolio updated", user_id=user_id, positions=len(portfolio_positions))
            
        except Exception as e:
            self.logger.error("Failed to update internal portfolio", user_id=user_id, error=str(e))
    
    async def _should_sync(self, user_id: str) -> bool:
        """Check if portfolio sync is needed"""
        try:
            last_sync = await self._get_last_sync_result(user_id)
            
            if not last_sync:
                return True
            
            # Check if enough time has passed
            time_since_sync = (datetime.utcnow() - last_sync.timestamp).total_seconds()
            return time_since_sync >= self.config.sync_interval
            
        except Exception:
            return True
    
    async def _cache_sync_result(self, user_id: str, sync_result: SyncResult) -> None:
        """Cache sync result"""
        try:
            cache_key = f"portfolio_sync_result:{user_id}"
            await self.cache_manager.set(
                cache_key,
                sync_result.__dict__,
                ttl=self.config.sync_interval * 2
            )
        except Exception as e:
            self.logger.error("Failed to cache sync result", user_id=user_id, error=str(e))
    
    async def _get_last_sync_result(self, user_id: str) -> Optional[SyncResult]:
        """Get last sync result from cache"""
        try:
            cache_key = f"portfolio_sync_result:{user_id}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                # Convert timestamp string back to datetime
                cached_data['timestamp'] = datetime.fromisoformat(cached_data['timestamp'])
                return SyncResult(**cached_data)
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get last sync result", user_id=user_id, error=str(e))
            return None
    
    async def _store_sync_history(self, user_id: str, sync_result: SyncResult) -> None:
        """Store sync result in history"""
        try:
            history_key = f"portfolio_sync_history:{user_id}"
            
            # Get existing history
            history = await self.cache_manager.get(history_key) or []
            
            # Add new result
            history.insert(0, sync_result.__dict__)
            
            # Keep only last 100 results
            history = history[:100]
            
            # Store updated history
            await self.cache_manager.set(history_key, history, ttl=86400 * 30)  # 30 days
            
        except Exception as e:
            self.logger.error("Failed to store sync history", user_id=user_id, error=str(e))
    
    async def _get_sync_history(
        self,
        user_id: str,
        limit: int = 50,
        start_date: Optional[datetime] = None
    ) -> List[SyncResult]:
        """Get sync history for user"""
        try:
            history_key = f"portfolio_sync_history:{user_id}"
            history_data = await self.cache_manager.get(history_key) or []
            
            # Convert to SyncResult objects
            history = []
            for item in history_data:
                item['timestamp'] = datetime.fromisoformat(item['timestamp'])
                sync_result = SyncResult(**item)
                
                # Filter by start date if provided
                if start_date and sync_result.timestamp < start_date:
                    continue
                
                history.append(sync_result)
                
                if len(history) >= limit:
                    break
            
            return history
            
        except Exception as e:
            self.logger.error("Failed to get sync history", user_id=user_id, error=str(e))
            return []
    
    async def _get_cached_unified_positions(self, user_id: str) -> List[UnifiedPosition]:
        """Get cached unified positions"""
        try:
            cache_key = f"unified_positions:{user_id}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return [UnifiedPosition(**pos) for pos in cached_data]
            
            return []
            
        except Exception as e:
            self.logger.error("Failed to get cached positions", user_id=user_id, error=str(e))
            return []
