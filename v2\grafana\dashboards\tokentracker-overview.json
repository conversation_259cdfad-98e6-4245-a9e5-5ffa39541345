{"dashboard": {"id": null, "title": "TokenTracker V2 - Overview", "tags": ["tokentracker", "overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "System Health", "type": "stat", "targets": [{"expr": "tokentracker_health_status", "legendFormat": "{{service}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "Unhealthy"}}, "type": "value"}, {"options": {"1": {"text": "Healthy"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Application Uptime", "type": "stat", "targets": [{"expr": "tokentracker_uptime_seconds", "legendFormat": "Uptime"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "CPU Usage", "type": "timeseries", "targets": [{"expr": "system_cpu_usage_percent", "legendFormat": "CPU Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}}, {"id": 4, "title": "Memory Usage", "type": "timeseries", "targets": [{"expr": "system_memory_usage_percent", "legendFormat": "Memory Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}}, {"id": 5, "title": "Disk Usage", "type": "timeseries", "targets": [{"expr": "system_disk_usage_percent", "legendFormat": "Disk Usage %"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}}, {"id": 6, "title": "HTTP Requests Rate", "type": "timeseries", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{method}} {{endpoint}}"}], "fieldConfig": {"defaults": {"unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "HTTP Response Times", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "Active Alerts", "type": "stat", "targets": [{"expr": "sum(alerts_active_count)", "legendFormat": "Total Active Alerts"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 24}}, {"id": 9, "title": "Database Query Rate", "type": "timeseries", "targets": [{"expr": "rate(database_queries_total[5m])", "legendFormat": "{{operation}} {{collection}}"}], "fieldConfig": {"defaults": {"unit": "qps"}}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 24}}, {"id": 10, "title": "<PERSON><PERSON> Hit Rate", "type": "stat", "targets": [{"expr": "cache_hit_rate", "legendFormat": "<PERSON><PERSON> Hit Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 70}, {"color": "green", "value": 90}]}}}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 24}}]}}