"""
🗄️ Database Configuration

MongoDB Atlas configuration following DATABASE_PATTERNS.md guidelines
with connection management, optimization, and monitoring.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from beanie import init_beanie

from .settings import get_settings
from .logging_config import get_logger
from ..database.models import *

logger = get_logger(__name__)


class DatabaseConfig:
    """
    🗄️ Database configuration and connection management
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.client: Optional[AsyncIOMotorClient] = None
        self.read_client: Optional[AsyncIOMotorClient] = None  # Read replica client
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.read_database: Optional[AsyncIOMotorDatabase] = None  # Read replica database
        self.is_connected = False
        self.read_replica_enabled = False

        # Query performance monitoring
        self.query_stats = {
            "total_queries": 0,
            "slow_queries": 0,
            "read_queries": 0,
            "write_queries": 0,
            "avg_query_time": 0.0,
            "query_times": []
        }
        
    async def connect(self) -> None:
        """
        🔌 Establish database connection with optimized settings
        """
        try:
            # Connection options optimized for production
            connection_options = {
                "maxPoolSize": 20 if self.settings.is_production else 10,
                "minPoolSize": 5 if self.settings.is_production else 2,
                "maxIdleTimeMS": 30000,
                "serverSelectionTimeoutMS": 5000,
                "socketTimeoutMS": 45000,
                "connectTimeoutMS": 10000,
                "retryWrites": True,
                "w": "majority",
                "readPreference": "primaryPreferred",
                "heartbeatFrequencyMS": 10000,
                "maxStalenessSeconds": 120,
            }
            
            # Add authentication if credentials are in URI
            self.client = AsyncIOMotorClient(
                self.settings.mongodb_uri,
                **connection_options
            )
            
            # Test connection
            await self.client.admin.command('ping')
            
            # Get database
            self.database = self.client[self.settings.mongodb_db_name]
            
            # Initialize Beanie ODM
            await self._initialize_beanie()
            
            # Create indexes
            await self._create_indexes()

            # Setup read replica if configured
            await self._setup_read_replica()

            self.is_connected = True
            logger.info(
                "Database connected successfully",
                database=self.settings.mongodb_db_name,
                connection_options=connection_options,
                read_replica_enabled=self.read_replica_enabled
            )
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(
                "Database connection failed",
                error=str(e),
                uri_host=self.settings.mongodb_uri.split('@')[-1].split('/')[0] if '@' in self.settings.mongodb_uri else "unknown"
            )
            raise
        except Exception as e:
            logger.error(
                "Unexpected database connection error",
                error=str(e),
                error_type=type(e).__name__
            )
            raise
    
    async def _initialize_beanie(self) -> None:
        """
        🔧 Initialize Beanie ODM with all models
        """
        try:
            # Import all model classes
            from ..database.models.token import Token
            from ..database.models.signal import Signal
            from ..database.models.trade import Trade
            from ..database.models.portfolio import Portfolio
            from ..database.models.user import User
            from ..database.models.query_result import QueryResult
            from ..database.models.performance_metric import PerformanceMetric
            
            # Initialize Beanie
            await init_beanie(
                database=self.database,
                document_models=[
                    Token,
                    Signal,
                    Trade,
                    Portfolio,
                    User,
                    QueryResult,
                    PerformanceMetric
                ]
            )
            
            logger.info("Beanie ODM initialized successfully")
            
        except Exception as e:
            logger.error(
                "Failed to initialize Beanie ODM",
                error=str(e)
            )
            raise
    
    async def _create_indexes(self) -> None:
        """
        📊 Create database indexes for optimal performance
        """
        try:
            # Token indexes
            await self.database.tokens.create_index("address", unique=True)
            await self.database.tokens.create_index([("symbol", 1), ("is_active", 1)])
            await self.database.tokens.create_index([("created_at", -1)])
            
            # Signal indexes
            await self.database.signals.create_index([("token_address", 1), ("created_at", -1)])
            await self.database.signals.create_index([("signal_type", 1), ("strength", 1)])
            await self.database.signals.create_index([("expires_at", 1)])
            
            # Trade indexes
            await self.database.trades.create_index([("signal_id", 1)])
            await self.database.trades.create_index([("token_address", 1), ("executed_at", -1)])
            await self.database.trades.create_index([("status", 1), ("created_at", -1)])
            
            # Portfolio indexes
            await self.database.portfolios.create_index([("user_id", 1), ("status", 1)])
            await self.database.portfolios.create_index([("created_at", -1)])
            
            # Query result indexes
            await self.database.query_results.create_index([("timestamp", -1)])
            await self.database.query_results.create_index([("processed", 1)])
            
            # Performance metric indexes
            await self.database.performance_metrics.create_index([("portfolio_id", 1), ("calculated_at", -1)])
            
            # TTL indexes for cleanup
            await self.database.query_results.create_index(
                [("timestamp", 1)],
                expireAfterSeconds=30 * 24 * 3600  # 30 days
            )
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(
                "Failed to create database indexes",
                error=str(e)
            )
            # Don't raise here as indexes are not critical for startup

    async def _setup_read_replica(self) -> None:
        """
        🔄 Setup read replica connection for production scaling
        """
        try:
            # Check if read replica URI is configured
            read_replica_uri = getattr(self.settings, 'mongodb_read_replica_uri', None)

            if not read_replica_uri:
                logger.info("No read replica URI configured, using primary for reads")
                self.read_client = self.client
                self.read_database = self.database
                return

            logger.info("Setting up read replica connection")

            # Connection options optimized for read operations
            read_connection_options = {
                "maxPoolSize": 30 if self.settings.is_production else 15,
                "minPoolSize": 10 if self.settings.is_production else 5,
                "maxIdleTimeMS": 60000,  # Longer idle time for read connections
                "serverSelectionTimeoutMS": 3000,  # Faster timeout for reads
                "socketTimeoutMS": 30000,
                "connectTimeoutMS": 5000,
                "retryWrites": False,  # No writes on read replica
                "readPreference": "secondary",  # Prefer secondary reads
                "readConcern": {"level": "local"},  # Faster reads with local concern
                "heartbeatFrequencyMS": 10000,
                "maxStalenessSeconds": 90,  # Allow slightly stale reads
            }

            # Create read replica client
            self.read_client = AsyncIOMotorClient(
                read_replica_uri,
                **read_connection_options
            )

            # Test read replica connection
            await self.read_client.admin.command('ping')

            # Get read database
            self.read_database = self.read_client[self.settings.mongodb_db_name]

            self.read_replica_enabled = True
            logger.info(
                "Read replica connected successfully",
                read_connection_options=read_connection_options
            )

        except Exception as e:
            logger.warning(
                "Failed to setup read replica, falling back to primary",
                error=str(e)
            )
            # Fallback to primary for reads
            self.read_client = self.client
            self.read_database = self.database

    async def disconnect(self) -> None:
        """
        🔌 Close database connections
        """
        if self.client:
            self.client.close()

        if self.read_client and self.read_client != self.client:
            self.read_client.close()

        self.is_connected = False
        logger.info(
            "Database disconnected",
            read_replica_enabled=self.read_replica_enabled
        )
    
    async def health_check(self) -> dict:
        """
        🏥 Perform database health check
        """
        try:
            if not self.is_connected or not self.client:
                return {
                    "status": "unhealthy",
                    "error": "Not connected to database"
                }
            
            # Test basic connectivity
            await self.client.admin.command('ping')
            
            # Get server status
            server_status = await self.client.admin.command('serverStatus')
            
            # Get database stats
            db_stats = await self.database.command('dbStats')
            
            return {
                "status": "healthy",
                "server_version": server_status.get("version"),
                "uptime_seconds": server_status.get("uptime"),
                "connections": server_status.get("connections", {}),
                "database_size_mb": round(db_stats.get("dataSize", 0) / 1024 / 1024, 2),
                "collection_count": db_stats.get("collections", 0),
                "index_count": db_stats.get("indexes", 0)
            }
            
        except Exception as e:
            logger.error(
                "Database health check failed",
                error=str(e)
            )
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    def get_database(self) -> AsyncIOMotorDatabase:
        """
        🗄️ Get database instance
        """
        if not self.database:
            raise RuntimeError("Database not connected")
        return self.database
    
    def get_client(self) -> AsyncIOMotorClient:
        """
        🔌 Get client instance
        """
        if not self.client:
            raise RuntimeError("Database client not initialized")
        return self.client

    def get_read_database(self) -> AsyncIOMotorDatabase:
        """
        📖 Get read database instance (uses read replica if available)
        """
        if not self.read_database:
            raise RuntimeError("Read database not connected")
        return self.read_database

    def get_read_client(self) -> AsyncIOMotorClient:
        """
        📖 Get read client instance (uses read replica if available)
        """
        if not self.read_client:
            raise RuntimeError("Read database client not initialized")
        return self.read_client

    async def track_query_performance(self, query_type: str, execution_time: float, is_slow: bool = False) -> None:
        """
        📊 Track query performance metrics

        Args:
            query_type: Type of query (read/write)
            execution_time: Query execution time in seconds
            is_slow: Whether this was a slow query
        """
        self.query_stats["total_queries"] += 1

        if query_type == "read":
            self.query_stats["read_queries"] += 1
        elif query_type == "write":
            self.query_stats["write_queries"] += 1

        if is_slow:
            self.query_stats["slow_queries"] += 1

        # Track query times (keep last 1000 for rolling average)
        self.query_stats["query_times"].append(execution_time)
        if len(self.query_stats["query_times"]) > 1000:
            self.query_stats["query_times"] = self.query_stats["query_times"][-1000:]

        # Update average
        if self.query_stats["query_times"]:
            self.query_stats["avg_query_time"] = sum(self.query_stats["query_times"]) / len(self.query_stats["query_times"])

    def get_query_stats(self) -> Dict[str, Any]:
        """
        📈 Get query performance statistics
        """
        return {
            **self.query_stats,
            "read_replica_enabled": self.read_replica_enabled,
            "slow_query_percentage": (
                (self.query_stats["slow_queries"] / self.query_stats["total_queries"] * 100)
                if self.query_stats["total_queries"] > 0 else 0
            ),
            "read_write_ratio": (
                (self.query_stats["read_queries"] / self.query_stats["write_queries"])
                if self.query_stats["write_queries"] > 0 else float('inf')
            )
        }

    async def optimize_indexes(self) -> Dict[str, Any]:
        """
        🔧 Analyze and optimize database indexes
        """
        try:
            optimization_results = {
                "analyzed_collections": 0,
                "suggested_indexes": [],
                "unused_indexes": [],
                "index_usage_stats": {}
            }

            # Get all collections
            collections = await self.database.list_collection_names()

            for collection_name in collections:
                collection = self.database[collection_name]
                optimization_results["analyzed_collections"] += 1

                # Get index stats
                try:
                    index_stats = await collection.aggregate([
                        {"$indexStats": {}}
                    ]).to_list(None)

                    optimization_results["index_usage_stats"][collection_name] = index_stats

                    # Find unused indexes (no accesses in stats)
                    for stat in index_stats:
                        if stat.get("accesses", {}).get("ops", 0) == 0 and stat["name"] != "_id_":
                            optimization_results["unused_indexes"].append({
                                "collection": collection_name,
                                "index": stat["name"],
                                "spec": stat["spec"]
                            })

                except Exception as e:
                    logger.warning(f"Could not get index stats for {collection_name}: {str(e)}")

            logger.info(
                "Index optimization analysis completed",
                analyzed_collections=optimization_results["analyzed_collections"],
                unused_indexes_count=len(optimization_results["unused_indexes"])
            )

            return optimization_results

        except Exception as e:
            logger.error(f"Error during index optimization: {str(e)}")
            return {"error": str(e)}


# Global database instance
database_config = DatabaseConfig()


async def get_database() -> AsyncIOMotorDatabase:
    """
    🗄️ Get database instance (dependency injection)
    """
    return database_config.get_database()


async def connect_database() -> None:
    """
    🚀 Initialize database connection
    """
    await database_config.connect()


async def disconnect_database() -> None:
    """
    🔌 Close database connection
    """
    await database_config.disconnect()


async def database_health_check() -> dict:
    """
    🏥 Database health check endpoint
    """
    return await database_config.health_check()
