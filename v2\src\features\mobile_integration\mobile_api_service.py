"""
Mobile API Service

Provides mobile-optimized API responses, data compression,
and offline synchronization capabilities for mobile applications.
"""

import asyncio
import gzip
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Response

from src.shared.types import (
    DashboardData, PortfolioSummary, SignalData, TradeData
)
from src.features.data_pipeline.cache_manager import CacheManager
from src.features.web_interface.dashboard_service import DashboardService

logger = structlog.get_logger(__name__)


class MobileDataFormat(Enum):
    """Mobile data format options"""
    COMPACT = "compact"
    STANDARD = "standard"
    DETAILED = "detailed"


class SyncStatus(Enum):
    """Data synchronization status"""
    UP_TO_DATE = "up_to_date"
    NEEDS_UPDATE = "needs_update"
    SYNCING = "syncing"
    ERROR = "error"


@dataclass
class MobileConfig:
    """Mobile API configuration"""
    max_response_size: int = 1024 * 1024  # 1MB
    compression_threshold: int = 1024  # 1KB
    cache_ttl: int = 300  # 5 minutes
    offline_data_retention: int = 86400 * 7  # 7 days
    sync_batch_size: int = 100


@dataclass
class MobileResponse:
    """Mobile-optimized API response"""
    data: Any
    format: MobileDataFormat
    compressed: bool = False
    cache_key: Optional[str] = None
    last_modified: Optional[datetime] = None
    sync_status: SyncStatus = SyncStatus.UP_TO_DATE


@dataclass
class OfflineData:
    """Offline data package"""
    user_id: str
    data_type: str
    data: Dict[str, Any]
    created_at: datetime
    expires_at: datetime
    version: int = 1


class MobileAPIService:
    """
    Mobile-optimized API service providing data compression,
    offline synchronization, and mobile-specific optimizations.
    """
    
    def __init__(
        self,
        cache_manager: CacheManager,
        dashboard_service: DashboardService,
        config: Optional[MobileConfig] = None
    ):
        self.cache_manager = cache_manager
        self.dashboard_service = dashboard_service
        self.config = config or MobileConfig()
        self.logger = logger.bind(service="mobile_api")
    
    async def get_mobile_dashboard(
        self,
        user_id: str,
        format: MobileDataFormat = MobileDataFormat.COMPACT,
        if_modified_since: Optional[datetime] = None
    ) -> MobileResponse:
        """
        Get mobile-optimized dashboard data.
        
        Args:
            user_id: User identifier
            format: Data format (compact, standard, detailed)
            if_modified_since: Client's last update timestamp
            
        Returns:
            Mobile-optimized dashboard response
        """
        try:
            cache_key = f"mobile_dashboard:{user_id}:{format.value}"
            
            # Check if data has been modified
            last_modified = await self._get_last_modified(cache_key)
            if if_modified_since and last_modified and last_modified <= if_modified_since:
                return MobileResponse(
                    data=None,
                    format=format,
                    sync_status=SyncStatus.UP_TO_DATE,
                    last_modified=last_modified
                )
            
            # Get dashboard data
            dashboard_data = await self.dashboard_service.get_dashboard_data(user_id)
            
            # Optimize data based on format
            optimized_data = await self._optimize_dashboard_data(dashboard_data, format)
            
            # Create mobile response
            response = MobileResponse(
                data=optimized_data,
                format=format,
                cache_key=cache_key,
                last_modified=datetime.utcnow(),
                sync_status=SyncStatus.UP_TO_DATE
            )
            
            # Cache the response
            await self._cache_mobile_response(cache_key, response)
            
            self.logger.info(
                "Mobile dashboard data retrieved",
                user_id=user_id,
                format=format.value,
                data_size=len(json.dumps(optimized_data))
            )
            
            return response
            
        except Exception as e:
            self.logger.error("Failed to get mobile dashboard", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get mobile dashboard")
    
    async def get_mobile_portfolio(
        self,
        user_id: str,
        format: MobileDataFormat = MobileDataFormat.COMPACT
    ) -> MobileResponse:
        """
        Get mobile-optimized portfolio data.
        
        Args:
            user_id: User identifier
            format: Data format
            
        Returns:
            Mobile-optimized portfolio response
        """
        try:
            cache_key = f"mobile_portfolio:{user_id}:{format.value}"
            
            # Check cache first
            cached_response = await self._get_cached_mobile_response(cache_key)
            if cached_response:
                return cached_response
            
            # Get portfolio data
            dashboard_data = await self.dashboard_service.get_dashboard_data(user_id)
            portfolio = dashboard_data.portfolio
            
            # Optimize portfolio data
            optimized_portfolio = await self._optimize_portfolio_data(portfolio, format)
            
            response = MobileResponse(
                data=optimized_portfolio,
                format=format,
                cache_key=cache_key,
                last_modified=datetime.utcnow()
            )
            
            # Cache the response
            await self._cache_mobile_response(cache_key, response)
            
            return response
            
        except Exception as e:
            self.logger.error("Failed to get mobile portfolio", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get mobile portfolio")
    
    async def get_mobile_signals(
        self,
        user_id: str,
        limit: int = 20,
        format: MobileDataFormat = MobileDataFormat.COMPACT
    ) -> MobileResponse:
        """
        Get mobile-optimized signals data.
        
        Args:
            user_id: User identifier
            limit: Maximum number of signals
            format: Data format
            
        Returns:
            Mobile-optimized signals response
        """
        try:
            cache_key = f"mobile_signals:{user_id}:{limit}:{format.value}"
            
            # Check cache first
            cached_response = await self._get_cached_mobile_response(cache_key)
            if cached_response:
                return cached_response
            
            # Get signals data
            dashboard_data = await self.dashboard_service.get_dashboard_data(user_id)
            signals = dashboard_data.recent_signals[:limit]
            
            # Optimize signals data
            optimized_signals = await self._optimize_signals_data(signals, format)
            
            response = MobileResponse(
                data=optimized_signals,
                format=format,
                cache_key=cache_key,
                last_modified=datetime.utcnow()
            )
            
            # Cache the response
            await self._cache_mobile_response(cache_key, response)
            
            return response
            
        except Exception as e:
            self.logger.error("Failed to get mobile signals", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get mobile signals")
    
    async def prepare_offline_data(
        self,
        user_id: str,
        data_types: List[str]
    ) -> Dict[str, OfflineData]:
        """
        Prepare offline data packages for mobile app.
        
        Args:
            user_id: User identifier
            data_types: Types of data to prepare (dashboard, portfolio, signals, trades)
            
        Returns:
            Dictionary of offline data packages
        """
        try:
            offline_packages = {}
            
            for data_type in data_types:
                if data_type == "dashboard":
                    data = await self.get_mobile_dashboard(user_id, MobileDataFormat.COMPACT)
                elif data_type == "portfolio":
                    data = await self.get_mobile_portfolio(user_id, MobileDataFormat.COMPACT)
                elif data_type == "signals":
                    data = await self.get_mobile_signals(user_id, 50, MobileDataFormat.COMPACT)
                else:
                    continue
                
                # Create offline package
                offline_package = OfflineData(
                    user_id=user_id,
                    data_type=data_type,
                    data=data.data,
                    created_at=datetime.utcnow(),
                    expires_at=datetime.utcnow() + timedelta(seconds=self.config.offline_data_retention)
                )
                
                offline_packages[data_type] = offline_package
                
                # Store offline package
                await self._store_offline_package(offline_package)
            
            self.logger.info(
                "Offline data prepared",
                user_id=user_id,
                data_types=data_types,
                packages=len(offline_packages)
            )
            
            return offline_packages
            
        except Exception as e:
            self.logger.error("Failed to prepare offline data", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to prepare offline data")
    
    async def sync_mobile_data(
        self,
        user_id: str,
        client_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Synchronize mobile app data with server.
        
        Args:
            user_id: User identifier
            client_data: Client's current data state
            
        Returns:
            Synchronization result with updates
        """
        try:
            sync_result = {
                "status": SyncStatus.UP_TO_DATE.value,
                "updates": {},
                "conflicts": [],
                "timestamp": datetime.utcnow()
            }
            
            # Check each data type for updates
            for data_type, client_version in client_data.items():
                server_data = await self._get_latest_data(user_id, data_type)
                
                if server_data and server_data.get("version", 0) > client_version.get("version", 0):
                    sync_result["updates"][data_type] = server_data
                    sync_result["status"] = SyncStatus.NEEDS_UPDATE.value
            
            self.logger.info(
                "Mobile data synchronized",
                user_id=user_id,
                status=sync_result["status"],
                updates=len(sync_result["updates"])
            )
            
            return sync_result
            
        except Exception as e:
            self.logger.error("Failed to sync mobile data", user_id=user_id, error=str(e))
            return {
                "status": SyncStatus.ERROR.value,
                "error": str(e),
                "timestamp": datetime.utcnow()
            }
    
    async def compress_response(self, data: Any) -> bytes:
        """
        Compress response data for mobile transmission.
        
        Args:
            data: Data to compress
            
        Returns:
            Compressed data bytes
        """
        try:
            json_data = json.dumps(data, default=str)
            
            if len(json_data) > self.config.compression_threshold:
                compressed = gzip.compress(json_data.encode('utf-8'))
                self.logger.debug(
                    "Response compressed",
                    original_size=len(json_data),
                    compressed_size=len(compressed),
                    compression_ratio=len(compressed) / len(json_data)
                )
                return compressed
            else:
                return json_data.encode('utf-8')
                
        except Exception as e:
            self.logger.error("Failed to compress response", error=str(e))
            return json.dumps(data, default=str).encode('utf-8')
    
    async def _optimize_dashboard_data(
        self,
        dashboard_data: DashboardData,
        format: MobileDataFormat
    ) -> Dict[str, Any]:
        """Optimize dashboard data for mobile"""
        if format == MobileDataFormat.COMPACT:
            return {
                "portfolio": {
                    "value": dashboard_data.portfolio.total_value,
                    "pnl": dashboard_data.portfolio.total_pnl,
                    "pnl_pct": dashboard_data.portfolio.total_pnl_percentage,
                    "positions": dashboard_data.portfolio.active_positions
                },
                "signals": len(dashboard_data.recent_signals),
                "performance": {
                    "sharpe": dashboard_data.performance.sharpe_ratio,
                    "drawdown": dashboard_data.performance.max_drawdown,
                    "win_rate": dashboard_data.performance.win_rate
                },
                "updated": dashboard_data.last_updated
            }
        elif format == MobileDataFormat.STANDARD:
            return {
                "portfolio": dashboard_data.portfolio.dict(),
                "recent_signals": [
                    {
                        "id": signal.id,
                        "token": signal.token_symbol,
                        "type": signal.signal_type,
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "created_at": signal.created_at
                    }
                    for signal in dashboard_data.recent_signals[:10]
                ],
                "performance": dashboard_data.performance.dict(),
                "updated": dashboard_data.last_updated
            }
        else:  # DETAILED
            return dashboard_data.dict()
    
    async def _optimize_portfolio_data(
        self,
        portfolio: PortfolioSummary,
        format: MobileDataFormat
    ) -> Dict[str, Any]:
        """Optimize portfolio data for mobile"""
        if format == MobileDataFormat.COMPACT:
            return {
                "value": portfolio.total_value,
                "pnl": portfolio.total_pnl,
                "pnl_pct": portfolio.total_pnl_percentage,
                "positions": portfolio.active_positions,
                "balance": portfolio.available_balance
            }
        else:
            return portfolio.dict()
    
    async def _optimize_signals_data(
        self,
        signals: List[SignalData],
        format: MobileDataFormat
    ) -> List[Dict[str, Any]]:
        """Optimize signals data for mobile"""
        if format == MobileDataFormat.COMPACT:
            return [
                {
                    "id": signal.id,
                    "token": signal.token_symbol,
                    "type": signal.signal_type,
                    "strength": signal.strength,
                    "confidence": signal.confidence,
                    "price": signal.current_price,
                    "created": signal.created_at
                }
                for signal in signals
            ]
        elif format == MobileDataFormat.STANDARD:
            return [
                {
                    "id": signal.id,
                    "token_address": signal.token_address,
                    "token_symbol": signal.token_symbol,
                    "signal_type": signal.signal_type,
                    "strength": signal.strength,
                    "confidence": signal.confidence,
                    "current_price": signal.current_price,
                    "target_price": signal.target_price,
                    "stop_loss": signal.stop_loss,
                    "created_at": signal.created_at,
                    "expires_at": signal.expires_at
                }
                for signal in signals
            ]
        else:  # DETAILED
            return [signal.dict() for signal in signals]
    
    async def _cache_mobile_response(
        self,
        cache_key: str,
        response: MobileResponse
    ) -> None:
        """Cache mobile response"""
        try:
            cache_data = {
                "data": response.data,
                "format": response.format.value,
                "last_modified": response.last_modified.isoformat() if response.last_modified else None,
                "sync_status": response.sync_status.value
            }
            
            await self.cache_manager.set(cache_key, cache_data, ttl=self.config.cache_ttl)
            
            # Store last modified timestamp
            await self.cache_manager.set(
                f"{cache_key}:last_modified",
                response.last_modified.isoformat() if response.last_modified else None,
                ttl=self.config.cache_ttl
            )
            
        except Exception as e:
            self.logger.error("Failed to cache mobile response", cache_key=cache_key, error=str(e))
    
    async def _get_cached_mobile_response(
        self,
        cache_key: str
    ) -> Optional[MobileResponse]:
        """Get cached mobile response"""
        try:
            cache_data = await self.cache_manager.get(cache_key)
            if not cache_data:
                return None
            
            return MobileResponse(
                data=cache_data["data"],
                format=MobileDataFormat(cache_data["format"]),
                last_modified=datetime.fromisoformat(cache_data["last_modified"]) if cache_data["last_modified"] else None,
                sync_status=SyncStatus(cache_data["sync_status"])
            )
            
        except Exception as e:
            self.logger.error("Failed to get cached response", cache_key=cache_key, error=str(e))
            return None
    
    async def _get_last_modified(self, cache_key: str) -> Optional[datetime]:
        """Get last modified timestamp for cache key"""
        try:
            timestamp_str = await self.cache_manager.get(f"{cache_key}:last_modified")
            if timestamp_str:
                return datetime.fromisoformat(timestamp_str)
            return None
        except Exception:
            return None
    
    async def _store_offline_package(self, package: OfflineData) -> None:
        """Store offline data package"""
        try:
            cache_key = f"offline:{package.user_id}:{package.data_type}"
            await self.cache_manager.set(
                cache_key,
                package.__dict__,
                ttl=self.config.offline_data_retention
            )
        except Exception as e:
            self.logger.error("Failed to store offline package", error=str(e))
    
    async def _get_latest_data(self, user_id: str, data_type: str) -> Optional[Dict[str, Any]]:
        """Get latest data for synchronization"""
        try:
            cache_key = f"mobile_{data_type}:{user_id}:compact"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                return {
                    "data": cached_data["data"],
                    "version": 1,  # TODO: Implement proper versioning
                    "timestamp": cached_data.get("last_modified")
                }
            
            return None
            
        except Exception as e:
            self.logger.error("Failed to get latest data", user_id=user_id, data_type=data_type, error=str(e))
            return None
