"""
📝 Log Aggregator

Centralized logging system with log analysis, pattern detection, error correlation,
search & filter capabilities, and export integration.
"""

import asyncio
import re
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Pattern
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class LogLevel(str, Enum):
    """Log levels"""
    TRACE = "trace"
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class LogEntry:
    """Log entry data structure"""
    timestamp: datetime
    level: LogLevel
    module: str
    message: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    correlation_id: Optional[str] = None
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None


@dataclass
class LogPattern:
    """Log pattern for detection"""
    name: str
    pattern: Pattern
    level: LogLevel
    description: str
    action: Optional[str] = None
    count_threshold: int = 5
    time_window_minutes: int = 10


@dataclass
class LogAnalysis:
    """Log analysis results"""
    total_logs: int
    logs_by_level: Dict[LogLevel, int]
    error_patterns: List[Dict[str, Any]]
    frequent_errors: List[Dict[str, Any]]
    correlation_groups: List[Dict[str, Any]]
    time_range: Dict[str, datetime]
    analysis_timestamp: datetime = field(default_factory=datetime.utcnow)


class LogAggregator:
    """
    📝 Centralized log aggregation and analysis system
    """
    
    def __init__(self, max_logs: int = 10000):
        self.settings = settings
        self.is_running = False
        self.max_logs = max_logs
        
        # Log storage
        self.logs: deque = deque(maxlen=max_logs)
        self.error_logs: deque = deque(maxlen=1000)
        
        # Pattern detection
        self.log_patterns: List[LogPattern] = []
        self.pattern_matches: Dict[str, List[datetime]] = defaultdict(list)
        
        # Error correlation
        self.error_correlations: Dict[str, List[LogEntry]] = defaultdict(list)
        
        # Search indexes
        self.module_index: Dict[str, List[int]] = defaultdict(list)
        self.level_index: Dict[LogLevel, List[int]] = defaultdict(list)
        self.keyword_index: Dict[str, List[int]] = defaultdict(list)
        
        # Initialize default patterns
        self._init_default_patterns()
        
        logger.info("LogAggregator initialized")
    
    def _init_default_patterns(self):
        """Initialize default log patterns"""
        default_patterns = [
            LogPattern(
                name="database_connection_error",
                pattern=re.compile(r"database.*connection.*failed|mongodb.*error|connection.*timeout", re.IGNORECASE),
                level=LogLevel.ERROR,
                description="Database connection issues",
                action="alert_ops_team",
                count_threshold=3,
                time_window_minutes=5
            ),
            LogPattern(
                name="api_rate_limit",
                pattern=re.compile(r"rate.*limit.*exceeded|too.*many.*requests", re.IGNORECASE),
                level=LogLevel.WARNING,
                description="API rate limiting triggered",
                count_threshold=10,
                time_window_minutes=5
            ),
            LogPattern(
                name="authentication_failure",
                pattern=re.compile(r"authentication.*failed|unauthorized|invalid.*token", re.IGNORECASE),
                level=LogLevel.WARNING,
                description="Authentication failures",
                count_threshold=5,
                time_window_minutes=10
            ),
            LogPattern(
                name="external_api_error",
                pattern=re.compile(r"external.*api.*error|api.*request.*failed|timeout.*api", re.IGNORECASE),
                level=LogLevel.ERROR,
                description="External API failures",
                count_threshold=5,
                time_window_minutes=10
            ),
            LogPattern(
                name="memory_warning",
                pattern=re.compile(r"memory.*usage.*high|out.*of.*memory|memory.*leak", re.IGNORECASE),
                level=LogLevel.WARNING,
                description="Memory usage warnings",
                count_threshold=3,
                time_window_minutes=15
            ),
            LogPattern(
                name="trading_error",
                pattern=re.compile(r"trade.*failed|order.*rejected|insufficient.*balance", re.IGNORECASE),
                level=LogLevel.ERROR,
                description="Trading operation errors",
                count_threshold=3,
                time_window_minutes=10
            )
        ]
        
        self.log_patterns.extend(default_patterns)
        logger.info(f"Initialized {len(default_patterns)} default log patterns")
    
    async def start(self):
        """Start log aggregation"""
        if self.is_running:
            logger.warning("LogAggregator already running")
            return
        
        self.is_running = True
        logger.info("Starting LogAggregator")
        
        # Start background analysis tasks
        asyncio.create_task(self._analysis_loop())
        asyncio.create_task(self._pattern_detection_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self):
        """Stop log aggregation"""
        self.is_running = False
        logger.info("LogAggregator stopped")
    
    async def _analysis_loop(self):
        """Main log analysis loop"""
        while self.is_running:
            try:
                await self._analyze_recent_logs()
                await asyncio.sleep(300)  # Analyze every 5 minutes
                
            except Exception as e:
                logger.error(
                    "Error in log analysis loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(300)
    
    async def _pattern_detection_loop(self):
        """Pattern detection loop"""
        while self.is_running:
            try:
                await self._detect_patterns()
                await asyncio.sleep(60)  # Check patterns every minute
                
            except Exception as e:
                logger.error(
                    "Error in pattern detection loop",
                    error=str(e)
                )
                await asyncio.sleep(60)
    
    async def _cleanup_loop(self):
        """Cleanup old data"""
        while self.is_running:
            try:
                await self._cleanup_old_data()
                await asyncio.sleep(3600)  # Cleanup every hour
                
            except Exception as e:
                logger.error(
                    "Error in cleanup loop",
                    error=str(e)
                )
                await asyncio.sleep(3600)
    
    def add_log(self, log_entry: LogEntry):
        """Add log entry to aggregator"""
        try:
            # Add to main log storage
            log_index = len(self.logs)
            self.logs.append(log_entry)
            
            # Add to error logs if error level
            if log_entry.level in [LogLevel.ERROR, LogLevel.CRITICAL]:
                self.error_logs.append(log_entry)
            
            # Update indexes
            self._update_indexes(log_entry, log_index)
            
            # Check for immediate pattern matches
            self._check_immediate_patterns(log_entry)
            
        except Exception as e:
            logger.error(
                "Error adding log entry",
                error=str(e)
            )
    
    def _update_indexes(self, log_entry: LogEntry, index: int):
        """Update search indexes"""
        # Module index
        self.module_index[log_entry.module].append(index)
        
        # Level index
        self.level_index[log_entry.level].append(index)
        
        # Keyword index (simple word extraction)
        words = re.findall(r'\w+', log_entry.message.lower())
        for word in words:
            if len(word) > 3:  # Only index words longer than 3 characters
                self.keyword_index[word].append(index)
    
    def _check_immediate_patterns(self, log_entry: LogEntry):
        """Check log entry against patterns immediately"""
        for pattern in self.log_patterns:
            if pattern.pattern.search(log_entry.message):
                self.pattern_matches[pattern.name].append(log_entry.timestamp)
                
                # Check if threshold is exceeded
                recent_matches = [
                    ts for ts in self.pattern_matches[pattern.name]
                    if ts > datetime.utcnow() - timedelta(minutes=pattern.time_window_minutes)
                ]
                
                if len(recent_matches) >= pattern.count_threshold:
                    asyncio.create_task(self._handle_pattern_threshold(pattern, recent_matches))
    
    async def _handle_pattern_threshold(self, pattern: LogPattern, matches: List[datetime]):
        """Handle pattern threshold exceeded"""
        logger.warning(
            "Log pattern threshold exceeded",
            pattern_name=pattern.name,
            description=pattern.description,
            match_count=len(matches),
            threshold=pattern.count_threshold,
            time_window_minutes=pattern.time_window_minutes
        )
        
        # Execute pattern action if defined
        if pattern.action:
            await self._execute_pattern_action(pattern, matches)
    
    async def _execute_pattern_action(self, pattern: LogPattern, matches: List[datetime]):
        """Execute action for pattern match"""
        try:
            if pattern.action == "alert_ops_team":
                # This would integrate with alerting system
                logger.critical(
                    "Pattern action: Alert ops team",
                    pattern=pattern.name,
                    description=pattern.description
                )
            elif pattern.action == "auto_restart":
                # This would trigger auto-restart logic
                logger.critical(
                    "Pattern action: Auto restart triggered",
                    pattern=pattern.name
                )
            
        except Exception as e:
            logger.error(
                "Error executing pattern action",
                pattern=pattern.name,
                action=pattern.action,
                error=str(e)
            )
    
    async def _analyze_recent_logs(self):
        """Analyze recent logs for trends and correlations"""
        try:
            if not self.logs:
                return
            
            # Analyze last hour of logs
            cutoff = datetime.utcnow() - timedelta(hours=1)
            recent_logs = [log for log in self.logs if log.timestamp > cutoff]
            
            if not recent_logs:
                return
            
            # Error correlation analysis
            await self._correlate_errors(recent_logs)
            
            # Frequency analysis
            await self._analyze_log_frequency(recent_logs)
            
        except Exception as e:
            logger.error(
                "Error analyzing recent logs",
                error=str(e)
            )
    
    async def _correlate_errors(self, logs: List[LogEntry]):
        """Correlate related errors"""
        error_logs = [log for log in logs if log.level in [LogLevel.ERROR, LogLevel.CRITICAL]]
        
        # Group by correlation_id
        correlation_groups = defaultdict(list)
        for log in error_logs:
            if log.correlation_id:
                correlation_groups[log.correlation_id].append(log)
        
        # Group by request_id
        request_groups = defaultdict(list)
        for log in error_logs:
            if log.request_id:
                request_groups[log.request_id].append(log)
        
        # Store correlations
        self.error_correlations.clear()
        self.error_correlations.update(correlation_groups)
        self.error_correlations.update(request_groups)
    
    async def _analyze_log_frequency(self, logs: List[LogEntry]):
        """Analyze log frequency patterns"""
        # Count logs by level
        level_counts = defaultdict(int)
        for log in logs:
            level_counts[log.level] += 1
        
        # Check for unusual patterns
        total_logs = len(logs)
        if total_logs > 0:
            error_ratio = (level_counts[LogLevel.ERROR] + level_counts[LogLevel.CRITICAL]) / total_logs
            if error_ratio > 0.1:  # More than 10% errors
                logger.warning(
                    "High error rate detected",
                    error_ratio=f"{error_ratio:.2%}",
                    total_logs=total_logs,
                    error_count=level_counts[LogLevel.ERROR] + level_counts[LogLevel.CRITICAL]
                )
    
    async def _detect_patterns(self):
        """Detect patterns in recent logs"""
        try:
            # Clean old pattern matches
            cutoff = datetime.utcnow() - timedelta(hours=1)
            for pattern_name in self.pattern_matches:
                self.pattern_matches[pattern_name] = [
                    ts for ts in self.pattern_matches[pattern_name] if ts > cutoff
                ]
            
        except Exception as e:
            logger.error(
                "Error detecting patterns",
                error=str(e)
            )

    async def _cleanup_old_data(self):
        """Clean up old log data"""
        try:
            # Clean pattern matches older than 24 hours
            cutoff = datetime.utcnow() - timedelta(hours=24)
            for pattern_name in list(self.pattern_matches.keys()):
                self.pattern_matches[pattern_name] = [
                    ts for ts in self.pattern_matches[pattern_name] if ts > cutoff
                ]

                # Remove empty pattern match lists
                if not self.pattern_matches[pattern_name]:
                    del self.pattern_matches[pattern_name]

            # Clean error correlations older than 24 hours
            for correlation_id in list(self.error_correlations.keys()):
                self.error_correlations[correlation_id] = [
                    log for log in self.error_correlations[correlation_id]
                    if log.timestamp > cutoff
                ]

                if not self.error_correlations[correlation_id]:
                    del self.error_correlations[correlation_id]

            # Clean indexes (rebuild from current logs)
            self._rebuild_indexes()

        except Exception as e:
            logger.error(
                "Error cleaning up log data",
                error=str(e)
            )

    def _rebuild_indexes(self):
        """Rebuild search indexes from current logs"""
        self.module_index.clear()
        self.level_index.clear()
        self.keyword_index.clear()

        for index, log_entry in enumerate(self.logs):
            self._update_indexes(log_entry, index)

    # Public API methods
    async def search_logs(
        self,
        query: Optional[str] = None,
        level: Optional[LogLevel] = None,
        module: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100
    ) -> List[LogEntry]:
        """Search logs with filters"""
        try:
            results = list(self.logs)

            # Filter by time range
            if start_time:
                results = [log for log in results if log.timestamp >= start_time]
            if end_time:
                results = [log for log in results if log.timestamp <= end_time]

            # Filter by level
            if level:
                results = [log for log in results if log.level == level]

            # Filter by module
            if module:
                results = [log for log in results if log.module == module]

            # Filter by query (simple text search)
            if query:
                query_lower = query.lower()
                results = [
                    log for log in results
                    if query_lower in log.message.lower() or
                       query_lower in log.module.lower()
                ]

            # Sort by timestamp (newest first) and limit
            results.sort(key=lambda x: x.timestamp, reverse=True)
            return results[:limit]

        except Exception as e:
            logger.error(
                "Error searching logs",
                error=str(e)
            )
            return []

    async def get_log_analysis(self, hours: int = 24) -> LogAnalysis:
        """Get comprehensive log analysis"""
        try:
            cutoff = datetime.utcnow() - timedelta(hours=hours)
            recent_logs = [log for log in self.logs if log.timestamp > cutoff]

            if not recent_logs:
                return LogAnalysis(
                    total_logs=0,
                    logs_by_level={},
                    error_patterns=[],
                    frequent_errors=[],
                    correlation_groups=[],
                    time_range={}
                )

            # Count logs by level
            logs_by_level = defaultdict(int)
            for log in recent_logs:
                logs_by_level[log.level] += 1

            # Analyze error patterns
            error_patterns = await self._analyze_error_patterns(recent_logs)

            # Find frequent errors
            frequent_errors = await self._find_frequent_errors(recent_logs)

            # Get correlation groups
            correlation_groups = [
                {
                    'correlation_id': corr_id,
                    'log_count': len(logs),
                    'time_span': (max(log.timestamp for log in logs) - min(log.timestamp for log in logs)).total_seconds(),
                    'modules': list(set(log.module for log in logs))
                }
                for corr_id, logs in self.error_correlations.items()
                if logs and any(log.timestamp > cutoff for log in logs)
            ]

            return LogAnalysis(
                total_logs=len(recent_logs),
                logs_by_level=dict(logs_by_level),
                error_patterns=error_patterns,
                frequent_errors=frequent_errors,
                correlation_groups=correlation_groups,
                time_range={
                    'start': min(log.timestamp for log in recent_logs),
                    'end': max(log.timestamp for log in recent_logs)
                }
            )

        except Exception as e:
            logger.error(
                "Error generating log analysis",
                error=str(e)
            )
            return LogAnalysis(
                total_logs=0,
                logs_by_level={},
                error_patterns=[],
                frequent_errors=[],
                correlation_groups=[],
                time_range={}
            )

    async def _analyze_error_patterns(self, logs: List[LogEntry]) -> List[Dict[str, Any]]:
        """Analyze error patterns in logs"""
        error_logs = [log for log in logs if log.level in [LogLevel.ERROR, LogLevel.CRITICAL]]

        # Group errors by message similarity
        error_groups = defaultdict(list)
        for log in error_logs:
            # Simple grouping by first 50 characters of message
            key = log.message[:50].lower()
            error_groups[key].append(log)

        # Return patterns with multiple occurrences
        patterns = []
        for pattern, group_logs in error_groups.items():
            if len(group_logs) > 1:
                patterns.append({
                    'pattern': pattern,
                    'count': len(group_logs),
                    'modules': list(set(log.module for log in group_logs)),
                    'first_occurrence': min(log.timestamp for log in group_logs),
                    'last_occurrence': max(log.timestamp for log in group_logs)
                })

        return sorted(patterns, key=lambda x: x['count'], reverse=True)[:10]

    async def _find_frequent_errors(self, logs: List[LogEntry]) -> List[Dict[str, Any]]:
        """Find most frequent error messages"""
        error_logs = [log for log in logs if log.level in [LogLevel.ERROR, LogLevel.CRITICAL]]

        # Count error messages
        error_counts = defaultdict(int)
        error_details = {}

        for log in error_logs:
            error_counts[log.message] += 1
            if log.message not in error_details:
                error_details[log.message] = {
                    'module': log.module,
                    'error_type': log.error_type,
                    'first_seen': log.timestamp
                }

        # Return top frequent errors
        frequent = []
        for message, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            details = error_details[message]
            frequent.append({
                'message': message,
                'count': count,
                'module': details['module'],
                'error_type': details['error_type'],
                'first_seen': details['first_seen']
            })

        return frequent

    async def get_error_correlations(self, correlation_id: str) -> List[LogEntry]:
        """Get logs for specific correlation ID"""
        return self.error_correlations.get(correlation_id, [])

    async def get_pattern_matches(self, pattern_name: str) -> List[datetime]:
        """Get pattern match timestamps"""
        return self.pattern_matches.get(pattern_name, [])

    async def add_log_pattern(self, pattern: LogPattern):
        """Add new log pattern"""
        self.log_patterns.append(pattern)
        logger.info(
            "Log pattern added",
            pattern_name=pattern.name,
            description=pattern.description
        )

    async def remove_log_pattern(self, pattern_name: str):
        """Remove log pattern"""
        self.log_patterns = [p for p in self.log_patterns if p.name != pattern_name]
        if pattern_name in self.pattern_matches:
            del self.pattern_matches[pattern_name]

        logger.info(
            "Log pattern removed",
            pattern_name=pattern_name
        )

    async def export_logs(
        self,
        format_type: str = "json",
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> str:
        """Export logs in specified format"""
        try:
            # Get logs in time range
            logs = await self.search_logs(
                start_time=start_time,
                end_time=end_time,
                limit=10000
            )

            if format_type == "json":
                return json.dumps([
                    {
                        'timestamp': log.timestamp.isoformat(),
                        'level': log.level.value,
                        'module': log.module,
                        'message': log.message,
                        'metadata': log.metadata,
                        'correlation_id': log.correlation_id,
                        'request_id': log.request_id,
                        'user_id': log.user_id,
                        'error_type': log.error_type
                    }
                    for log in logs
                ], indent=2)

            elif format_type == "csv":
                import csv
                from io import StringIO

                output = StringIO()
                writer = csv.writer(output)

                # Write header
                writer.writerow([
                    'timestamp', 'level', 'module', 'message',
                    'correlation_id', 'request_id', 'user_id', 'error_type'
                ])

                # Write data
                for log in logs:
                    writer.writerow([
                        log.timestamp.isoformat(),
                        log.level.value,
                        log.module,
                        log.message,
                        log.correlation_id or '',
                        log.request_id or '',
                        log.user_id or '',
                        log.error_type or ''
                    ])

                return output.getvalue()

            else:
                raise ValueError(f"Unsupported export format: {format_type}")

        except Exception as e:
            logger.error(
                "Error exporting logs",
                format_type=format_type,
                error=str(e)
            )
            return ""

    async def get_log_statistics(self) -> Dict[str, Any]:
        """Get log statistics"""
        try:
            total_logs = len(self.logs)
            if total_logs == 0:
                return {'total_logs': 0}

            # Count by level
            level_counts = defaultdict(int)
            module_counts = defaultdict(int)

            for log in self.logs:
                level_counts[log.level] += 1
                module_counts[log.module] += 1

            # Recent activity (last hour)
            recent_cutoff = datetime.utcnow() - timedelta(hours=1)
            recent_logs = [log for log in self.logs if log.timestamp > recent_cutoff]

            return {
                'total_logs': total_logs,
                'logs_by_level': dict(level_counts),
                'logs_by_module': dict(sorted(module_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
                'recent_activity': {
                    'last_hour': len(recent_logs),
                    'rate_per_minute': len(recent_logs) / 60 if recent_logs else 0
                },
                'error_statistics': {
                    'total_errors': len(self.error_logs),
                    'unique_correlations': len(self.error_correlations),
                    'active_patterns': len([p for p in self.pattern_matches if self.pattern_matches[p]])
                },
                'storage_info': {
                    'current_size': total_logs,
                    'max_size': self.max_logs,
                    'utilization_percent': (total_logs / self.max_logs) * 100
                }
            }

        except Exception as e:
            logger.error(
                "Error getting log statistics",
                error=str(e)
            )
            return {'error': str(e)}
