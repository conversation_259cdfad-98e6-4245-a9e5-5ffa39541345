"""
⚖️ Load Balancer - Phase 3 API Optimization

Advanced load balancing system for distributing API requests across multiple
backends with health monitoring and intelligent routing.
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import random
import statistics

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)


class LoadBalancingStrategy(Enum):
    """Load balancing strategies"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    LEAST_RESPONSE_TIME = "least_response_time"
    HEALTH_WEIGHTED = "health_weighted"
    ADAPTIVE = "adaptive"


class BackendStatus(Enum):
    """Backend server status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    MAINTENANCE = "maintenance"


@dataclass
class BackendServer:
    """Backend server configuration and metrics"""
    id: str
    host: str
    port: int
    weight: float = 1.0
    max_connections: int = 100
    timeout: float = 30.0
    
    # Runtime metrics
    current_connections: int = 0
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    last_health_check: Optional[datetime] = None
    status: BackendStatus = BackendStatus.HEALTHY
    
    # Health metrics
    response_times: List[float] = field(default_factory=list)
    error_rate: float = 0.0
    health_score: float = 100.0
    
    def update_metrics(self, response_time: float, success: bool) -> None:
        """Update server metrics after request"""
        self.total_requests += 1
        
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        # Update response times (keep last 100)
        self.response_times.append(response_time)
        if len(self.response_times) > 100:
            self.response_times = self.response_times[-100:]
        
        # Calculate average response time
        if self.response_times:
            self.avg_response_time = statistics.mean(self.response_times)
        
        # Calculate error rate
        if self.total_requests > 0:
            self.error_rate = (self.failed_requests / self.total_requests) * 100
        
        # Update health score
        self._calculate_health_score()
    
    def _calculate_health_score(self) -> None:
        """Calculate overall health score (0-100)"""
        try:
            # Base score
            score = 100.0
            
            # Penalize high error rate
            if self.error_rate > 0:
                score -= min(self.error_rate * 2, 50)  # Max 50 point penalty
            
            # Penalize slow response times
            if self.avg_response_time > 1.0:  # 1 second threshold
                penalty = min((self.avg_response_time - 1.0) * 10, 30)  # Max 30 point penalty
                score -= penalty
            
            # Penalize high connection usage
            if self.max_connections > 0:
                connection_usage = (self.current_connections / self.max_connections) * 100
                if connection_usage > 80:
                    score -= (connection_usage - 80) * 0.5  # Penalty for high usage
            
            self.health_score = max(0.0, score)
            
        except Exception as e:
            logger.error(f"Error calculating health score for {self.id}: {str(e)}")
            self.health_score = 50.0  # Default to medium health on error
    
    def is_available(self) -> bool:
        """Check if server is available for requests"""
        return (
            self.status in [BackendStatus.HEALTHY, BackendStatus.DEGRADED] and
            self.current_connections < self.max_connections
        )
    
    def get_load_factor(self) -> float:
        """Get current load factor (0.0 = no load, 1.0 = full load)"""
        if self.max_connections == 0:
            return 0.0
        return self.current_connections / self.max_connections


class LoadBalancer:
    """
    ⚖️ Advanced Load Balancer
    
    Features:
    - Multiple load balancing strategies
    - Health monitoring and automatic failover
    - Adaptive routing based on performance
    - Connection pooling and rate limiting
    - Real-time metrics and monitoring
    """
    
    def __init__(self, strategy: LoadBalancingStrategy = LoadBalancingStrategy.ADAPTIVE):
        self.settings = get_settings()
        self.strategy = strategy
        
        # Backend servers
        self.backends: Dict[str, BackendServer] = {}
        self.backend_order: List[str] = []  # For round-robin
        self.current_backend_index = 0
        
        # Health monitoring
        self.health_check_interval = 30.0  # seconds
        self.health_check_timeout = 5.0
        self.health_check_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time": 0.0,
            "backend_switches": 0,
            "health_checks": 0,
            "failovers": 0
        }
        
        # Circuit breaker settings
        self.circuit_breaker_threshold = 50.0  # Error rate threshold
        self.circuit_breaker_window = 60.0  # Time window in seconds
        
        self.running = False
        
        logger.info(f"Load balancer initialized with strategy: {strategy.value}")
    
    async def start(self) -> None:
        """Start the load balancer"""
        if self.running:
            return
        
        self.running = True
        
        # Start health monitoring
        self.health_check_task = asyncio.create_task(self._health_monitor())
        
        logger.info("Load balancer started")
    
    async def stop(self) -> None:
        """Stop the load balancer"""
        if not self.running:
            return
        
        self.running = False
        
        # Stop health monitoring
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Load balancer stopped")
    
    def add_backend(
        self,
        backend_id: str,
        host: str,
        port: int,
        weight: float = 1.0,
        max_connections: int = 100,
        timeout: float = 30.0
    ) -> None:
        """
        ➕ Add backend server
        
        Args:
            backend_id: Unique backend identifier
            host: Backend host
            port: Backend port
            weight: Backend weight for weighted strategies
            max_connections: Maximum concurrent connections
            timeout: Request timeout
        """
        backend = BackendServer(
            id=backend_id,
            host=host,
            port=port,
            weight=weight,
            max_connections=max_connections,
            timeout=timeout
        )
        
        self.backends[backend_id] = backend
        self.backend_order.append(backend_id)
        
        logger.info(
            "Backend added",
            backend_id=backend_id,
            host=host,
            port=port,
            weight=weight
        )
    
    def remove_backend(self, backend_id: str) -> bool:
        """
        ➖ Remove backend server
        
        Args:
            backend_id: Backend identifier to remove
            
        Returns:
            True if backend was removed
        """
        if backend_id in self.backends:
            del self.backends[backend_id]
            if backend_id in self.backend_order:
                self.backend_order.remove(backend_id)
            
            logger.info("Backend removed", backend_id=backend_id)
            return True
        
        return False
    
    async def route_request(self, request_data: Dict[str, Any]) -> Optional[BackendServer]:
        """
        🎯 Route request to optimal backend
        
        Args:
            request_data: Request information for routing decisions
            
        Returns:
            Selected backend server or None if no backends available
        """
        try:
            # Get available backends
            available_backends = [
                backend for backend in self.backends.values()
                if backend.is_available()
            ]
            
            if not available_backends:
                logger.warning("No available backends for request routing")
                return None
            
            # Select backend based on strategy
            selected_backend = await self._select_backend(available_backends, request_data)
            
            if selected_backend:
                # Update connection count
                selected_backend.current_connections += 1
                
                logger.debug(
                    "Request routed to backend",
                    backend_id=selected_backend.id,
                    strategy=self.strategy.value,
                    current_connections=selected_backend.current_connections
                )
            
            return selected_backend
            
        except Exception as e:
            logger.error(f"Error routing request: {str(e)}")
            return None
    
    async def complete_request(
        self,
        backend: BackendServer,
        response_time: float,
        success: bool
    ) -> None:
        """
        ✅ Mark request as completed and update metrics
        
        Args:
            backend: Backend that handled the request
            response_time: Request response time in seconds
            success: Whether request was successful
        """
        try:
            # Update backend metrics
            backend.current_connections = max(0, backend.current_connections - 1)
            backend.update_metrics(response_time, success)
            
            # Update global stats
            self.stats["total_requests"] += 1
            if success:
                self.stats["successful_requests"] += 1
            else:
                self.stats["failed_requests"] += 1
            
            # Update average response time
            total_requests = self.stats["total_requests"]
            self.stats["avg_response_time"] = (
                (self.stats["avg_response_time"] * (total_requests - 1) + response_time) / total_requests
            )
            
            # Check for circuit breaker conditions
            await self._check_circuit_breaker(backend)
            
        except Exception as e:
            logger.error(f"Error completing request: {str(e)}")
    
    async def get_load_balancer_stats(self) -> Dict[str, Any]:
        """
        📊 Get comprehensive load balancer statistics
        """
        try:
            backend_stats = {}
            for backend_id, backend in self.backends.items():
                backend_stats[backend_id] = {
                    "status": backend.status.value,
                    "health_score": backend.health_score,
                    "current_connections": backend.current_connections,
                    "total_requests": backend.total_requests,
                    "success_rate": (
                        (backend.successful_requests / backend.total_requests * 100)
                        if backend.total_requests > 0 else 0
                    ),
                    "avg_response_time": backend.avg_response_time,
                    "load_factor": backend.get_load_factor(),
                    "last_health_check": (
                        backend.last_health_check.isoformat()
                        if backend.last_health_check else None
                    )
                }
            
            return {
                "strategy": self.strategy.value,
                "global_stats": self.stats,
                "backend_stats": backend_stats,
                "available_backends": len([b for b in self.backends.values() if b.is_available()]),
                "total_backends": len(self.backends),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting load balancer stats: {str(e)}")
            return {"error": str(e)}

    # Private methods

    async def _select_backend(
        self,
        available_backends: List[BackendServer],
        request_data: Dict[str, Any]
    ) -> Optional[BackendServer]:
        """Select backend based on configured strategy"""
        try:
            if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
                return self._round_robin_selection(available_backends)

            elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
                return self._weighted_round_robin_selection(available_backends)

            elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
                return self._least_connections_selection(available_backends)

            elif self.strategy == LoadBalancingStrategy.LEAST_RESPONSE_TIME:
                return self._least_response_time_selection(available_backends)

            elif self.strategy == LoadBalancingStrategy.HEALTH_WEIGHTED:
                return self._health_weighted_selection(available_backends)

            elif self.strategy == LoadBalancingStrategy.ADAPTIVE:
                return self._adaptive_selection(available_backends, request_data)

            else:
                # Default to round robin
                return self._round_robin_selection(available_backends)

        except Exception as e:
            logger.error(f"Error selecting backend: {str(e)}")
            return available_backends[0] if available_backends else None

    def _round_robin_selection(self, backends: List[BackendServer]) -> BackendServer:
        """Round robin backend selection"""
        if not backends:
            return None

        # Find next backend in order
        available_ids = [b.id for b in backends]

        # Start from current index and find next available
        for _ in range(len(self.backend_order)):
            backend_id = self.backend_order[self.current_backend_index]
            self.current_backend_index = (self.current_backend_index + 1) % len(self.backend_order)

            if backend_id in available_ids:
                return next(b for b in backends if b.id == backend_id)

        # Fallback to first available
        return backends[0]

    def _weighted_round_robin_selection(self, backends: List[BackendServer]) -> BackendServer:
        """Weighted round robin backend selection"""
        if not backends:
            return None

        # Calculate total weight
        total_weight = sum(b.weight for b in backends)
        if total_weight == 0:
            return backends[0]

        # Generate random number and select based on weight
        rand = random.uniform(0, total_weight)
        current_weight = 0

        for backend in backends:
            current_weight += backend.weight
            if rand <= current_weight:
                return backend

        return backends[-1]  # Fallback

    def _least_connections_selection(self, backends: List[BackendServer]) -> BackendServer:
        """Least connections backend selection"""
        if not backends:
            return None

        return min(backends, key=lambda b: b.current_connections)

    def _least_response_time_selection(self, backends: List[BackendServer]) -> BackendServer:
        """Least response time backend selection"""
        if not backends:
            return None

        return min(backends, key=lambda b: b.avg_response_time)

    def _health_weighted_selection(self, backends: List[BackendServer]) -> BackendServer:
        """Health-weighted backend selection"""
        if not backends:
            return None

        # Calculate total health score
        total_health = sum(b.health_score for b in backends)
        if total_health == 0:
            return backends[0]

        # Generate random number and select based on health score
        rand = random.uniform(0, total_health)
        current_health = 0

        for backend in backends:
            current_health += backend.health_score
            if rand <= current_health:
                return backend

        return backends[-1]  # Fallback

    def _adaptive_selection(
        self,
        backends: List[BackendServer],
        request_data: Dict[str, Any]
    ) -> BackendServer:
        """Adaptive backend selection based on multiple factors"""
        if not backends:
            return None

        # Score each backend based on multiple factors
        backend_scores = []

        for backend in backends:
            score = 0.0

            # Health score factor (40% weight)
            score += (backend.health_score / 100.0) * 0.4

            # Connection load factor (30% weight)
            load_factor = backend.get_load_factor()
            score += (1.0 - load_factor) * 0.3

            # Response time factor (20% weight)
            if backend.avg_response_time > 0:
                # Normalize response time (assume 1 second is baseline)
                response_factor = min(1.0, 1.0 / backend.avg_response_time)
                score += response_factor * 0.2
            else:
                score += 0.2  # No response time data, give full points

            # Weight factor (10% weight)
            max_weight = max(b.weight for b in backends) if backends else 1.0
            weight_factor = backend.weight / max_weight if max_weight > 0 else 1.0
            score += weight_factor * 0.1

            backend_scores.append((backend, score))

        # Select backend with highest score
        best_backend = max(backend_scores, key=lambda x: x[1])[0]
        return best_backend

    async def _health_monitor(self) -> None:
        """Background health monitoring task"""
        while self.running:
            try:
                await asyncio.sleep(self.health_check_interval)

                # Check health of all backends
                health_tasks = [
                    self._check_backend_health(backend)
                    for backend in self.backends.values()
                ]

                if health_tasks:
                    await asyncio.gather(*health_tasks, return_exceptions=True)

                self.stats["health_checks"] += 1

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitor: {str(e)}")

    async def _check_backend_health(self, backend: BackendServer) -> None:
        """Check health of a specific backend"""
        try:
            start_time = time.time()

            # Simulate health check (would be actual HTTP request in real implementation)
            await asyncio.sleep(0.01)  # Simulate network delay

            response_time = time.time() - start_time

            # Update health check timestamp
            backend.last_health_check = datetime.utcnow()

            # Determine health status based on metrics
            if backend.error_rate > 25.0 or backend.avg_response_time > 5.0:
                backend.status = BackendStatus.UNHEALTHY
            elif backend.error_rate > 10.0 or backend.avg_response_time > 2.0:
                backend.status = BackendStatus.DEGRADED
            else:
                backend.status = BackendStatus.HEALTHY

            logger.debug(
                "Backend health check completed",
                backend_id=backend.id,
                status=backend.status.value,
                health_score=backend.health_score,
                response_time=f"{response_time:.3f}s"
            )

        except Exception as e:
            logger.error(f"Health check failed for backend {backend.id}: {str(e)}")
            backend.status = BackendStatus.UNHEALTHY
            backend.last_health_check = datetime.utcnow()

    async def _check_circuit_breaker(self, backend: BackendServer) -> None:
        """Check circuit breaker conditions for backend"""
        try:
            # Check if error rate exceeds threshold
            if backend.error_rate > self.circuit_breaker_threshold:
                if backend.status != BackendStatus.UNHEALTHY:
                    logger.warning(
                        "Circuit breaker triggered for backend",
                        backend_id=backend.id,
                        error_rate=backend.error_rate,
                        threshold=self.circuit_breaker_threshold
                    )
                    backend.status = BackendStatus.UNHEALTHY
                    self.stats["failovers"] += 1

        except Exception as e:
            logger.error(f"Error checking circuit breaker: {str(e)}")
