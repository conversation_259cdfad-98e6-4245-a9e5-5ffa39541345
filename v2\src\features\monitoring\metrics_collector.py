"""
📊 Metrics Collector

Comprehensive metrics collection system for business metrics, performance metrics,
error metrics, and resource metrics with Prometheus integration.
"""

import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST

from ...config.logging_config import get_logger
from ...config.settings import get_settings
from ...shared.constants import MONITORING_CONSTANTS

logger = get_logger(__name__)
settings = get_settings()


class MetricsCollector:
    """
    📊 Comprehensive metrics collection system
    """
    
    def __init__(self):
        self.settings = settings
        self.is_running = False
        self.start_time = time.time()
        
        # Initialize Prometheus metrics
        self._init_prometheus_metrics()
        
        # Internal metrics storage
        self.business_metrics = {}
        self.performance_metrics = {}
        self.error_metrics = {}
        self.resource_metrics = {}
        
        logger.info("MetricsCollector initialized")
    
    def _init_prometheus_metrics(self):
        """Initialize Prometheus metrics"""
        # Business metrics
        self.signal_accuracy = Gauge('signal_accuracy_ratio', 'Signal accuracy ratio')
        self.trade_success_rate = Gauge('trade_success_rate', 'Trade success rate')
        self.portfolio_performance = Gauge('portfolio_performance_percent', 'Portfolio performance percentage')
        self.total_trades = Counter('total_trades_count', 'Total number of trades')
        self.successful_trades = Counter('successful_trades_count', 'Number of successful trades')
        
        # Performance metrics
        self.request_duration = Histogram('request_duration_seconds', 'Request duration', ['method', 'endpoint'])
        self.database_query_duration = Histogram('database_query_duration_seconds', 'Database query duration', ['operation'])
        self.api_response_time = Histogram('api_response_time_seconds', 'External API response time', ['api_name'])
        
        # Error metrics
        self.error_count = Counter('errors_total', 'Total number of errors', ['error_type', 'module'])
        self.failed_requests = Counter('failed_requests_total', 'Failed requests', ['status_code'])
        self.exception_count = Counter('exceptions_total', 'Total exceptions', ['exception_type'])
        
        # Resource metrics
        self.cpu_usage = Gauge('cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage = Gauge('memory_usage_bytes', 'Memory usage in bytes')
        self.memory_usage_percent = Gauge('memory_usage_percent', 'Memory usage percentage')
        self.disk_usage = Gauge('disk_usage_percent', 'Disk usage percentage')
        self.active_connections = Gauge('active_connections_count', 'Number of active connections')
        
        # Custom metrics
        self.token_analysis_speed = Histogram('token_analysis_duration_seconds', 'Token analysis duration')
        self.notification_delivery_rate = Gauge('notification_delivery_rate', 'Notification delivery success rate')
        self.cache_hit_rate = Gauge('cache_hit_rate', 'Cache hit rate percentage')
        
        logger.info("Prometheus metrics initialized")
    
    async def start(self):
        """Start metrics collection"""
        if self.is_running:
            logger.warning("MetricsCollector already running")
            return
        
        self.is_running = True
        logger.info("Starting MetricsCollector")
        
        # Start background collection task
        asyncio.create_task(self._collection_loop())
    
    async def stop(self):
        """Stop metrics collection"""
        self.is_running = False
        logger.info("MetricsCollector stopped")
    
    async def _collection_loop(self):
        """Main metrics collection loop"""
        while self.is_running:
            try:
                await self._collect_system_metrics()
                await self._collect_business_metrics()
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                logger.error(
                    "Error in metrics collection loop",
                    error=str(e),
                    exc_info=True
                )
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _collect_system_metrics(self):
        """Collect system resource metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.set(cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.memory_usage.set(memory.used)
            self.memory_usage_percent.set(memory.percent)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.disk_usage.set(disk_percent)
            
            # Network connections
            connections = len(psutil.net_connections())
            self.active_connections.set(connections)
            
            # Store in internal metrics
            self.resource_metrics.update({
                'cpu_usage_percent': cpu_percent,
                'memory_usage_bytes': memory.used,
                'memory_usage_percent': memory.percent,
                'disk_usage_percent': disk_percent,
                'active_connections': connections,
                'timestamp': datetime.utcnow()
            })
            
        except Exception as e:
            logger.error(
                "Error collecting system metrics",
                error=str(e)
            )
    
    async def _collect_business_metrics(self):
        """Collect business-specific metrics"""
        try:
            # This would integrate with actual business logic
            # For now, we'll set placeholder values
            
            # Signal accuracy (would come from signal processing module)
            signal_accuracy = 0.85  # Placeholder
            self.signal_accuracy.set(signal_accuracy)
            
            # Trade success rate (would come from trading module)
            trade_success = 0.72  # Placeholder
            self.trade_success_rate.set(trade_success)
            
            # Portfolio performance (would come from portfolio module)
            portfolio_perf = 15.5  # Placeholder
            self.portfolio_performance.set(portfolio_perf)
            
            # Cache hit rate (would come from cache manager)
            cache_hit = 0.89  # Placeholder
            self.cache_hit_rate.set(cache_hit)
            
            # Notification delivery rate (would come from notification module)
            notification_rate = 0.98  # Placeholder
            self.notification_delivery_rate.set(notification_rate)
            
            # Store in internal metrics
            self.business_metrics.update({
                'signal_accuracy': signal_accuracy,
                'trade_success_rate': trade_success,
                'portfolio_performance_percent': portfolio_perf,
                'cache_hit_rate': cache_hit,
                'notification_delivery_rate': notification_rate,
                'timestamp': datetime.utcnow()
            })
            
        except Exception as e:
            logger.error(
                "Error collecting business metrics",
                error=str(e)
            )
    
    def record_request_metric(self, method: str, endpoint: str, duration: float):
        """Record request performance metric"""
        self.request_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def record_database_metric(self, operation: str, duration: float):
        """Record database operation metric"""
        self.database_query_duration.labels(operation=operation).observe(duration)
    
    def record_api_metric(self, api_name: str, duration: float):
        """Record external API call metric"""
        self.api_response_time.labels(api_name=api_name).observe(duration)
    
    def record_error_metric(self, error_type: str, module: str):
        """Record error occurrence"""
        self.error_count.labels(error_type=error_type, module=module).inc()
    
    def record_failed_request(self, status_code: int):
        """Record failed request"""
        self.failed_requests.labels(status_code=str(status_code)).inc()
    
    def record_exception(self, exception_type: str):
        """Record exception occurrence"""
        self.exception_count.labels(exception_type=exception_type).inc()
    
    def record_trade_metric(self, successful: bool):
        """Record trade execution metric"""
        self.total_trades.inc()
        if successful:
            self.successful_trades.inc()
    
    def record_token_analysis_time(self, duration: float):
        """Record token analysis duration"""
        self.token_analysis_speed.observe(duration)
    
    async def get_prometheus_metrics(self) -> str:
        """Get metrics in Prometheus format"""
        try:
            return generate_latest().decode('utf-8')
        except Exception as e:
            logger.error(
                "Error generating Prometheus metrics",
                error=str(e)
            )
            return ""
    
    async def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary"""
        uptime = time.time() - self.start_time
        
        return {
            'system_metrics': self.resource_metrics,
            'business_metrics': self.business_metrics,
            'performance_metrics': self.performance_metrics,
            'error_metrics': self.error_metrics,
            'uptime_seconds': uptime,
            'collection_status': 'running' if self.is_running else 'stopped',
            'timestamp': datetime.utcnow()
        }
    
    async def get_health_metrics(self) -> Dict[str, Any]:
        """Get health-related metrics"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            
            return {
                'cpu_usage_percent': cpu_percent,
                'memory_usage_percent': memory.percent,
                'disk_usage_percent': psutil.disk_usage('/').percent,
                'uptime_seconds': time.time() - self.start_time,
                'is_healthy': (
                    cpu_percent < MONITORING_CONSTANTS.HIGH_CPU_PERCENT and
                    memory.percent < MONITORING_CONSTANTS.HIGH_MEMORY_PERCENT
                )
            }
        except Exception as e:
            logger.error(
                "Error getting health metrics",
                error=str(e)
            )
            return {'is_healthy': False, 'error': str(e)}
