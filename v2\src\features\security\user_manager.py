"""
👤 User Manager

Comprehensive user management system with role-based access control,
user profiles, permissions, and administrative functions.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from ...config.logging_config import get_logger
from ...database.models import User, Portfolio
from ...shared.types import UserRole

logger = get_logger(__name__)


class PermissionType(str, Enum):
    """Permission types"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"
    EXECUTE = "execute"


class ResourceType(str, Enum):
    """Resource types for permissions"""
    PORTFOLIO = "portfolio"
    TRADE = "trade"
    SIGNAL = "signal"
    USER = "user"
    SYSTEM = "system"
    ANALYTICS = "analytics"
    AUTOMATION = "automation"


class UserManager:
    """
    👤 User Manager
    
    Manages user accounts, roles, permissions, and profiles:
    - User profile management
    - Role-based access control (RBAC)
    - Permission management
    - User activity tracking
    - Administrative functions
    """
    
    def __init__(self):
        self.logger = logger
        
        # Role permissions mapping
        self.role_permissions = {
            UserRole.ADMIN: {
                ResourceType.PORTFOLIO: [PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.ADMIN],
                ResourceType.TRADE: [PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.EXECUTE],
                ResourceType.SIGNAL: [PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE],
                ResourceType.USER: [PermissionType.READ, PermissionType.WRITE, PermissionType.DELETE, PermissionType.ADMIN],
                ResourceType.SYSTEM: [PermissionType.READ, PermissionType.WRITE, PermissionType.ADMIN],
                ResourceType.ANALYTICS: [PermissionType.READ, PermissionType.WRITE, PermissionType.EXECUTE],
                ResourceType.AUTOMATION: [PermissionType.READ, PermissionType.WRITE, PermissionType.EXECUTE]
            },
            UserRole.PREMIUM: {
                ResourceType.PORTFOLIO: [PermissionType.READ, PermissionType.WRITE],
                ResourceType.TRADE: [PermissionType.READ, PermissionType.WRITE, PermissionType.EXECUTE],
                ResourceType.SIGNAL: [PermissionType.READ, PermissionType.WRITE],
                ResourceType.ANALYTICS: [PermissionType.READ, PermissionType.EXECUTE],
                ResourceType.AUTOMATION: [PermissionType.READ, PermissionType.EXECUTE]
            },
            UserRole.USER: {
                ResourceType.PORTFOLIO: [PermissionType.READ, PermissionType.WRITE],
                ResourceType.TRADE: [PermissionType.READ],
                ResourceType.SIGNAL: [PermissionType.READ],
                ResourceType.ANALYTICS: [PermissionType.READ]
            },
            UserRole.VIEWER: {
                ResourceType.PORTFOLIO: [PermissionType.READ],
                ResourceType.TRADE: [PermissionType.READ],
                ResourceType.SIGNAL: [PermissionType.READ],
                ResourceType.ANALYTICS: [PermissionType.READ]
            }
        }
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile information"""
        try:
            user = await User.get(user_id)
            if not user:
                return None
            
            # Get user portfolios
            portfolios = await Portfolio.find({"user_id": user_id}).to_list()
            
            # Calculate user statistics
            stats = await self._calculate_user_stats(user_id)
            
            return {
                "user_id": str(user.id),
                "email": user.email,
                "username": user.username,
                "role": user.role.value,
                "is_active": user.is_active,
                "is_verified": user.is_verified,
                "created_at": user.created_at,
                "last_login": user.last_login,
                "verified_at": getattr(user, 'verified_at', None),
                "portfolio_count": len(portfolios),
                "statistics": stats,
                "permissions": self.get_user_permissions(user.role)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user profile: {str(e)}")
            return None
    
    async def update_user_profile(
        self,
        user_id: str,
        updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Update user profile"""
        try:
            user = await User.get(user_id)
            if not user:
                return {"success": False, "error": "User not found"}
            
            # Allowed fields for update
            allowed_fields = ["username", "email", "profile_data"]
            
            updated_fields = []
            for field, value in updates.items():
                if field in allowed_fields:
                    if field == "username":
                        # Check if username is available
                        existing = await User.find_one({"username": value, "_id": {"$ne": user.id}})
                        if existing:
                            return {"success": False, "error": "Username is already taken"}
                    
                    elif field == "email":
                        # Check if email is available
                        existing = await User.find_one({"email": value, "_id": {"$ne": user.id}})
                        if existing:
                            return {"success": False, "error": "Email is already taken"}
                        # Reset verification if email changed
                        user.is_verified = False
                    
                    setattr(user, field, value)
                    updated_fields.append(field)
            
            if updated_fields:
                user.updated_at = datetime.utcnow()
                await user.save()
                
                self.logger.info(f"User profile updated: {user_id}, fields: {updated_fields}")
                
                return {
                    "success": True,
                    "updated_fields": updated_fields,
                    "message": "Profile updated successfully"
                }
            else:
                return {"success": False, "error": "No valid fields to update"}
            
        except Exception as e:
            self.logger.error(f"Error updating user profile: {str(e)}")
            return {"success": False, "error": "Profile update failed"}
    
    async def change_user_role(
        self,
        admin_user_id: str,
        target_user_id: str,
        new_role: UserRole
    ) -> Dict[str, Any]:
        """Change user role (admin only)"""
        try:
            # Verify admin permissions
            admin_user = await User.get(admin_user_id)
            if not admin_user or admin_user.role != UserRole.ADMIN:
                return {"success": False, "error": "Insufficient permissions"}
            
            # Get target user
            target_user = await User.get(target_user_id)
            if not target_user:
                return {"success": False, "error": "Target user not found"}
            
            # Prevent self-demotion
            if admin_user_id == target_user_id and new_role != UserRole.ADMIN:
                return {"success": False, "error": "Cannot change your own admin role"}
            
            old_role = target_user.role
            target_user.role = new_role
            target_user.updated_at = datetime.utcnow()
            await target_user.save()
            
            self.logger.info(f"User role changed: {target_user_id} from {old_role} to {new_role}")
            
            return {
                "success": True,
                "old_role": old_role.value,
                "new_role": new_role.value,
                "message": "User role updated successfully"
            }
            
        except Exception as e:
            self.logger.error(f"Error changing user role: {str(e)}")
            return {"success": False, "error": "Role change failed"}
    
    async def deactivate_user(
        self,
        admin_user_id: str,
        target_user_id: str,
        reason: str = None
    ) -> Dict[str, Any]:
        """Deactivate user account (admin only)"""
        try:
            # Verify admin permissions
            admin_user = await User.get(admin_user_id)
            if not admin_user or admin_user.role != UserRole.ADMIN:
                return {"success": False, "error": "Insufficient permissions"}
            
            # Get target user
            target_user = await User.get(target_user_id)
            if not target_user:
                return {"success": False, "error": "Target user not found"}
            
            # Prevent self-deactivation
            if admin_user_id == target_user_id:
                return {"success": False, "error": "Cannot deactivate your own account"}
            
            target_user.is_active = False
            target_user.deactivated_at = datetime.utcnow()
            target_user.deactivation_reason = reason
            target_user.updated_at = datetime.utcnow()
            await target_user.save()
            
            self.logger.info(f"User deactivated: {target_user_id}, reason: {reason}")
            
            return {
                "success": True,
                "message": "User account deactivated successfully"
            }
            
        except Exception as e:
            self.logger.error(f"Error deactivating user: {str(e)}")
            return {"success": False, "error": "User deactivation failed"}
    
    async def reactivate_user(
        self,
        admin_user_id: str,
        target_user_id: str
    ) -> Dict[str, Any]:
        """Reactivate user account (admin only)"""
        try:
            # Verify admin permissions
            admin_user = await User.get(admin_user_id)
            if not admin_user or admin_user.role != UserRole.ADMIN:
                return {"success": False, "error": "Insufficient permissions"}
            
            # Get target user
            target_user = await User.get(target_user_id)
            if not target_user:
                return {"success": False, "error": "Target user not found"}
            
            target_user.is_active = True
            target_user.reactivated_at = datetime.utcnow()
            target_user.updated_at = datetime.utcnow()
            await target_user.save()
            
            self.logger.info(f"User reactivated: {target_user_id}")
            
            return {
                "success": True,
                "message": "User account reactivated successfully"
            }
            
        except Exception as e:
            self.logger.error(f"Error reactivating user: {str(e)}")
            return {"success": False, "error": "User reactivation failed"}
    
    async def list_users(
        self,
        admin_user_id: str,
        filters: Dict[str, Any] = None,
        limit: int = 100,
        offset: int = 0
    ) -> Dict[str, Any]:
        """List users with filtering (admin only)"""
        try:
            # Verify admin permissions
            admin_user = await User.get(admin_user_id)
            if not admin_user or admin_user.role != UserRole.ADMIN:
                return {"success": False, "error": "Insufficient permissions"}
            
            # Build query
            query = {}
            if filters:
                if "role" in filters:
                    query["role"] = filters["role"]
                if "is_active" in filters:
                    query["is_active"] = filters["is_active"]
                if "is_verified" in filters:
                    query["is_verified"] = filters["is_verified"]
            
            # Get users
            users = await User.find(query).skip(offset).limit(limit).to_list()
            total_count = await User.find(query).count()
            
            # Format user data
            user_list = []
            for user in users:
                user_data = {
                    "user_id": str(user.id),
                    "email": user.email,
                    "username": user.username,
                    "role": user.role.value,
                    "is_active": user.is_active,
                    "is_verified": user.is_verified,
                    "created_at": user.created_at,
                    "last_login": user.last_login
                }
                user_list.append(user_data)
            
            return {
                "success": True,
                "users": user_list,
                "total_count": total_count,
                "limit": limit,
                "offset": offset
            }
            
        except Exception as e:
            self.logger.error(f"Error listing users: {str(e)}")
            return {"success": False, "error": "Failed to list users"}
    
    def get_user_permissions(self, user_role: UserRole) -> Dict[str, List[str]]:
        """Get permissions for a user role"""
        try:
            permissions = self.role_permissions.get(user_role, {})
            
            # Convert to string format
            formatted_permissions = {}
            for resource, perms in permissions.items():
                formatted_permissions[resource.value] = [perm.value for perm in perms]
            
            return formatted_permissions
            
        except Exception as e:
            self.logger.error(f"Error getting user permissions: {str(e)}")
            return {}
    
    def check_permission(
        self,
        user_role: UserRole,
        resource: ResourceType,
        permission: PermissionType
    ) -> bool:
        """Check if user role has specific permission for resource"""
        try:
            role_permissions = self.role_permissions.get(user_role, {})
            resource_permissions = role_permissions.get(resource, [])
            
            return permission in resource_permissions
            
        except Exception as e:
            self.logger.error(f"Error checking permission: {str(e)}")
            return False
    
    async def _calculate_user_stats(self, user_id: str) -> Dict[str, Any]:
        """Calculate user statistics"""
        try:
            # Get user portfolios
            portfolios = await Portfolio.find({"user_id": user_id}).to_list()
            
            # Calculate basic stats
            total_portfolios = len(portfolios)
            active_portfolios = len([p for p in portfolios if p.status == "active"])
            
            # Calculate total portfolio value
            total_value = sum(p.total_value for p in portfolios if p.total_value)
            total_pnl = sum(p.total_pnl for p in portfolios if p.total_pnl)
            
            return {
                "total_portfolios": total_portfolios,
                "active_portfolios": active_portfolios,
                "total_portfolio_value": float(total_value),
                "total_pnl": float(total_pnl),
                "account_age_days": (datetime.utcnow() - portfolios[0].created_at).days if portfolios else 0
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating user stats: {str(e)}")
            return {}
