"""
🔧 Shared Middleware

Common middleware functions for request processing, authentication,
and error handling.
"""

import time
import uuid
import psutil
from datetime import datetime
from typing import Callable, Dict, Any, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..config.logging_config import get_logger

logger = get_logger(__name__)


async def logging_middleware(request: Request, call_next: Callable) -> Response:
    """
    Log all requests and responses
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response
    """
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url.path}")
    
    try:
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Log response
        logger.info(
            f"Response: {response.status_code} "
            f"({process_time:.3f}s) "
            f"{request.method} {request.url.path}"
        )
        
        # Add processing time header
        response.headers["X-Process-Time"] = str(process_time)
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Error: {str(e)} "
            f"({process_time:.3f}s) "
            f"{request.method} {request.url.path}"
        )
        raise


async def cors_middleware(request: Request, call_next: Callable) -> Response:
    """
    Handle CORS headers
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response with CORS headers
    """
    # Handle preflight requests
    if request.method == "OPTIONS":
        response = Response()
    else:
        response = await call_next(request)
    
    # Add CORS headers
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    response.headers["Access-Control-Max-Age"] = "86400"
    
    return response


async def security_headers_middleware(request: Request, call_next: Callable) -> Response:
    """
    Add security headers to responses
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response with security headers
    """
    response = await call_next(request)
    
    # Security headers
    security_headers = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    
    for header, value in security_headers.items():
        response.headers[header] = value
    
    return response


async def error_handling_middleware(request: Request, call_next: Callable) -> Response:
    """
    Global error handling middleware
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response or error response
    """
    try:
        return await call_next(request)
        
    except HTTPException:
        # Re-raise HTTP exceptions (handled by FastAPI)
        raise
        
    except Exception as e:
        # Log unexpected errors
        logger.error(f"Unexpected error in {request.method} {request.url.path}: {str(e)}")
        
        # Return generic error response
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": "An unexpected error occurred",
                "timestamp": datetime.utcnow().isoformat()
            }
        )


def create_rate_limit_middleware(
    requests_per_minute: int = 60,
    requests_per_hour: int = 1000
):
    """
    Create rate limiting middleware
    
    Args:
        requests_per_minute: Requests per minute limit
        requests_per_hour: Requests per hour limit
        
    Returns:
        Rate limiting middleware function
    """
    # Simple in-memory storage (use Redis in production)
    request_counts: Dict[str, Dict[str, Any]] = {}
    
    async def rate_limit_middleware(request: Request, call_next: Callable) -> Response:
        """Rate limiting middleware"""
        client_ip = request.client.host
        current_time = time.time()
        
        # Initialize client data if not exists
        if client_ip not in request_counts:
            request_counts[client_ip] = {
                "minute_requests": [],
                "hour_requests": []
            }
        
        client_data = request_counts[client_ip]
        
        # Clean old requests
        client_data["minute_requests"] = [
            req_time for req_time in client_data["minute_requests"]
            if current_time - req_time < 60
        ]
        client_data["hour_requests"] = [
            req_time for req_time in client_data["hour_requests"]
            if current_time - req_time < 3600
        ]
        
        # Check limits
        if len(client_data["minute_requests"]) >= requests_per_minute:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests per minute (limit: {requests_per_minute})"
                },
                headers={"Retry-After": "60"}
            )
        
        if len(client_data["hour_requests"]) >= requests_per_hour:
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests per hour (limit: {requests_per_hour})"
                },
                headers={"Retry-After": "3600"}
            )
        
        # Record request
        client_data["minute_requests"].append(current_time)
        client_data["hour_requests"].append(current_time)
        
        return await call_next(request)
    
    return rate_limit_middleware


async def request_validation_middleware(request: Request, call_next: Callable) -> Response:
    """
    Validate incoming requests
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response or validation error
    """
    try:
        # Check content length
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
            return JSONResponse(
                status_code=413,
                content={
                    "error": "Request too large",
                    "message": "Request body exceeds maximum size limit"
                }
            )
        
        # Check content type for POST/PUT requests
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("content-type", "")
            if not content_type.startswith(("application/json", "multipart/form-data")):
                return JSONResponse(
                    status_code=415,
                    content={
                        "error": "Unsupported media type",
                        "message": "Content-Type must be application/json or multipart/form-data"
                    }
                )
        
        return await call_next(request)
        
    except Exception as e:
        logger.error(f"Request validation error: {str(e)}")
        return JSONResponse(
            status_code=400,
            content={
                "error": "Bad request",
                "message": "Request validation failed"
            }
        )


async def cache_control_middleware(request: Request, call_next: Callable) -> Response:
    """
    Add cache control headers
    
    Args:
        request: FastAPI request
        call_next: Next middleware/endpoint
        
    Returns:
        Response with cache headers
    """
    response = await call_next(request)
    
    # Set cache headers based on endpoint
    if request.url.path.startswith("/api/static/"):
        # Static resources - cache for 1 hour
        response.headers["Cache-Control"] = "public, max-age=3600"
    elif request.url.path.startswith("/api/data/"):
        # Data endpoints - cache for 5 minutes
        response.headers["Cache-Control"] = "public, max-age=300"
    else:
        # Default - no cache
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
    
    return response


def create_auth_middleware(excluded_paths: Optional[list] = None):
    """
    Create authentication middleware
    
    Args:
        excluded_paths: Paths to exclude from authentication
        
    Returns:
        Authentication middleware function
    """
    excluded_paths = excluded_paths or [
        "/api/auth/login",
        "/api/auth/register",
        "/api/health",
        "/docs",
        "/redoc",
        "/openapi.json"
    ]
    
    async def auth_middleware(request: Request, call_next: Callable) -> Response:
        """Authentication middleware"""
        # Skip authentication for excluded paths
        if any(request.url.path.startswith(path) for path in excluded_paths):
            return await call_next(request)
        
        # Check for Authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=401,
                content={
                    "error": "Unauthorized",
                    "message": "Missing or invalid authorization header"
                }
            )
        
        # Extract token
        token = auth_header.split(" ")[1]
        
        # Validate token (simplified - use proper JWT validation in production)
        if not token or len(token) < 10:
            return JSONResponse(
                status_code=401,
                content={
                    "error": "Unauthorized",
                    "message": "Invalid token"
                }
            )
        
        # Add user info to request state (would come from token validation)
        request.state.user_id = "user_from_token"
        request.state.user_role = "user"
        
        return await call_next(request)
    
    return auth_middleware


# 📊 Middleware Classes for FastAPI
class LoggingMiddleware(BaseHTTPMiddleware):
    """
    🔍 Enhanced logging middleware with request tracking
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with enhanced logging"""
        start_time = time.time()
        request_id = str(uuid.uuid4())

        # Add request ID to state
        request.state.request_id = request_id

        # Log request
        logger.info(
            "Request started",
            request_id=request_id,
            method=request.method,
            path=request.url.path,
            query_params=str(request.query_params),
            client_ip=request.client.host if request.client else "unknown",
            user_agent=request.headers.get("user-agent", "unknown")
        )

        try:
            response = await call_next(request)

            # Calculate processing time
            process_time = time.time() - start_time

            # Log response
            logger.info(
                "Request completed",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                status_code=response.status_code,
                process_time=f"{process_time:.3f}s"
            )

            # Add headers
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}"

            return response

        except Exception as e:
            process_time = time.time() - start_time
            logger.error(
                "Request failed",
                request_id=request_id,
                method=request.method,
                path=request.url.path,
                error=str(e),
                error_type=type(e).__name__,
                process_time=f"{process_time:.3f}s",
                exc_info=True
            )
            raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    📊 Metrics collection middleware
    """

    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.request_times = []
        self.error_count = 0

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Collect metrics for each request"""
        start_time = time.time()
        self.request_count += 1

        try:
            response = await call_next(request)

            # Record metrics
            process_time = time.time() - start_time
            self.request_times.append(process_time)

            # Keep only last 1000 request times
            if len(self.request_times) > 1000:
                self.request_times = self.request_times[-1000:]

            # Add metrics headers
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Error-Count"] = str(self.error_count)

            return response

        except Exception as e:
            self.error_count += 1
            process_time = time.time() - start_time
            self.request_times.append(process_time)

            logger.error(
                "Request error in metrics middleware",
                error=str(e),
                process_time=f"{process_time:.3f}s"
            )
            raise


class SecurityMiddleware(BaseHTTPMiddleware):
    """
    🔒 Security headers and validation middleware
    """

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers and validate requests"""

        # Security validations
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
            return JSONResponse(
                status_code=413,
                content={
                    "error": "Request too large",
                    "message": "Request body exceeds maximum size limit"
                }
            )

        response = await call_next(request)

        # Add security headers
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "X-Permitted-Cross-Domain-Policies": "none",
            "X-Download-Options": "noopen"
        }

        for header, value in security_headers.items():
            response.headers[header] = value

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    🚦 Rate limiting middleware
    """

    def __init__(self, app, requests_per_minute: int = 60, requests_per_hour: int = 1000):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.request_counts: Dict[str, Dict[str, Any]] = {}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting"""
        client_ip = request.client.host if request.client else "unknown"
        current_time = time.time()

        # Skip rate limiting for health checks
        if request.url.path in ["/health", "/ready", "/metrics"]:
            return await call_next(request)

        # Initialize client data if not exists
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = {
                "minute_requests": [],
                "hour_requests": []
            }

        client_data = self.request_counts[client_ip]

        # Clean old requests
        client_data["minute_requests"] = [
            req_time for req_time in client_data["minute_requests"]
            if current_time - req_time < 60
        ]
        client_data["hour_requests"] = [
            req_time for req_time in client_data["hour_requests"]
            if current_time - req_time < 3600
        ]

        # Check limits
        if len(client_data["minute_requests"]) >= self.requests_per_minute:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                requests_per_minute=len(client_data["minute_requests"]),
                limit=self.requests_per_minute
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests per minute (limit: {self.requests_per_minute})"
                },
                headers={"Retry-After": "60"}
            )

        if len(client_data["hour_requests"]) >= self.requests_per_hour:
            logger.warning(
                "Rate limit exceeded",
                client_ip=client_ip,
                requests_per_hour=len(client_data["hour_requests"]),
                limit=self.requests_per_hour
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests per hour (limit: {self.requests_per_hour})"
                },
                headers={"Retry-After": "3600"}
            )

        # Record request
        client_data["minute_requests"].append(current_time)
        client_data["hour_requests"].append(current_time)

        return await call_next(request)
