"""
📊 Prometheus Integration

Enhanced Prometheus metrics integration with custom business metrics,
Grafana dashboard compatibility, and advanced metric collection.
"""

import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from prometheus_client import (
    Counter, Histogram, Gauge, Summary, Info, Enum,
    generate_latest, CONTENT_TYPE_LATEST, REGISTRY,
    CollectorRegistry, multiprocess, values
)

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class PrometheusIntegration:
    """
    📊 Enhanced Prometheus metrics integration
    """
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or REGISTRY
        self.settings = settings
        
        # Initialize all metrics
        self._init_system_metrics()
        self._init_business_metrics()
        self._init_application_metrics()
        self._init_custom_metrics()
        
        logger.info("PrometheusIntegration initialized")
    
    def _init_system_metrics(self):
        """Initialize system-level metrics"""
        # CPU metrics
        self.cpu_usage = Gauge(
            'system_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        # Memory metrics
        self.memory_usage_bytes = Gauge(
            'system_memory_usage_bytes',
            'Memory usage in bytes',
            registry=self.registry
        )
        self.memory_usage_percent = Gauge(
            'system_memory_usage_percent',
            'Memory usage percentage',
            registry=self.registry
        )
        
        # Disk metrics
        self.disk_usage_percent = Gauge(
            'system_disk_usage_percent',
            'Disk usage percentage',
            registry=self.registry
        )
        self.disk_io_read_bytes = Counter(
            'system_disk_io_read_bytes_total',
            'Total disk read bytes',
            registry=self.registry
        )
        self.disk_io_write_bytes = Counter(
            'system_disk_io_write_bytes_total',
            'Total disk write bytes',
            registry=self.registry
        )
        
        # Network metrics
        self.network_bytes_sent = Counter(
            'system_network_bytes_sent_total',
            'Total network bytes sent',
            registry=self.registry
        )
        self.network_bytes_received = Counter(
            'system_network_bytes_received_total',
            'Total network bytes received',
            registry=self.registry
        )
        self.active_connections = Gauge(
            'system_active_connections',
            'Number of active network connections',
            registry=self.registry
        )
    
    def _init_business_metrics(self):
        """Initialize business-specific metrics"""
        # Trading metrics
        self.total_trades = Counter(
            'trading_total_trades',
            'Total number of trades executed',
            ['status', 'token_symbol'],
            registry=self.registry
        )
        self.trade_volume_usd = Counter(
            'trading_volume_usd_total',
            'Total trading volume in USD',
            ['token_symbol'],
            registry=self.registry
        )
        self.trade_success_rate = Gauge(
            'trading_success_rate',
            'Trading success rate percentage',
            registry=self.registry
        )
        
        # Signal metrics
        self.signals_generated = Counter(
            'signals_generated_total',
            'Total signals generated',
            ['signal_type', 'strength'],
            registry=self.registry
        )
        self.signal_accuracy = Gauge(
            'signals_accuracy_ratio',
            'Signal accuracy ratio',
            ['signal_type'],
            registry=self.registry
        )
        
        # Portfolio metrics
        self.portfolio_value_usd = Gauge(
            'portfolio_value_usd',
            'Portfolio value in USD',
            ['portfolio_id'],
            registry=self.registry
        )
        self.portfolio_pnl_percent = Gauge(
            'portfolio_pnl_percent',
            'Portfolio P&L percentage',
            ['portfolio_id'],
            registry=self.registry
        )
        
        # Token metrics
        self.tokens_analyzed = Counter(
            'tokens_analyzed_total',
            'Total tokens analyzed',
            registry=self.registry
        )
        self.token_analysis_duration = Histogram(
            'token_analysis_duration_seconds',
            'Token analysis duration in seconds',
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0],
            registry=self.registry
        )
    
    def _init_application_metrics(self):
        """Initialize application-level metrics"""
        # HTTP metrics
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code'],
            registry=self.registry
        )
        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration in seconds',
            ['method', 'endpoint'],
            buckets=[0.01, 0.05, 0.1, 0.5, 1.0, 2.0, 5.0, 10.0],
            registry=self.registry
        )
        self.http_request_size_bytes = Histogram(
            'http_request_size_bytes',
            'HTTP request size in bytes',
            buckets=[100, 1000, 10000, 100000, 1000000],
            registry=self.registry
        )
        self.http_response_size_bytes = Histogram(
            'http_response_size_bytes',
            'HTTP response size in bytes',
            buckets=[100, 1000, 10000, 100000, 1000000],
            registry=self.registry
        )
        
        # Database metrics
        self.database_queries_total = Counter(
            'database_queries_total',
            'Total database queries',
            ['operation', 'collection', 'status'],
            registry=self.registry
        )
        self.database_query_duration = Histogram(
            'database_query_duration_seconds',
            'Database query duration in seconds',
            ['operation', 'collection'],
            buckets=[0.001, 0.01, 0.1, 0.5, 1.0, 2.0, 5.0],
            registry=self.registry
        )
        
        # Cache metrics
        self.cache_operations_total = Counter(
            'cache_operations_total',
            'Total cache operations',
            ['operation', 'status'],
            registry=self.registry
        )
        self.cache_hit_rate = Gauge(
            'cache_hit_rate',
            'Cache hit rate percentage',
            registry=self.registry
        )
        
        # External API metrics
        self.external_api_requests_total = Counter(
            'external_api_requests_total',
            'Total external API requests',
            ['api_name', 'endpoint', 'status_code'],
            registry=self.registry
        )
        self.external_api_duration = Histogram(
            'external_api_duration_seconds',
            'External API request duration in seconds',
            ['api_name', 'endpoint'],
            buckets=[0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0],
            registry=self.registry
        )
    
    def _init_custom_metrics(self):
        """Initialize custom application metrics"""
        # Application info
        self.app_info = Info(
            'tokentracker_app_info',
            'TokenTracker application information',
            registry=self.registry
        )
        self.app_info.info({
            'version': '2.0.0',
            'environment': settings.environment,
            'build_time': datetime.utcnow().isoformat()
        })
        
        # Application status
        self.app_status = Enum(
            'tokentracker_app_status',
            'TokenTracker application status',
            states=['starting', 'running', 'stopping', 'stopped', 'error'],
            registry=self.registry
        )
        self.app_status.state('running')
        
        # Uptime
        self.app_uptime_seconds = Gauge(
            'tokentracker_uptime_seconds',
            'Application uptime in seconds',
            registry=self.registry
        )
        
        # Health status
        self.health_status = Gauge(
            'tokentracker_health_status',
            'Health status (1=healthy, 0=unhealthy)',
            ['service'],
            registry=self.registry
        )
        
        # Alert metrics
        self.alerts_active = Gauge(
            'alerts_active_count',
            'Number of active alerts',
            ['severity'],
            registry=self.registry
        )
        self.alerts_total = Counter(
            'alerts_total',
            'Total alerts generated',
            ['rule_name', 'severity'],
            registry=self.registry
        )
        
        # Performance metrics
        self.performance_bottlenecks = Counter(
            'performance_bottlenecks_total',
            'Total performance bottlenecks detected',
            ['type'],
            registry=self.registry
        )
        
        # Notification metrics
        self.notifications_sent = Counter(
            'notifications_sent_total',
            'Total notifications sent',
            ['channel', 'type', 'status'],
            registry=self.registry
        )
        self.notification_delivery_rate = Gauge(
            'notification_delivery_rate',
            'Notification delivery success rate',
            ['channel'],
            registry=self.registry
        )
    
    # Metric recording methods
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float, request_size: int = 0, response_size: int = 0):
        """Record HTTP request metrics"""
        self.http_requests_total.labels(method=method, endpoint=endpoint, status_code=str(status_code)).inc()
        self.http_request_duration.labels(method=method, endpoint=endpoint).observe(duration)
        if request_size > 0:
            self.http_request_size_bytes.observe(request_size)
        if response_size > 0:
            self.http_response_size_bytes.observe(response_size)
    
    def record_database_query(self, operation: str, collection: str, duration: float, status: str = "success"):
        """Record database query metrics"""
        self.database_queries_total.labels(operation=operation, collection=collection, status=status).inc()
        self.database_query_duration.labels(operation=operation, collection=collection).observe(duration)
    
    def record_trade(self, token_symbol: str, status: str, volume_usd: float):
        """Record trading metrics"""
        self.total_trades.labels(status=status, token_symbol=token_symbol).inc()
        self.trade_volume_usd.labels(token_symbol=token_symbol).inc(volume_usd)
    
    def record_signal(self, signal_type: str, strength: str):
        """Record signal generation metrics"""
        self.signals_generated.labels(signal_type=signal_type, strength=strength).inc()
    
    def record_external_api_call(self, api_name: str, endpoint: str, duration: float, status_code: int):
        """Record external API call metrics"""
        self.external_api_requests_total.labels(
            api_name=api_name, 
            endpoint=endpoint, 
            status_code=str(status_code)
        ).inc()
        self.external_api_duration.labels(api_name=api_name, endpoint=endpoint).observe(duration)
    
    def record_cache_operation(self, operation: str, status: str):
        """Record cache operation metrics"""
        self.cache_operations_total.labels(operation=operation, status=status).inc()
    
    def record_notification(self, channel: str, notification_type: str, status: str):
        """Record notification metrics"""
        self.notifications_sent.labels(channel=channel, type=notification_type, status=status).inc()
    
    def record_alert(self, rule_name: str, severity: str):
        """Record alert metrics"""
        self.alerts_total.labels(rule_name=rule_name, severity=severity).inc()
    
    def record_performance_bottleneck(self, bottleneck_type: str):
        """Record performance bottleneck"""
        self.performance_bottlenecks.labels(type=bottleneck_type).inc()
    
    def record_token_analysis(self, duration: float):
        """Record token analysis metrics"""
        self.tokens_analyzed.inc()
        self.token_analysis_duration.observe(duration)
    
    # System metric updates
    def update_system_metrics(self, cpu_percent: float, memory_bytes: int, memory_percent: float, disk_percent: float, connections: int):
        """Update system metrics"""
        self.cpu_usage.set(cpu_percent)
        self.memory_usage_bytes.set(memory_bytes)
        self.memory_usage_percent.set(memory_percent)
        self.disk_usage_percent.set(disk_percent)
        self.active_connections.set(connections)
    
    def update_business_metrics(self, trade_success_rate: float, signal_accuracy: Dict[str, float], cache_hit_rate: float):
        """Update business metrics"""
        self.trade_success_rate.set(trade_success_rate)
        self.cache_hit_rate.set(cache_hit_rate)
        
        for signal_type, accuracy in signal_accuracy.items():
            self.signal_accuracy.labels(signal_type=signal_type).set(accuracy)
    
    def update_health_status(self, service_health: Dict[str, bool]):
        """Update health status metrics"""
        for service, is_healthy in service_health.items():
            self.health_status.labels(service=service).set(1 if is_healthy else 0)
    
    def update_alert_counts(self, active_alerts_by_severity: Dict[str, int]):
        """Update alert count metrics"""
        for severity, count in active_alerts_by_severity.items():
            self.alerts_active.labels(severity=severity).set(count)
    
    def update_portfolio_metrics(self, portfolio_id: str, value_usd: float, pnl_percent: float):
        """Update portfolio metrics"""
        self.portfolio_value_usd.labels(portfolio_id=portfolio_id).set(value_usd)
        self.portfolio_pnl_percent.labels(portfolio_id=portfolio_id).set(pnl_percent)
    
    def update_notification_delivery_rate(self, channel: str, rate: float):
        """Update notification delivery rate"""
        self.notification_delivery_rate.labels(channel=channel).set(rate)
    
    def set_app_status(self, status: str):
        """Set application status"""
        self.app_status.state(status)
    
    def update_uptime(self, uptime_seconds: float):
        """Update application uptime"""
        self.app_uptime_seconds.set(uptime_seconds)
    
    def get_metrics(self) -> str:
        """Get all metrics in Prometheus format"""
        return generate_latest(self.registry).decode('utf-8')
    
    def get_content_type(self) -> str:
        """Get Prometheus content type"""
        return CONTENT_TYPE_LATEST
