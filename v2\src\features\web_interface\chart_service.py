"""
Chart Service

Provides performance charts, signal visualization, and trade history charts
for the web interface. Optimized for real-time updates and interactive features.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

import structlog
from fastapi import HTT<PERSON>Exception

from src.shared.types import (
    ChartData, ChartConfig, TimeSeriesData, 
    PerformanceChart, SignalChart, TradeChart
)
from src.features.paper_trading.portfolio_manager import PortfolioManager
from src.features.paper_trading.performance_tracker import PerformanceTracker
from src.features.signal_processing.signal_generator import SignalGenerator
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


class ChartType(Enum):
    """Chart type enumeration"""
    LINE = "line"
    BAR = "bar"
    CANDLESTICK = "candlestick"
    AREA = "area"
    SCATTER = "scatter"


class TimeFrame(Enum):
    """Time frame enumeration"""
    HOUR = "1h"
    DAY = "1d"
    WEEK = "1w"
    MONTH = "1m"
    QUARTER = "3m"
    YEAR = "1y"


@dataclass
class ChartServiceConfig:
    """Chart service configuration"""
    max_data_points: int = 1000
    cache_ttl: int = 300  # seconds
    real_time_interval: int = 30  # seconds
    default_timeframe: TimeFrame = TimeFrame.DAY


class ChartService:
    """
    Provides chart data and visualization services for the web interface.
    Supports real-time updates and multiple chart types.
    """
    
    def __init__(
        self,
        portfolio_manager: PortfolioManager,
        performance_tracker: PerformanceTracker,
        signal_generator: SignalGenerator,
        cache_manager: CacheManager,
        config: Optional[ChartServiceConfig] = None
    ):
        self.portfolio_manager = portfolio_manager
        self.performance_tracker = performance_tracker
        self.signal_generator = signal_generator
        self.cache_manager = cache_manager
        self.config = config or ChartServiceConfig()
        self.logger = logger.bind(service="chart")
    
    async def get_portfolio_chart(
        self,
        user_id: str,
        timeframe: TimeFrame = TimeFrame.DAY,
        chart_type: ChartType = ChartType.LINE
    ) -> PerformanceChart:
        """
        Get portfolio performance chart.
        
        Args:
            user_id: User identifier
            timeframe: Chart timeframe
            chart_type: Type of chart
            
        Returns:
            Portfolio performance chart data
        """
        try:
            cache_key = f"portfolio_chart:{user_id}:{timeframe.value}:{chart_type.value}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Portfolio chart served from cache", user_id=user_id)
                return PerformanceChart(**cached_data)
            
            # Get date range based on timeframe
            end_date = datetime.utcnow()
            start_date = self._get_start_date(end_date, timeframe)
            
            # Get portfolio history
            history = await self.portfolio_manager.get_portfolio_history(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            if not history:
                return PerformanceChart(
                    chart_data=ChartData(labels=[], datasets=[]),
                    timeframe=timeframe.value,
                    chart_type=chart_type.value,
                    metrics={}
                )
            
            # Process data based on timeframe
            processed_data = self._process_time_series_data(history, timeframe)
            
            # Create chart data
            chart_data = self._create_portfolio_chart_data(processed_data, chart_type)
            
            # Calculate metrics
            metrics = await self._calculate_chart_metrics(processed_data)
            
            chart = PerformanceChart(
                chart_data=chart_data,
                timeframe=timeframe.value,
                chart_type=chart_type.value,
                metrics=metrics,
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                chart.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Portfolio chart generated", user_id=user_id, timeframe=timeframe.value)
            return chart
            
        except Exception as e:
            self.logger.error("Failed to get portfolio chart", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate portfolio chart")
    
    async def get_signals_chart(
        self,
        user_id: str,
        timeframe: TimeFrame = TimeFrame.DAY,
        signal_type: Optional[str] = None
    ) -> SignalChart:
        """
        Get signals chart.
        
        Args:
            user_id: User identifier
            timeframe: Chart timeframe
            signal_type: Filter by signal type (optional)
            
        Returns:
            Signals chart data
        """
        try:
            cache_key = f"signals_chart:{user_id}:{timeframe.value}:{signal_type or 'all'}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Signals chart served from cache", user_id=user_id)
                return SignalChart(**cached_data)
            
            # Get date range
            end_date = datetime.utcnow()
            start_date = self._get_start_date(end_date, timeframe)
            
            # Get signals data
            signals = await self.signal_generator.get_signals_by_date_range(
                start_date=start_date,
                end_date=end_date,
                signal_type=signal_type
            )
            
            # Process signals for chart
            chart_data = self._create_signals_chart_data(signals, timeframe)
            
            # Calculate signal metrics
            metrics = self._calculate_signal_metrics(signals)
            
            chart = SignalChart(
                chart_data=chart_data,
                timeframe=timeframe.value,
                signal_type=signal_type,
                metrics=metrics,
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                chart.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Signals chart generated", user_id=user_id, timeframe=timeframe.value)
            return chart
            
        except Exception as e:
            self.logger.error("Failed to get signals chart", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate signals chart")
    
    async def get_trade_history_chart(
        self,
        user_id: str,
        timeframe: TimeFrame = TimeFrame.DAY
    ) -> TradeChart:
        """
        Get trade history chart.
        
        Args:
            user_id: User identifier
            timeframe: Chart timeframe
            
        Returns:
            Trade history chart data
        """
        try:
            cache_key = f"trades_chart:{user_id}:{timeframe.value}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Trade chart served from cache", user_id=user_id)
                return TradeChart(**cached_data)
            
            # Get date range
            end_date = datetime.utcnow()
            start_date = self._get_start_date(end_date, timeframe)
            
            # Get trade history
            trades = await self.portfolio_manager.get_trade_history(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Create chart data
            chart_data = self._create_trade_chart_data(trades, timeframe)
            
            # Calculate trade metrics
            metrics = self._calculate_trade_metrics(trades)
            
            chart = TradeChart(
                chart_data=chart_data,
                timeframe=timeframe.value,
                metrics=metrics,
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key,
                chart.dict(),
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Trade chart generated", user_id=user_id, timeframe=timeframe.value)
            return chart
            
        except Exception as e:
            self.logger.error("Failed to get trade chart", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to generate trade chart")
    
    def _get_start_date(self, end_date: datetime, timeframe: TimeFrame) -> datetime:
        """Get start date based on timeframe"""
        timeframe_deltas = {
            TimeFrame.HOUR: timedelta(hours=24),
            TimeFrame.DAY: timedelta(days=30),
            TimeFrame.WEEK: timedelta(weeks=12),
            TimeFrame.MONTH: timedelta(days=365),
            TimeFrame.QUARTER: timedelta(days=1095),  # 3 years
            TimeFrame.YEAR: timedelta(days=1825)  # 5 years
        }
        
        return end_date - timeframe_deltas.get(timeframe, timedelta(days=30))
    
    def _process_time_series_data(
        self, 
        data: List[Any], 
        timeframe: TimeFrame
    ) -> List[TimeSeriesData]:
        """Process time series data based on timeframe"""
        if not data:
            return []
        
        # Group data by timeframe
        if timeframe == TimeFrame.HOUR:
            # Group by hour
            return self._group_by_hour(data)
        elif timeframe == TimeFrame.DAY:
            # Group by day
            return self._group_by_day(data)
        elif timeframe == TimeFrame.WEEK:
            # Group by week
            return self._group_by_week(data)
        elif timeframe == TimeFrame.MONTH:
            # Group by month
            return self._group_by_month(data)
        else:
            # Default to daily grouping
            return self._group_by_day(data)
    
    def _group_by_day(self, data: List[Any]) -> List[TimeSeriesData]:
        """Group data by day"""
        grouped = {}
        
        for item in data:
            date_key = item.created_at.date()
            if date_key not in grouped:
                grouped[date_key] = []
            grouped[date_key].append(item)
        
        result = []
        for date, items in sorted(grouped.items()):
            # Calculate aggregated values for the day
            total_value = sum(item.total_value for item in items if hasattr(item, 'total_value'))
            avg_value = total_value / len(items) if items else 0
            
            result.append(TimeSeriesData(
                timestamp=datetime.combine(date, datetime.min.time()),
                value=avg_value,
                volume=len(items),
                metadata={"items_count": len(items)}
            ))
        
        return result
    
    def _group_by_hour(self, data: List[Any]) -> List[TimeSeriesData]:
        """Group data by hour"""
        grouped = {}
        
        for item in data:
            hour_key = item.created_at.replace(minute=0, second=0, microsecond=0)
            if hour_key not in grouped:
                grouped[hour_key] = []
            grouped[hour_key].append(item)
        
        result = []
        for hour, items in sorted(grouped.items()):
            total_value = sum(item.total_value for item in items if hasattr(item, 'total_value'))
            avg_value = total_value / len(items) if items else 0
            
            result.append(TimeSeriesData(
                timestamp=hour,
                value=avg_value,
                volume=len(items),
                metadata={"items_count": len(items)}
            ))
        
        return result
    
    def _group_by_week(self, data: List[Any]) -> List[TimeSeriesData]:
        """Group data by week"""
        grouped = {}
        
        for item in data:
            # Get start of week (Monday)
            week_start = item.created_at.date() - timedelta(days=item.created_at.weekday())
            if week_start not in grouped:
                grouped[week_start] = []
            grouped[week_start].append(item)
        
        result = []
        for week, items in sorted(grouped.items()):
            total_value = sum(item.total_value for item in items if hasattr(item, 'total_value'))
            avg_value = total_value / len(items) if items else 0
            
            result.append(TimeSeriesData(
                timestamp=datetime.combine(week, datetime.min.time()),
                value=avg_value,
                volume=len(items),
                metadata={"items_count": len(items)}
            ))
        
        return result
    
    def _group_by_month(self, data: List[Any]) -> List[TimeSeriesData]:
        """Group data by month"""
        grouped = {}
        
        for item in data:
            month_key = item.created_at.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if month_key not in grouped:
                grouped[month_key] = []
            grouped[month_key].append(item)
        
        result = []
        for month, items in sorted(grouped.items()):
            total_value = sum(item.total_value for item in items if hasattr(item, 'total_value'))
            avg_value = total_value / len(items) if items else 0
            
            result.append(TimeSeriesData(
                timestamp=month,
                value=avg_value,
                volume=len(items),
                metadata={"items_count": len(items)}
            ))
        
        return result
    
    def _create_portfolio_chart_data(
        self, 
        data: List[TimeSeriesData], 
        chart_type: ChartType
    ) -> ChartData:
        """Create portfolio chart data"""
        if not data:
            return ChartData(labels=[], datasets=[])
        
        labels = [item.timestamp.strftime("%Y-%m-%d %H:%M") for item in data]
        values = [item.value for item in data]
        
        if chart_type == ChartType.LINE:
            datasets = [{
                "label": "Portfolio Value",
                "data": values,
                "borderColor": "#3B82F6",
                "backgroundColor": "rgba(59, 130, 246, 0.1)",
                "fill": False
            }]
        elif chart_type == ChartType.AREA:
            datasets = [{
                "label": "Portfolio Value",
                "data": values,
                "borderColor": "#3B82F6",
                "backgroundColor": "rgba(59, 130, 246, 0.3)",
                "fill": True
            }]
        elif chart_type == ChartType.BAR:
            datasets = [{
                "label": "Portfolio Value",
                "data": values,
                "backgroundColor": "#3B82F6",
                "borderColor": "#1E40AF",
                "borderWidth": 1
            }]
        else:
            # Default to line chart
            datasets = [{
                "label": "Portfolio Value",
                "data": values,
                "borderColor": "#3B82F6",
                "backgroundColor": "rgba(59, 130, 246, 0.1)",
                "fill": False
            }]
        
        return ChartData(labels=labels, datasets=datasets)
    
    def _create_signals_chart_data(
        self, 
        signals: List[Any], 
        timeframe: TimeFrame
    ) -> ChartData:
        """Create signals chart data"""
        if not signals:
            return ChartData(labels=[], datasets=[])
        
        # Group signals by date and strength
        grouped = {}
        for signal in signals:
            date_key = signal.created_at.date()
            if date_key not in grouped:
                grouped[date_key] = {"WEAK": 0, "MODERATE": 0, "STRONG": 0, "VERY_STRONG": 0}
            grouped[date_key][signal.strength] += 1
        
        labels = [date.strftime("%Y-%m-%d") for date in sorted(grouped.keys())]
        
        datasets = [
            {
                "label": "Weak Signals",
                "data": [grouped[date]["WEAK"] for date in sorted(grouped.keys())],
                "backgroundColor": "#FEF3C7",
                "borderColor": "#F59E0B"
            },
            {
                "label": "Moderate Signals",
                "data": [grouped[date]["MODERATE"] for date in sorted(grouped.keys())],
                "backgroundColor": "#DBEAFE",
                "borderColor": "#3B82F6"
            },
            {
                "label": "Strong Signals",
                "data": [grouped[date]["STRONG"] for date in sorted(grouped.keys())],
                "backgroundColor": "#D1FAE5",
                "borderColor": "#10B981"
            },
            {
                "label": "Very Strong Signals",
                "data": [grouped[date]["VERY_STRONG"] for date in sorted(grouped.keys())],
                "backgroundColor": "#FEE2E2",
                "borderColor": "#EF4444"
            }
        ]
        
        return ChartData(labels=labels, datasets=datasets)
    
    def _create_trade_chart_data(
        self, 
        trades: List[Any], 
        timeframe: TimeFrame
    ) -> ChartData:
        """Create trade chart data"""
        if not trades:
            return ChartData(labels=[], datasets=[])
        
        # Group trades by date
        grouped = {}
        for trade in trades:
            date_key = trade.created_at.date()
            if date_key not in grouped:
                grouped[date_key] = {"profit": 0, "loss": 0, "count": 0}
            
            if trade.pnl > 0:
                grouped[date_key]["profit"] += trade.pnl
            else:
                grouped[date_key]["loss"] += abs(trade.pnl)
            grouped[date_key]["count"] += 1
        
        labels = [date.strftime("%Y-%m-%d") for date in sorted(grouped.keys())]
        
        datasets = [
            {
                "label": "Profit",
                "data": [grouped[date]["profit"] for date in sorted(grouped.keys())],
                "backgroundColor": "#10B981",
                "borderColor": "#059669"
            },
            {
                "label": "Loss",
                "data": [-grouped[date]["loss"] for date in sorted(grouped.keys())],
                "backgroundColor": "#EF4444",
                "borderColor": "#DC2626"
            }
        ]
        
        return ChartData(labels=labels, datasets=datasets)
    
    async def _calculate_chart_metrics(self, data: List[TimeSeriesData]) -> Dict[str, Any]:
        """Calculate chart metrics"""
        if not data:
            return {}
        
        values = [item.value for item in data]
        
        return {
            "min_value": min(values),
            "max_value": max(values),
            "avg_value": sum(values) / len(values),
            "total_change": values[-1] - values[0] if len(values) > 1 else 0,
            "total_change_percentage": ((values[-1] - values[0]) / values[0] * 100) if len(values) > 1 and values[0] != 0 else 0,
            "data_points": len(values)
        }
    
    def _calculate_signal_metrics(self, signals: List[Any]) -> Dict[str, Any]:
        """Calculate signal metrics"""
        if not signals:
            return {}
        
        total_signals = len(signals)
        strength_counts = {}
        
        for signal in signals:
            strength = signal.strength
            strength_counts[strength] = strength_counts.get(strength, 0) + 1
        
        return {
            "total_signals": total_signals,
            "strength_distribution": strength_counts,
            "avg_confidence": sum(signal.confidence for signal in signals) / total_signals,
            "unique_tokens": len(set(signal.token_address for signal in signals))
        }
    
    def _calculate_trade_metrics(self, trades: List[Any]) -> Dict[str, Any]:
        """Calculate trade metrics"""
        if not trades:
            return {}
        
        total_trades = len(trades)
        profitable_trades = len([t for t in trades if t.pnl > 0])
        total_pnl = sum(trade.pnl for trade in trades)
        
        return {
            "total_trades": total_trades,
            "profitable_trades": profitable_trades,
            "win_rate": (profitable_trades / total_trades * 100) if total_trades > 0 else 0,
            "total_pnl": total_pnl,
            "avg_pnl": total_pnl / total_trades if total_trades > 0 else 0,
            "best_trade": max(trade.pnl for trade in trades) if trades else 0,
            "worst_trade": min(trade.pnl for trade in trades) if trades else 0
        }
