"""
📊 Advanced Metrics Calculator

Advanced performance metrics including alpha, beta, information ratio,
Calmar ratio, Sortino ratio, and other sophisticated risk-adjusted metrics.
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from ...config.logging_config import get_logger
from ...shared.types import MarketData
from ...database.models import Portfolio, Trade, PerformanceMetric
from ...features.data_pipeline import DataAggregator

logger = get_logger(__name__)


class MetricType(str, Enum):
    """Advanced metric types"""
    ALPHA = "alpha"
    BETA = "beta"
    INFORMATION_RATIO = "information_ratio"
    CALMAR_RATIO = "calmar_ratio"
    SORTINO_RATIO = "sortino_ratio"
    TREYNOR_RATIO = "treynor_ratio"
    JENSEN_ALPHA = "jensen_alpha"
    TRACKING_ERROR = "tracking_error"
    MAXIMUM_DRAWDOWN = "maximum_drawdown"
    VALUE_AT_RISK = "value_at_risk"
    CONDITIONAL_VAR = "conditional_var"
    OMEGA_RATIO = "omega_ratio"


class BenchmarkType(str, Enum):
    """Benchmark types for comparison"""
    SOL = "SOL"
    BTC = "BTC"
    ETH = "ETH"
    MARKET_INDEX = "market_index"
    RISK_FREE = "risk_free"


class AdvancedMetricsCalculator:
    """
    📊 Advanced Metrics Calculator
    
    Calculates sophisticated performance metrics:
    - Alpha and Beta calculations
    - Information Ratio and Tracking Error
    - Calmar and Sortino Ratios
    - Value at Risk (VaR) and Conditional VaR
    - Maximum Drawdown analysis
    - Risk-adjusted return metrics
    """
    
    def __init__(self):
        self.logger = logger
        self.data_aggregator = DataAggregator()
        
        # Risk-free rate (annualized)
        self.risk_free_rate = Decimal("0.05")  # 5% annual
        
        # Benchmark data cache
        self.benchmark_cache: Dict[str, pd.DataFrame] = {}
        self.cache_ttl = 3600  # 1 hour
    
    async def calculate_all_metrics(
        self,
        portfolio_id: str,
        benchmark: BenchmarkType = BenchmarkType.SOL,
        period_days: int = 90
    ) -> Dict[str, Any]:
        """
        Calculate all advanced metrics for a portfolio
        
        Args:
            portfolio_id: Portfolio ID
            benchmark: Benchmark for comparison
            period_days: Analysis period in days
            
        Returns:
            Complete metrics analysis
        """
        try:
            self.logger.info(f"Calculating advanced metrics for portfolio {portfolio_id}")
            
            # Get portfolio performance data
            portfolio_returns = await self._get_portfolio_returns(portfolio_id, period_days)
            benchmark_returns = await self._get_benchmark_returns(benchmark, period_days)
            
            if len(portfolio_returns) < 30:
                raise ValueError("Insufficient data for metrics calculation")
            
            # Align data
            aligned_data = self._align_returns(portfolio_returns, benchmark_returns)
            portfolio_ret = aligned_data["portfolio"]
            benchmark_ret = aligned_data["benchmark"]
            
            # Calculate individual metrics
            metrics = {}
            
            # Alpha and Beta
            alpha_beta = await self._calculate_alpha_beta(portfolio_ret, benchmark_ret)
            metrics.update(alpha_beta)
            
            # Information Ratio
            metrics["information_ratio"] = await self._calculate_information_ratio(
                portfolio_ret, benchmark_ret
            )
            
            # Calmar Ratio
            metrics["calmar_ratio"] = await self._calculate_calmar_ratio(portfolio_ret)
            
            # Sortino Ratio
            metrics["sortino_ratio"] = await self._calculate_sortino_ratio(portfolio_ret)
            
            # Treynor Ratio
            metrics["treynor_ratio"] = await self._calculate_treynor_ratio(
                portfolio_ret, metrics["beta"]
            )
            
            # Jensen's Alpha
            metrics["jensen_alpha"] = await self._calculate_jensen_alpha(
                portfolio_ret, benchmark_ret, metrics["beta"]
            )
            
            # Tracking Error
            metrics["tracking_error"] = await self._calculate_tracking_error(
                portfolio_ret, benchmark_ret
            )
            
            # Maximum Drawdown
            metrics["maximum_drawdown"] = await self._calculate_maximum_drawdown(portfolio_ret)
            
            # Value at Risk
            var_metrics = await self._calculate_var_metrics(portfolio_ret)
            metrics.update(var_metrics)
            
            # Omega Ratio
            metrics["omega_ratio"] = await self._calculate_omega_ratio(portfolio_ret)
            
            # Additional risk metrics
            risk_metrics = await self._calculate_risk_metrics(portfolio_ret)
            metrics.update(risk_metrics)
            
            # Performance summary
            performance_summary = await self._calculate_performance_summary(
                portfolio_ret, benchmark_ret
            )
            
            result = {
                "portfolio_id": portfolio_id,
                "benchmark": benchmark,
                "analysis_period_days": period_days,
                "calculation_time": datetime.utcnow(),
                "metrics": metrics,
                "performance_summary": performance_summary,
                "data_points": len(portfolio_ret)
            }
            
            # Save metrics to database
            await self._save_metrics_to_db(portfolio_id, metrics)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating advanced metrics: {str(e)}")
            raise
    
    async def calculate_metric(
        self,
        portfolio_id: str,
        metric_type: MetricType,
        benchmark: BenchmarkType = BenchmarkType.SOL,
        period_days: int = 90
    ) -> Dict[str, Any]:
        """Calculate a specific metric"""
        try:
            portfolio_returns = await self._get_portfolio_returns(portfolio_id, period_days)
            benchmark_returns = await self._get_benchmark_returns(benchmark, period_days)
            
            aligned_data = self._align_returns(portfolio_returns, benchmark_returns)
            portfolio_ret = aligned_data["portfolio"]
            benchmark_ret = aligned_data["benchmark"]
            
            if metric_type == MetricType.ALPHA:
                alpha_beta = await self._calculate_alpha_beta(portfolio_ret, benchmark_ret)
                return {"metric_type": metric_type, "value": alpha_beta["alpha"]}
            
            elif metric_type == MetricType.BETA:
                alpha_beta = await self._calculate_alpha_beta(portfolio_ret, benchmark_ret)
                return {"metric_type": metric_type, "value": alpha_beta["beta"]}
            
            elif metric_type == MetricType.INFORMATION_RATIO:
                value = await self._calculate_information_ratio(portfolio_ret, benchmark_ret)
                return {"metric_type": metric_type, "value": value}
            
            elif metric_type == MetricType.CALMAR_RATIO:
                value = await self._calculate_calmar_ratio(portfolio_ret)
                return {"metric_type": metric_type, "value": value}
            
            elif metric_type == MetricType.SORTINO_RATIO:
                value = await self._calculate_sortino_ratio(portfolio_ret)
                return {"metric_type": metric_type, "value": value}
            
            elif metric_type == MetricType.MAXIMUM_DRAWDOWN:
                value = await self._calculate_maximum_drawdown(portfolio_ret)
                return {"metric_type": metric_type, "value": value}
            
            else:
                raise ValueError(f"Unsupported metric type: {metric_type}")
            
        except Exception as e:
            self.logger.error(f"Error calculating metric {metric_type}: {str(e)}")
            raise
    
    async def _calculate_alpha_beta(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series
    ) -> Dict[str, float]:
        """Calculate Alpha and Beta"""
        try:
            # Convert to excess returns (subtract risk-free rate)
            daily_rf_rate = float(self.risk_free_rate) / 365
            
            portfolio_excess = portfolio_returns - daily_rf_rate
            benchmark_excess = benchmark_returns - daily_rf_rate
            
            # Calculate beta using linear regression
            covariance = np.cov(portfolio_excess, benchmark_excess)[0, 1]
            benchmark_variance = np.var(benchmark_excess)
            
            beta = covariance / benchmark_variance if benchmark_variance != 0 else 0
            
            # Calculate alpha
            portfolio_mean = np.mean(portfolio_excess)
            benchmark_mean = np.mean(benchmark_excess)
            alpha = portfolio_mean - (beta * benchmark_mean)
            
            # Annualize alpha
            alpha_annualized = alpha * 365
            
            return {
                "alpha": float(alpha_annualized),
                "beta": float(beta),
                "r_squared": float(np.corrcoef(portfolio_excess, benchmark_excess)[0, 1] ** 2)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating alpha/beta: {str(e)}")
            return {"alpha": 0.0, "beta": 1.0, "r_squared": 0.0}
    
    async def _calculate_information_ratio(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series
    ) -> float:
        """Calculate Information Ratio"""
        try:
            # Active returns (portfolio - benchmark)
            active_returns = portfolio_returns - benchmark_returns
            
            # Information ratio = mean active return / tracking error
            mean_active_return = np.mean(active_returns)
            tracking_error = np.std(active_returns)
            
            if tracking_error == 0:
                return 0.0
            
            information_ratio = mean_active_return / tracking_error
            
            # Annualize
            return float(information_ratio * np.sqrt(365))
            
        except Exception as e:
            self.logger.error(f"Error calculating information ratio: {str(e)}")
            return 0.0
    
    async def _calculate_calmar_ratio(self, returns: pd.Series) -> float:
        """Calculate Calmar Ratio"""
        try:
            # Annualized return
            total_return = (1 + returns).prod() - 1
            annualized_return = (1 + total_return) ** (365 / len(returns)) - 1
            
            # Maximum drawdown
            max_dd = await self._calculate_maximum_drawdown(returns)
            
            if max_dd["max_drawdown"] == 0:
                return float('inf') if annualized_return > 0 else 0.0
            
            calmar_ratio = annualized_return / abs(max_dd["max_drawdown"])
            
            return float(calmar_ratio)
            
        except Exception as e:
            self.logger.error(f"Error calculating Calmar ratio: {str(e)}")
            return 0.0
    
    async def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """Calculate Sortino Ratio"""
        try:
            # Mean return
            mean_return = np.mean(returns)
            
            # Downside deviation (only negative returns)
            downside_returns = returns[returns < 0]
            downside_deviation = np.std(downside_returns) if len(downside_returns) > 0 else 0
            
            if downside_deviation == 0:
                return float('inf') if mean_return > 0 else 0.0
            
            # Risk-free rate adjustment
            daily_rf_rate = float(self.risk_free_rate) / 365
            excess_return = mean_return - daily_rf_rate
            
            sortino_ratio = excess_return / downside_deviation
            
            # Annualize
            return float(sortino_ratio * np.sqrt(365))
            
        except Exception as e:
            self.logger.error(f"Error calculating Sortino ratio: {str(e)}")
            return 0.0
    
    async def _calculate_treynor_ratio(self, returns: pd.Series, beta: float) -> float:
        """Calculate Treynor Ratio"""
        try:
            # Mean excess return
            daily_rf_rate = float(self.risk_free_rate) / 365
            mean_excess_return = np.mean(returns) - daily_rf_rate
            
            if beta == 0:
                return 0.0
            
            treynor_ratio = mean_excess_return / beta
            
            # Annualize
            return float(treynor_ratio * 365)
            
        except Exception as e:
            self.logger.error(f"Error calculating Treynor ratio: {str(e)}")
            return 0.0
    
    async def _calculate_jensen_alpha(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series,
        beta: float
    ) -> float:
        """Calculate Jensen's Alpha"""
        try:
            # Risk-free rate
            daily_rf_rate = float(self.risk_free_rate) / 365
            
            # Expected return using CAPM
            portfolio_excess = np.mean(portfolio_returns) - daily_rf_rate
            benchmark_excess = np.mean(benchmark_returns) - daily_rf_rate
            expected_excess_return = beta * benchmark_excess
            
            # Jensen's Alpha
            jensen_alpha = portfolio_excess - expected_excess_return
            
            # Annualize
            return float(jensen_alpha * 365)
            
        except Exception as e:
            self.logger.error(f"Error calculating Jensen's alpha: {str(e)}")
            return 0.0

    async def _calculate_tracking_error(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series
    ) -> float:
        """Calculate Tracking Error"""
        try:
            # Active returns
            active_returns = portfolio_returns - benchmark_returns

            # Tracking error is the standard deviation of active returns
            tracking_error = np.std(active_returns)

            # Annualize
            return float(tracking_error * np.sqrt(365))

        except Exception as e:
            self.logger.error(f"Error calculating tracking error: {str(e)}")
            return 0.0

    async def _calculate_maximum_drawdown(self, returns: pd.Series) -> Dict[str, Any]:
        """Calculate Maximum Drawdown"""
        try:
            # Calculate cumulative returns
            cumulative_returns = (1 + returns).cumprod()

            # Calculate running maximum
            running_max = cumulative_returns.expanding().max()

            # Calculate drawdown
            drawdown = (cumulative_returns - running_max) / running_max

            # Find maximum drawdown
            max_drawdown = drawdown.min()
            max_dd_date = drawdown.idxmin()

            # Find peak before max drawdown
            peak_date = running_max.loc[:max_dd_date].idxmax()

            # Calculate recovery information
            recovery_date = None
            if max_dd_date < drawdown.index[-1]:
                # Find when portfolio recovered to peak level
                recovery_mask = cumulative_returns.loc[max_dd_date:] >= running_max.loc[max_dd_date]
                if recovery_mask.any():
                    recovery_date = recovery_mask.idxmax()

            return {
                "max_drawdown": float(max_drawdown),
                "max_drawdown_date": max_dd_date,
                "peak_date": peak_date,
                "recovery_date": recovery_date,
                "drawdown_duration_days": (max_dd_date - peak_date).days if peak_date else 0,
                "recovery_duration_days": (recovery_date - max_dd_date).days if recovery_date else None
            }

        except Exception as e:
            self.logger.error(f"Error calculating maximum drawdown: {str(e)}")
            return {"max_drawdown": 0.0}

    async def _calculate_var_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate Value at Risk and Conditional VaR"""
        try:
            # Sort returns
            sorted_returns = np.sort(returns)

            # Calculate VaR at different confidence levels
            var_95 = np.percentile(sorted_returns, 5)  # 95% VaR
            var_99 = np.percentile(sorted_returns, 1)  # 99% VaR

            # Calculate Conditional VaR (Expected Shortfall)
            cvar_95 = np.mean(sorted_returns[sorted_returns <= var_95])
            cvar_99 = np.mean(sorted_returns[sorted_returns <= var_99])

            return {
                "var_95": float(var_95),
                "var_99": float(var_99),
                "cvar_95": float(cvar_95),
                "cvar_99": float(cvar_99)
            }

        except Exception as e:
            self.logger.error(f"Error calculating VaR metrics: {str(e)}")
            return {"var_95": 0.0, "var_99": 0.0, "cvar_95": 0.0, "cvar_99": 0.0}

    async def _calculate_omega_ratio(self, returns: pd.Series, threshold: float = 0.0) -> float:
        """Calculate Omega Ratio"""
        try:
            # Gains and losses relative to threshold
            gains = returns[returns > threshold] - threshold
            losses = threshold - returns[returns <= threshold]

            if len(losses) == 0 or losses.sum() == 0:
                return float('inf') if len(gains) > 0 else 1.0

            omega_ratio = gains.sum() / losses.sum()

            return float(omega_ratio)

        except Exception as e:
            self.logger.error(f"Error calculating Omega ratio: {str(e)}")
            return 1.0

    async def _calculate_risk_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate additional risk metrics"""
        try:
            # Volatility
            volatility = np.std(returns) * np.sqrt(365)  # Annualized

            # Skewness
            skewness = returns.skew()

            # Kurtosis
            kurtosis = returns.kurtosis()

            # Sharpe ratio
            daily_rf_rate = float(self.risk_free_rate) / 365
            excess_returns = returns - daily_rf_rate
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(365)

            # Upside/Downside capture
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]

            upside_capture = len(positive_returns) / len(returns) if len(returns) > 0 else 0
            downside_capture = len(negative_returns) / len(returns) if len(returns) > 0 else 0

            return {
                "volatility": float(volatility),
                "skewness": float(skewness),
                "kurtosis": float(kurtosis),
                "sharpe_ratio": float(sharpe_ratio),
                "upside_capture": float(upside_capture),
                "downside_capture": float(downside_capture)
            }

        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {str(e)}")
            return {}

    async def _calculate_performance_summary(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series
    ) -> Dict[str, Any]:
        """Calculate performance summary statistics"""
        try:
            # Total returns
            portfolio_total = (1 + portfolio_returns).prod() - 1
            benchmark_total = (1 + benchmark_returns).prod() - 1

            # Annualized returns
            days = len(portfolio_returns)
            portfolio_annualized = (1 + portfolio_total) ** (365 / days) - 1
            benchmark_annualized = (1 + benchmark_total) ** (365 / days) - 1

            # Win rate
            win_rate = (portfolio_returns > 0).mean()

            # Best/worst days
            best_day = portfolio_returns.max()
            worst_day = portfolio_returns.min()

            # Consecutive wins/losses
            consecutive_wins = self._calculate_consecutive_periods(portfolio_returns > 0)
            consecutive_losses = self._calculate_consecutive_periods(portfolio_returns < 0)

            return {
                "total_return_portfolio": float(portfolio_total),
                "total_return_benchmark": float(benchmark_total),
                "annualized_return_portfolio": float(portfolio_annualized),
                "annualized_return_benchmark": float(benchmark_annualized),
                "excess_return": float(portfolio_total - benchmark_total),
                "win_rate": float(win_rate),
                "best_day": float(best_day),
                "worst_day": float(worst_day),
                "max_consecutive_wins": consecutive_wins,
                "max_consecutive_losses": consecutive_losses,
                "analysis_days": days
            }

        except Exception as e:
            self.logger.error(f"Error calculating performance summary: {str(e)}")
            return {}

    def _calculate_consecutive_periods(self, condition_series: pd.Series) -> int:
        """Calculate maximum consecutive periods where condition is True"""
        try:
            # Find consecutive True values
            groups = (condition_series != condition_series.shift()).cumsum()
            consecutive_counts = condition_series.groupby(groups).sum()

            return int(consecutive_counts.max()) if len(consecutive_counts) > 0 else 0

        except Exception as e:
            self.logger.error(f"Error calculating consecutive periods: {str(e)}")
            return 0

    async def _get_portfolio_returns(self, portfolio_id: str, days: int) -> pd.Series:
        """Get portfolio daily returns"""
        try:
            # For now, generate synthetic returns
            # In a real implementation, this would calculate from portfolio value changes

            np.random.seed(42)
            dates = pd.date_range(
                start=datetime.utcnow() - timedelta(days=days),
                periods=days,
                freq='D'
            )

            # Generate synthetic returns with some trend
            returns = np.random.normal(0.001, 0.02, days)  # 0.1% daily return, 2% volatility

            return pd.Series(returns, index=dates)

        except Exception as e:
            self.logger.error(f"Error getting portfolio returns: {str(e)}")
            raise

    async def _get_benchmark_returns(self, benchmark: BenchmarkType, days: int) -> pd.Series:
        """Get benchmark returns"""
        try:
            # Check cache
            cache_key = f"{benchmark}_{days}"
            if cache_key in self.benchmark_cache:
                cache_time = self.benchmark_cache[cache_key].index[-1]
                if (datetime.now() - cache_time).total_seconds() < self.cache_ttl:
                    return self.benchmark_cache[cache_key]

            # Generate synthetic benchmark returns
            np.random.seed(hash(benchmark.value))
            dates = pd.date_range(
                start=datetime.utcnow() - timedelta(days=days),
                periods=days,
                freq='D'
            )

            if benchmark == BenchmarkType.SOL:
                returns = np.random.normal(0.0008, 0.025, days)  # SOL-like returns
            elif benchmark == BenchmarkType.BTC:
                returns = np.random.normal(0.0005, 0.02, days)   # BTC-like returns
            elif benchmark == BenchmarkType.ETH:
                returns = np.random.normal(0.0006, 0.022, days)  # ETH-like returns
            else:
                returns = np.random.normal(0.0003, 0.015, days)  # Market index

            benchmark_series = pd.Series(returns, index=dates)

            # Cache result
            self.benchmark_cache[cache_key] = benchmark_series

            return benchmark_series

        except Exception as e:
            self.logger.error(f"Error getting benchmark returns: {str(e)}")
            raise

    def _align_returns(
        self,
        portfolio_returns: pd.Series,
        benchmark_returns: pd.Series
    ) -> Dict[str, pd.Series]:
        """Align portfolio and benchmark returns by date"""
        try:
            # Find common dates
            common_dates = portfolio_returns.index.intersection(benchmark_returns.index)

            if len(common_dates) == 0:
                raise ValueError("No common dates between portfolio and benchmark")

            return {
                "portfolio": portfolio_returns.loc[common_dates],
                "benchmark": benchmark_returns.loc[common_dates]
            }

        except Exception as e:
            self.logger.error(f"Error aligning returns: {str(e)}")
            raise

    async def _save_metrics_to_db(self, portfolio_id: str, metrics: Dict[str, Any]):
        """Save calculated metrics to database"""
        try:
            # Create performance metric records
            for metric_name, metric_value in metrics.items():
                if isinstance(metric_value, (int, float)):
                    performance_metric = PerformanceMetric(
                        portfolio_id=portfolio_id,
                        metric_name=metric_name,
                        metric_value=Decimal(str(metric_value)),
                        calculation_date=datetime.utcnow(),
                        period_days=90,  # Default period
                        metadata={"calculation_method": "advanced_metrics_calculator"}
                    )
                    await performance_metric.save()

            self.logger.info(f"Saved {len(metrics)} metrics to database")

        except Exception as e:
            self.logger.error(f"Error saving metrics to database: {str(e)}")
