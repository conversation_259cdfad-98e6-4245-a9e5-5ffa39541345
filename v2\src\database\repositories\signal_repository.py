"""
📡 Signal Repository

Repository for signal data access operations.
"""

from typing import List, Optional, Dict, Any
from ..models.signal import Signal
from .base_repository import BaseRepository


class SignalRepository(BaseRepository):
    """Repository for signal operations"""
    
    def __init__(self):
        super().__init__(Signal)
    
    async def get_by_token_address(self, token_address: str) -> List[Signal]:
        """Get signals by token address"""
        return await self.find_many({"token_address": token_address})
    
    async def get_active_signals(self) -> List[Signal]:
        """Get active signals"""
        return await self.find_many({"is_active": True})
