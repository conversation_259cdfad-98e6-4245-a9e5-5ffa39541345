# Development Setup Guide

This guide will help you set up a local development environment for TokenTracker V2.

## 📋 Prerequisites

### Required Software

- **Python 3.11+**: [Download Python](https://www.python.org/downloads/)
- **Node.js 18+**: [Download Node.js](https://nodejs.org/)
- **Docker**: [Download Docker](https://www.docker.com/get-started)
- **Git**: [Download Git](https://git-scm.com/downloads)

### Recommended Tools

- **VS Code**: [Download VS Code](https://code.visualstudio.com/)
- **Postman**: [Download Postman](https://www.postman.com/downloads/)
- **DBeaver**: [Download DBeaver](https://dbeaver.io/download/)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/tokentracker-v2.git
cd tokentracker-v2/v2
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### 3. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# See Environment Variables section below
```

### 4. Start Development Services

```bash
# Start database and Redis with Docker
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Run database migrations
alembic upgrade head

# Start the development server
python -m uvicorn src.app:app --reload --host 0.0.0.0 --port 8000
```

### 5. Verify Installation

- **API**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Dashboard**: http://localhost:8000/dashboard

## 🔧 Detailed Setup

### Python Environment

#### Using pyenv (Recommended)

```bash
# Install pyenv
curl https://pyenv.run | bash

# Install Python 3.11
pyenv install 3.11.7
pyenv local 3.11.7

# Create virtual environment
python -m venv venv
source venv/bin/activate
```

#### Dependencies

```bash
# Core dependencies
pip install -r requirements.txt

# Development dependencies
pip install -r requirements-dev.txt

# Pre-commit hooks
pre-commit install
```

### Database Setup

#### PostgreSQL with Docker

```bash
# Start PostgreSQL
docker run -d \
  --name tokentracker-postgres \
  -e POSTGRES_DB=tokentracker \
  -e POSTGRES_USER=tokentracker \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 \
  postgres:14

# Run migrations
alembic upgrade head

# Seed test data (optional)
python scripts/seed_data.py
```

#### Local PostgreSQL Installation

```bash
# Install PostgreSQL
# On macOS:
brew install postgresql
brew services start postgresql

# On Ubuntu:
sudo apt-get install postgresql postgresql-contrib

# Create database
createdb tokentracker
```

### Redis Setup

```bash
# With Docker
docker run -d \
  --name tokentracker-redis \
  -p 6379:6379 \
  redis:6-alpine

# Or install locally
# On macOS:
brew install redis
brew services start redis

# On Ubuntu:
sudo apt-get install redis-server
```

## 🌍 Environment Variables

Create a `.env` file in the project root:

```bash
# Application
APP_NAME=TokenTracker V2
APP_VERSION=2.0.0
DEBUG=true
LOG_LEVEL=DEBUG

# Database
DATABASE_URL=postgresql://tokentracker:password@localhost:5432/tokentracker
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis
REDIS_URL=redis://localhost:6379/0
REDIS_POOL_SIZE=10

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_EXPIRATION=3600

# External APIs
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_WS_URL=wss://api.mainnet-beta.solana.com

# Telegram
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_CHAT_ID=your-telegram-chat-id

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Trading
PAPER_TRADING_ENABLED=true
INITIAL_BALANCE=10000.0
MAX_POSITION_SIZE=1000.0

# Risk Management
RISK_MANAGEMENT_ENABLED=true
MAX_DAILY_LOSS=500.0
MAX_DRAWDOWN=0.20

# Monitoring
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
GRAFANA_ENABLED=true
GRAFANA_PORT=3000

# Development
RELOAD=true
WORKERS=1
```

## 🛠️ Development Tools

### VS Code Setup

Install recommended extensions:

```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.isort",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json"
  ]
}
```

Configure settings (`.vscode/settings.json`):

```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

### Pre-commit Hooks

```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run on all files
pre-commit run --all-files
```

Pre-commit configuration (`.pre-commit-config.yaml`):

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
```

## 🧪 Testing Setup

### Running Tests

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test file
pytest tests/unit/test_signal_processing.py

# Run with verbose output
pytest -v

# Run tests in parallel
pytest -n auto
```

### Test Configuration

Create `pytest.ini`:

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --disable-warnings
    -ra
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
```

### Test Database

```bash
# Create test database
createdb tokentracker_test

# Set test environment
export DATABASE_URL=postgresql://tokentracker:password@localhost:5432/tokentracker_test
export TESTING=true

# Run migrations for test database
alembic -x testing=true upgrade head
```

## 🐳 Docker Development

### Development Compose

```yaml
# docker-compose.dev.yml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: tokentracker
      POSTGRES_USER: tokentracker
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"

  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      - DATABASE_URL=************************************************/tokentracker
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:
```

### Development Dockerfile

```dockerfile
# Dockerfile.dev
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt requirements-dev.txt ./
RUN pip install -r requirements.txt -r requirements-dev.txt

# Copy source code
COPY . .

# Expose port
EXPOSE 8000

# Start development server
CMD ["python", "-m", "uvicorn", "src.app:app", "--reload", "--host", "0.0.0.0", "--port", "8000"]
```

## 🔍 Debugging

### VS Code Debugging

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "program": "-m",
      "args": ["uvicorn", "src.app:app", "--reload"],
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Python: Tests",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["tests/"],
      "console": "integratedTerminal",
      "envFile": "${workspaceFolder}/.env"
    }
  ]
}
```

### Logging Configuration

```python
# src/config/logging.py
import logging
import structlog

def configure_logging():
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
```

## 📊 Monitoring Setup

### Prometheus

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'tokentracker'
    static_configs:
      - targets: ['localhost:8000']
```

### Grafana

```bash
# Start Grafana
docker run -d \
  --name grafana \
  -p 3000:3000 \
  grafana/grafana

# Access: http://localhost:3000
# Default login: admin/admin
```

## 🚀 Next Steps

1. **Read the Architecture Guide**: [architecture.md](./architecture.md)
2. **Review Contributing Guidelines**: [contributing.md](./contributing.md)
3. **Explore the Codebase**: Start with `src/app.py`
4. **Run the Test Suite**: Ensure everything works
5. **Make Your First Change**: Try adding a simple feature

## 🆘 Troubleshooting

### Common Issues

1. **Port already in use**:
   ```bash
   lsof -ti:8000 | xargs kill -9
   ```

2. **Database connection error**:
   ```bash
   docker-compose -f docker-compose.dev.yml restart postgres
   ```

3. **Module not found**:
   ```bash
   pip install -e .
   ```

4. **Permission denied**:
   ```bash
   chmod +x scripts/*.sh
   ```

### Getting Help

- **Documentation**: [docs/](../README.md)
- **Issues**: [GitHub Issues](https://github.com/your-org/tokentracker-v2/issues)
- **Discord**: [Development Channel](https://discord.gg/tokentracker)

---

You're now ready to start developing with TokenTracker V2! 🚀
