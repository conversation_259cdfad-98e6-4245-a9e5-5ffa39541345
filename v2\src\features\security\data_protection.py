"""
🔒 Data Protection Manager

Comprehensive data protection with encryption, data masking,
secure storage, and privacy compliance features.
"""

import asyncio
import hashlib
import secrets
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from ...config.logging_config import get_logger
from ...config.settings import get_settings

logger = get_logger(__name__)
settings = get_settings()


class DataClassification(str, Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


class EncryptionType(str, Enum):
    """Encryption types"""
    AES_256 = "aes_256"
    FERNET = "fernet"
    HASH_SHA256 = "hash_sha256"
    HASH_BCRYPT = "hash_bcrypt"


class EncryptionManager:
    """
    🔐 Encryption Manager
    
    Handles data encryption, decryption, and key management
    """
    
    def __init__(self):
        self.logger = logger
        
        # Initialize encryption keys
        self.master_key = self._get_or_create_master_key()
        self.fernet = Fernet(self.master_key)
        
        # Key rotation settings
        self.key_rotation_days = 90
        self.key_versions: Dict[str, bytes] = {}
    
    def _get_or_create_master_key(self) -> bytes:
        """Get or create master encryption key"""
        try:
            # In production, retrieve from secure key management service
            master_key_b64 = getattr(settings, 'encryption_master_key', None)
            
            if master_key_b64:
                return base64.urlsafe_b64decode(master_key_b64)
            else:
                # Generate new key
                key = Fernet.generate_key()
                self.logger.warning("Generated new master encryption key - store securely!")
                return key
                
        except Exception as e:
            self.logger.error(f"Error getting master key: {str(e)}")
            # Fallback to generated key
            return Fernet.generate_key()
    
    def encrypt_data(self, data: Union[str, bytes], classification: DataClassification = DataClassification.CONFIDENTIAL) -> Dict[str, Any]:
        """
        Encrypt data based on classification level
        
        Args:
            data: Data to encrypt
            classification: Data classification level
            
        Returns:
            Encrypted data with metadata
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Choose encryption method based on classification
            if classification in [DataClassification.RESTRICTED, DataClassification.CONFIDENTIAL]:
                encrypted_data = self.fernet.encrypt(data)
                encryption_type = EncryptionType.FERNET
            else:
                # For less sensitive data, use simpler encoding
                encrypted_data = base64.b64encode(data)
                encryption_type = EncryptionType.AES_256
            
            return {
                "encrypted_data": base64.b64encode(encrypted_data).decode('utf-8'),
                "encryption_type": encryption_type.value,
                "classification": classification.value,
                "encrypted_at": datetime.utcnow(),
                "key_version": "v1"  # For key rotation
            }
            
        except Exception as e:
            self.logger.error(f"Error encrypting data: {str(e)}")
            raise
    
    def decrypt_data(self, encrypted_data_info: Dict[str, Any]) -> Union[str, bytes]:
        """
        Decrypt data using stored metadata
        
        Args:
            encrypted_data_info: Encrypted data with metadata
            
        Returns:
            Decrypted data
        """
        try:
            encrypted_data = base64.b64decode(encrypted_data_info["encrypted_data"])
            encryption_type = encrypted_data_info["encryption_type"]
            
            if encryption_type == EncryptionType.FERNET.value:
                decrypted_data = self.fernet.decrypt(encrypted_data)
            elif encryption_type == EncryptionType.AES_256.value:
                decrypted_data = base64.b64decode(encrypted_data)
            else:
                raise ValueError(f"Unsupported encryption type: {encryption_type}")
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Error decrypting data: {str(e)}")
            raise
    
    def hash_data(self, data: str, algorithm: str = "sha256") -> str:
        """
        Hash data for one-way encryption
        
        Args:
            data: Data to hash
            algorithm: Hashing algorithm
            
        Returns:
            Hashed data
        """
        try:
            if algorithm == "sha256":
                return hashlib.sha256(data.encode('utf-8')).hexdigest()
            elif algorithm == "sha512":
                return hashlib.sha512(data.encode('utf-8')).hexdigest()
            else:
                raise ValueError(f"Unsupported hashing algorithm: {algorithm}")
                
        except Exception as e:
            self.logger.error(f"Error hashing data: {str(e)}")
            raise
    
    def generate_salt(self, length: int = 32) -> str:
        """Generate cryptographic salt"""
        return secrets.token_hex(length)
    
    def derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password"""
        try:
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            return base64.urlsafe_b64encode(kdf.derive(password.encode()))
            
        except Exception as e:
            self.logger.error(f"Error deriving key: {str(e)}")
            raise


class DataProtectionManager:
    """
    🔒 Data Protection Manager
    
    Comprehensive data protection including:
    - Data encryption and decryption
    - Data masking and anonymization
    - Secure data storage
    - Data retention policies
    - Privacy compliance
    """
    
    def __init__(self):
        self.logger = logger
        self.encryption_manager = EncryptionManager()
        
        # Data retention policies (in days)
        self.retention_policies = {
            DataClassification.PUBLIC: 365 * 7,      # 7 years
            DataClassification.INTERNAL: 365 * 5,    # 5 years
            DataClassification.CONFIDENTIAL: 365 * 3, # 3 years
            DataClassification.RESTRICTED: 365 * 1   # 1 year
        }
        
        # PII fields that need special handling
        self.pii_fields = {
            "email", "phone", "address", "ssn", "passport",
            "credit_card", "bank_account", "ip_address"
        }
    
    async def protect_sensitive_data(
        self,
        data: Dict[str, Any],
        classification: DataClassification = DataClassification.CONFIDENTIAL
    ) -> Dict[str, Any]:
        """
        Protect sensitive data with appropriate security measures
        
        Args:
            data: Data to protect
            classification: Data classification level
            
        Returns:
            Protected data
        """
        try:
            protected_data = {}
            
            for key, value in data.items():
                if self._is_sensitive_field(key):
                    # Encrypt sensitive fields
                    if value:
                        encrypted_info = self.encryption_manager.encrypt_data(str(value), classification)
                        protected_data[f"{key}_encrypted"] = encrypted_info
                        
                        # Store masked version for display
                        protected_data[key] = self.mask_data(str(value), key)
                    else:
                        protected_data[key] = value
                else:
                    # Non-sensitive data
                    protected_data[key] = value
            
            # Add protection metadata
            protected_data["_protection_info"] = {
                "classification": classification.value,
                "protected_at": datetime.utcnow(),
                "protection_version": "v1"
            }
            
            return protected_data
            
        except Exception as e:
            self.logger.error(f"Error protecting sensitive data: {str(e)}")
            raise
    
    async def unprotect_sensitive_data(
        self,
        protected_data: Dict[str, Any],
        user_permissions: List[str] = None
    ) -> Dict[str, Any]:
        """
        Unprotect sensitive data based on user permissions
        
        Args:
            protected_data: Protected data
            user_permissions: User permissions
            
        Returns:
            Unprotected data (based on permissions)
        """
        try:
            unprotected_data = {}
            user_permissions = user_permissions or []
            
            for key, value in protected_data.items():
                if key.endswith("_encrypted"):
                    # Check if user has permission to decrypt
                    original_key = key.replace("_encrypted", "")
                    
                    if "decrypt_pii" in user_permissions or "admin" in user_permissions:
                        # Decrypt the data
                        decrypted_value = self.encryption_manager.decrypt_data(value)
                        unprotected_data[original_key] = decrypted_value
                    else:
                        # Return masked version
                        unprotected_data[original_key] = protected_data.get(original_key, "[PROTECTED]")
                
                elif not key.startswith("_"):
                    unprotected_data[key] = value
            
            return unprotected_data
            
        except Exception as e:
            self.logger.error(f"Error unprotecting sensitive data: {str(e)}")
            raise
    
    def mask_data(self, data: str, field_type: str) -> str:
        """
        Mask sensitive data for display
        
        Args:
            data: Data to mask
            field_type: Type of field
            
        Returns:
            Masked data
        """
        try:
            if not data:
                return data
            
            if field_type == "email":
                # Mask email: j***@example.com
                parts = data.split("@")
                if len(parts) == 2:
                    username = parts[0]
                    domain = parts[1]
                    masked_username = username[0] + "*" * (len(username) - 1) if len(username) > 1 else "*"
                    return f"{masked_username}@{domain}"
            
            elif field_type == "phone":
                # Mask phone: ***-***-1234
                if len(data) >= 4:
                    return "*" * (len(data) - 4) + data[-4:]
            
            elif field_type in ["credit_card", "bank_account"]:
                # Mask card/account: ****-****-****-1234
                if len(data) >= 4:
                    return "*" * (len(data) - 4) + data[-4:]
            
            elif field_type == "ssn":
                # Mask SSN: ***-**-1234
                if len(data) >= 4:
                    return "*" * (len(data) - 4) + data[-4:]
            
            elif field_type == "ip_address":
                # Mask IP: 192.168.*.*
                parts = data.split(".")
                if len(parts) == 4:
                    return f"{parts[0]}.{parts[1]}.*.*"
            
            else:
                # Generic masking: show first and last character
                if len(data) > 2:
                    return data[0] + "*" * (len(data) - 2) + data[-1]
                else:
                    return "*" * len(data)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error masking data: {str(e)}")
            return "[MASKED]"
    
    def anonymize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Anonymize data by removing or hashing PII
        
        Args:
            data: Data to anonymize
            
        Returns:
            Anonymized data
        """
        try:
            anonymized_data = {}
            
            for key, value in data.items():
                if self._is_sensitive_field(key):
                    if value:
                        # Hash PII fields
                        anonymized_data[f"{key}_hash"] = self.encryption_manager.hash_data(str(value))
                    # Don't include original PII
                else:
                    anonymized_data[key] = value
            
            # Add anonymization metadata
            anonymized_data["_anonymized_at"] = datetime.utcnow()
            
            return anonymized_data
            
        except Exception as e:
            self.logger.error(f"Error anonymizing data: {str(e)}")
            raise
    
    async def check_data_retention(self, data_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check data retention policies and identify records for deletion
        
        Args:
            data_records: List of data records to check
            
        Returns:
            Retention check results
        """
        try:
            results = {
                "total_records": len(data_records),
                "records_to_delete": [],
                "records_to_archive": [],
                "retention_compliant": 0
            }
            
            current_time = datetime.utcnow()
            
            for record in data_records:
                protection_info = record.get("_protection_info", {})
                classification = DataClassification(protection_info.get("classification", "confidential"))
                protected_at = protection_info.get("protected_at")
                
                if protected_at:
                    age_days = (current_time - protected_at).days
                    retention_days = self.retention_policies[classification]
                    
                    if age_days > retention_days:
                        results["records_to_delete"].append({
                            "record_id": record.get("id"),
                            "age_days": age_days,
                            "retention_days": retention_days,
                            "classification": classification.value
                        })
                    elif age_days > retention_days * 0.8:  # 80% of retention period
                        results["records_to_archive"].append({
                            "record_id": record.get("id"),
                            "age_days": age_days,
                            "retention_days": retention_days,
                            "classification": classification.value
                        })
                    else:
                        results["retention_compliant"] += 1
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error checking data retention: {str(e)}")
            return {"error": str(e)}
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """Check if field contains sensitive data"""
        field_lower = field_name.lower()
        return any(pii_field in field_lower for pii_field in self.pii_fields)
    
    async def secure_delete(self, data_id: str) -> bool:
        """
        Securely delete data (overwrite multiple times)
        
        Args:
            data_id: ID of data to delete
            
        Returns:
            Success status
        """
        try:
            # In a real implementation, this would:
            # 1. Overwrite the data multiple times with random data
            # 2. Remove from all backups
            # 3. Clear from caches
            # 4. Log the deletion for audit
            
            self.logger.info(f"Securely deleted data: {data_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error securely deleting data: {str(e)}")
            return False
