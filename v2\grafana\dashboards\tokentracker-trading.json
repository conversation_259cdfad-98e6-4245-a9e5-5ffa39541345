{"dashboard": {"id": null, "title": "TokenTracker V2 - Trading Metrics", "tags": ["tokentracker", "trading"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-24h", "to": "now"}, "panels": [{"id": 1, "title": "Trading Success Rate", "type": "stat", "targets": [{"expr": "trading_success_rate", "legendFormat": "Success Rate"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 50}, {"color": "green", "value": 70}]}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0}}, {"id": 2, "title": "Total Trades", "type": "stat", "targets": [{"expr": "sum(trading_total_trades)", "legendFormat": "Total Trades"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0}}, {"id": 3, "title": "Trading Volume (USD)", "type": "stat", "targets": [{"expr": "sum(trading_volume_usd_total)", "legendFormat": "Total Volume"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0}}, {"id": 4, "title": "Trades by Status", "type": "piechart", "targets": [{"expr": "sum by (status) (trading_total_trades)", "legendFormat": "{{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 5, "title": "Portfolio Performance", "type": "timeseries", "targets": [{"expr": "portfolio_pnl_percent", "legendFormat": "Portfolio {{portfolio_id}}"}], "fieldConfig": {"defaults": {"unit": "percent"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 6, "title": "Signal Generation Rate", "type": "timeseries", "targets": [{"expr": "rate(signals_generated_total[5m])", "legendFormat": "{{signal_type}} ({{strength}})"}], "fieldConfig": {"defaults": {"unit": "sps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 7, "title": "Signal Accuracy", "type": "timeseries", "targets": [{"expr": "signals_accuracy_ratio", "legendFormat": "{{signal_type}}"}], "fieldConfig": {"defaults": {"unit": "percentunit", "min": 0, "max": 1}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 8, "title": "Token Analysis Rate", "type": "timeseries", "targets": [{"expr": "rate(tokens_analyzed_total[5m])", "legendFormat": "Tokens/sec"}], "fieldConfig": {"defaults": {"unit": "tps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 9, "title": "Token Analysis Duration", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.95, rate(token_analysis_duration_seconds_bucket[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(token_analysis_duration_seconds_bucket[5m]))", "legendFormat": "50th percentile"}], "fieldConfig": {"defaults": {"unit": "s"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}]}}